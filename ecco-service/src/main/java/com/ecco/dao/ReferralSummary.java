package com.ecco.dao;

import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.dom.Referral;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.dom.contacts.Contact;
import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.querydsl.core.annotations.QueryProjection;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.defaultString;

/**
 * A view of a referral for use with JPQL SELECT NEW
 */
// TODO extends ServiceRecipientSummary (it already has the properties, just SRS is currently inaccessible)
// Actually, the property displayName is missing - but it gets added for the client side in ReferralSummaryToViewModel
@Getter
@RequiredArgsConstructor
public class ReferralSummary extends ServiceRecipientSummary implements ServiceRecipientCaseStatusView {


//    public final boolean isSummary = true;
    public boolean _readOnly = false;
    public final DateTime requestedDelete;
    public final Long referralId;
    public final String referralCode;
    public final Long primaryReferralId;
    public final Integer primaryRelationshipId;
    public final Long parentReferralId;
    public final Long clientId;
    public final String clientCode;
    public final Integer srcGeographicAreaId;
    public final String firstName, lastName, clientDisplayName;
    public final UUID dataProtectionSignedId;
    public final DateTime dataProtectionAgreementDate;
    public final Boolean dataProtectionAgreementStatus;
    public final UUID consentSignedId;
    public final DateTime consentAgreementDate;
    public final Boolean consentAgreementStatus;
    public final UUID agreement1SignedId;
    public final DateTime agreement1AgreementDate;
    public final Boolean agreement1AgreementStatus;
    public final UUID agreement2SignedId;
    public final DateTime agreement2AgreementDate;
    public final Boolean agreement2AgreementStatus;
    public final UUID agreement3SignedId;
    public final DateTime agreement3AgreementDate;
    public final Boolean agreement3AgreementStatus;
    public final UUID agreement4SignedId;
    public final DateTime agreement4AgreementDate;
    public final Boolean agreement4AgreementStatus;
    public final UUID agreement5SignedId;
    public final DateTime agreement5AgreementDate;
    public final Boolean agreement5AgreementStatus;
    public final UUID agreement6SignedId;
    public final DateTime agreement6AgreementDate;
    public final Boolean agreement6AgreementStatus;
    public final UUID agreement7SignedId;
    public final DateTime agreement7AgreementDate;
    public final Boolean agreement7AgreementStatus;
    public final UUID agreement8SignedId;
    public final DateTime agreement8AgreementDate;
    public final Boolean agreement8AgreementStatus;
    public final UUID agreement9SignedId;
    public final DateTime agreement9AgreementDate;
    public final Boolean agreement9AgreementStatus;
    public final UUID agreement10SignedId;
    public final DateTime agreement10AgreementDate;
    public final Boolean agreement10AgreementStatus;
    public final Long signpostedCommentId;
    public final Integer signpostedReasonId;
    public final boolean signpostedBack;
    public final Long signpostedAgencyId;
    public final DateTime firstResponseMadeOn, firstOfferedInterviewDate;
    public final Integer interviewDna;
    public final String interviewDnaComments;
    public final String interviewSetupComments;

    public final DateTime decisionReferralMadeOn, decisionDate, decisionMadeOn;

    /** Exists in database with a time of midnight, so should be converted to being a date */
    public final LocalDate receivedDate, receivingServiceDate;

    /** Exists in database with a time of midnight, so should be converted to being a date */
    public final LocalDate exitedDate;
    public final boolean finalDecision, acceptedOnService;

    /** The components of 'source' of the referral; either the name of the agency that
     * initiated the referral, or "self referral". */
    public final boolean selfReferral;
    public final Long referrerAgencyId;
    public final Long referrerIndividualId;
    public final String referralReason;

    //public final String deliveredByName;
    public final boolean acceptedReferral;
    public final Long interviewer1ContactId;
    public final Long interviewer2ContactId;
    public final String interviewLocation;
    public final Long supportWorkerId;
    public final Integer exitReasonId;
    public int daysAttending;
    private final Integer pendingStatusId;
    public final Integer latestClientStatusId;
    public final DateTime latestClientStatusDateTime;
    public final LocalDate nextDueSlaDate;
    public final Long nextDueSlaTaskId;
    public final boolean acceptedFunding;
    public final Long fundingSourceId;
    public final BigDecimal fundingHoursOfSupport;
    public final DateTime decisionFundingDate;
    public final Integer waitingListScore;

    // lazy populated due to the virtual impossibility of being able to use Hib/JPA/MySQL,Oracle to do it without custom SQL
    public String agencyName;
    public Integer parentServiceRecipientId;

    // TODO ? deliveredByName; pending; hidden
    @QueryProjection
    public ReferralSummary(
            DateTime requestedDelete, Long referralId, String referralCode, Integer serviceRecipientId, Long parentReferralId,
            Long primaryReferralId, Integer primaryRelationshipId,
            Long clientId, String clientCode,
            Integer serviceAllocationId,
            Long serviceTypeId,
            Long serviceIdAcl, // for permissions
            Long projectIdAcl, // for permissions
            String firstName, String lastName,
            Long contactId, String calendarId,
            Integer pendingStatusId,
            DateTime firstResponseMadeOn, DateTime firstOfferedInterviewDate,
            Integer interviewDna,
            String interviewDnaComments,
            String interviewSetupComments,
            Long interviewer1ContactId,
            Long interviewer2ContactId,
            String interviewLocation,
            DateTime decisionReferralMadeOn,
            DateTime decisionDate,
            DateTime decisionMadeOn,
            DateTime receivedDate, DateTime receivingServiceDate, DateTime exitedDate,
            boolean selfReferral, Long referrerAgencyId, Long referrerIndividualId,
            String referralReason,
            Integer srcGeographicAreaId,
            Long signpostedCommentId,
            boolean finalDecision, boolean acceptedOnService, boolean acceptedReferral,
            UUID dataProtectionSignatureId,
            DateTime dataProtectionAgreementDate,
            Boolean dataProtectionAgreementStatus,
            UUID consentSignatureId,
            DateTime consentAgreementDate,
            Boolean consentAgreementStatus,
            UUID agreement1SignatureId,
            DateTime agreement1AgreementDate,
            Boolean agreement1AgreementStatus,
            UUID agreement2SignatureId,
            DateTime agreement2AgreementDate,
            Boolean agreement2AgreementStatus,
            UUID agreement3SignatureId,
            DateTime agreement3AgreementDate,
            Boolean agreement3AgreementStatus,
            UUID agreement4SignatureId,
            DateTime agreement4AgreementDate,
            Boolean agreement4AgreementStatus,
            UUID agreement5SignatureId,
            DateTime agreement5AgreementDate,
            Boolean agreement5AgreementStatus,
            UUID agreement6SignatureId,
            DateTime agreement6AgreementDate,
            Boolean agreement6AgreementStatus,
            UUID agreement7SignatureId,
            DateTime agreement7AgreementDate,
            Boolean agreement7AgreementStatus,
            UUID agreement8SignatureId,
            DateTime agreement8AgreementDate,
            Boolean agreement8AgreementStatus,
            UUID agreement9SignatureId,
            DateTime agreement9AgreementDate,
            Boolean agreement9AgreementStatus,
            UUID agreement10SignatureId,
            DateTime agreement10AgreementDate,
            Boolean agreement10AgreementStatus,
            Integer signpostedReasonId, boolean signpostedBack, Long signpostedAgencyId, Long supportWorkerId,
            Integer exitReasonId, DaysOfWeek daysAttending,
            LocalDate nextDueSlaDate,
            Long nextDueSlaTaskId,
            Integer latestClientStatusId,
            DateTime latestClientStatusDateTime,
            boolean acceptedFunding, Long fundingSourceId, BigDecimal fundingHoursOfSupport, DateTime decisionFundingDate,
            Integer waitingListScore,
            Integer currentTaskIndex,
            Long currentTaskDefId,
            Contact contact
            ) {
        this.requestedDelete = requestedDelete;
        this.referralId = referralId;
        this.referralCode = referralCode;
        this.serviceRecipientId = serviceRecipientId;
        this.parentReferralId = parentReferralId;
        this.primaryReferralId = primaryReferralId;
        this.primaryRelationshipId = primaryRelationshipId;
        this.clientId = clientId;
        this.clientCode = clientCode;
        this.serviceAllocationId = serviceAllocationId;
        this.serviceTypeId = serviceTypeId;
        this.serviceIdAcl = serviceIdAcl;
        this.projectIdAcl = projectIdAcl;
        this.firstName = firstName;
        this.lastName = lastName;
        this.contactId = contactId;
        this.calendarId = calendarId;
        this.pendingStatusId = pendingStatusId;
        this.firstResponseMadeOn = firstResponseMadeOn;
        this.firstOfferedInterviewDate = firstOfferedInterviewDate;
        this.interviewDna = interviewDna;
        this.interviewer1ContactId = interviewer1ContactId;
        this.decisionReferralMadeOn = decisionReferralMadeOn;
        this.decisionDate = decisionDate;
        this.decisionMadeOn = decisionMadeOn;
        this.receivedDate = receivedDate == null ? null : receivedDate.toLocalDate();
        this.receivingServiceDate = receivingServiceDate == null ? null : receivingServiceDate.toLocalDate(); // start
        this.exitedDate = exitedDate == null ? null : exitedDate.toLocalDate();
        this.finalDecision = finalDecision;
        this.acceptedOnService = acceptedOnService;
        this.acceptedReferral = acceptedReferral;
        this.srcGeographicAreaId = srcGeographicAreaId;
        this.statusMessageKey = Support.getStatusMessageKey(this);
        this.clientDisplayName = defaultString(firstName, "") + " " + defaultString(lastName, "");
        this.signpostedCommentId = signpostedCommentId;
        this.signpostedAgencyId = signpostedAgencyId;
        this.signpostedBack = signpostedBack;
        this.signpostedReasonId = signpostedReasonId;
        this.dataProtectionSignedId = dataProtectionSignatureId;
        this.dataProtectionAgreementDate = dataProtectionAgreementDate;
        this.dataProtectionAgreementStatus = dataProtectionAgreementStatus;
        this.consentSignedId = consentSignatureId;
        this.consentAgreementDate = consentAgreementDate;
        this.consentAgreementStatus = consentAgreementStatus;
        this.agreement1SignedId = agreement1SignatureId;
        this.agreement1AgreementDate = agreement1AgreementDate;
        this.agreement1AgreementStatus = agreement1AgreementStatus;
        this.agreement2SignedId = agreement2SignatureId;
        this.agreement2AgreementDate = agreement2AgreementDate;
        this.agreement2AgreementStatus = agreement2AgreementStatus;
        this.agreement3SignedId = agreement3SignatureId;
        this.agreement3AgreementDate = agreement3AgreementDate;
        this.agreement3AgreementStatus = agreement3AgreementStatus;
        this.agreement4SignedId = agreement4SignatureId;
        this.agreement4AgreementDate = agreement4AgreementDate;
        this.agreement4AgreementStatus = agreement4AgreementStatus;
        this.agreement5SignedId = agreement5SignatureId;
        this.agreement5AgreementDate = agreement5AgreementDate;
        this.agreement5AgreementStatus = agreement5AgreementStatus;
        this.agreement6SignedId = agreement6SignatureId;
        this.agreement6AgreementDate = agreement6AgreementDate;
        this.agreement6AgreementStatus = agreement6AgreementStatus;
        this.agreement7SignedId = agreement7SignatureId;
        this.agreement7AgreementDate = agreement7AgreementDate;
        this.agreement7AgreementStatus = agreement7AgreementStatus;
        this.agreement8SignedId = agreement8SignatureId;
        this.agreement8AgreementDate = agreement8AgreementDate;
        this.agreement8AgreementStatus = agreement8AgreementStatus;
        this.agreement9SignedId = agreement9SignatureId;
        this.agreement9AgreementDate = agreement9AgreementDate;
        this.agreement9AgreementStatus = agreement9AgreementStatus;
        this.agreement10SignedId = agreement10SignatureId;
        this.agreement10AgreementDate = agreement10AgreementDate;
        this.agreement10AgreementStatus = agreement10AgreementStatus;
        this.supportWorkerId = supportWorkerId;
        this.exitReasonId = exitReasonId;
        this.selfReferral = selfReferral;
        this.referrerAgencyId = referrerAgencyId;
        this.referrerIndividualId = referrerIndividualId;
        this.daysAttending = Referral.daysAttending(daysAttending);
        this.nextDueSlaDate = nextDueSlaDate;
        this.nextDueSlaTaskId = nextDueSlaTaskId;
        this.acceptedFunding = acceptedFunding;
        this.fundingSourceId = fundingSourceId;
        this.fundingHoursOfSupport = fundingHoursOfSupport;
        this.decisionFundingDate = decisionFundingDate;
        this.waitingListScore = waitingListScore;
        this.currentTaskIndex = currentTaskIndex;
        this.currentTaskDefId = currentTaskDefId;
        this.interviewDnaComments = interviewDnaComments;
        this.interviewSetupComments = interviewSetupComments;
        this.interviewer2ContactId = interviewer2ContactId;
        this.interviewLocation = interviewLocation;
        this.referralReason = referralReason;
        this.latestClientStatusId = latestClientStatusId;
        this.latestClientStatusDateTime = latestClientStatusDateTime;
        // NB this gets applied to the RotaAppointmentViewModel.location, for the ActivityView.tsx activity.getLocation()
        this.address = contact.getAddress();

        this.displayName = this.clientDisplayName;
        this.prefix = ReferralServiceRecipient.PREFIX;
        this.discriminator = ReferralServiceRecipient.DISCRIMINATOR;
        //this.textMap = textMap;
        this.parentId = referralId;
        this.addressCommaSep = address != null ? address.toCommaSepString() : null;
    }

    public DateTime getExited() {
        return exitedDate == null ? null : exitedDate.toDateTimeAtStartOfDay(); // TODO: ripple LocalDate through all code
    }

    @Override
    public boolean isAcceptedOnService() {
        return acceptedOnService;
    }

    @Override
    public Boolean isAcceptedReferral() {
        return acceptedReferral;
    }

    /**
     * finalDecision is a maintained property, but silently ignored since its derivable from decisionDate != null
     */
    @Override
    public boolean isFinalDecision() {
        return decisionMadeOn != null;
    }

    @Override
    public Boolean isReferralDecision() {
        return decisionReferralMadeOn != null;
    }

    @Override
    public LocalDate getDecisionReferralMadeOn() {
        return DateTimeUtils.convertFromUtcToUsersLocalDate(decisionReferralMadeOn);
    }

    @Override
    public LocalDate getDecisionMadeOn() {
        return DateTimeUtils.convertFromUtcToUsersLocalDate(decisionMadeOn);
    }

    @Override
    public DateTime getDecisionMadeOnDT() {
        return decisionMadeOn;
    }

    @Override
    public boolean isPending() {
        return pendingStatusId != null;
    }

    @Override
    public boolean isRequestedDelete() {
        return this.requestedDelete != null;
    }

    @Override
    public Integer getServiceRecipientId() {
        return serviceRecipientId;
    }

    @Override
    public LocalDate getExitedDate() {
        return exitedDate;
    }

    public DaysOfWeek getMeetingDays() {
        return DaysOfWeek.fromBits(daysAttending);
    }

    @Override
    public LocalDate getReceivingServiceLocalDate() {
        return receivingServiceDate;
    }
}
