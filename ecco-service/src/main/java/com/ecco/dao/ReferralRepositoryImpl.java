package com.ecco.dao;

import com.ecco.dom.Agency;
import com.ecco.dom.QReferral;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.infrastructure.hibernate.HibFilterTemplate;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.stream.Collectors;

import static com.ecco.dao.ReferralRepository.parent;
import static com.ecco.dom.QReferral.referral;
import static java.util.stream.StreamSupport.stream;

public class ReferralRepositoryImpl implements ReferralRepositoryCustom  {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ReferralRepository referralRepository;

    @Autowired
    private AgencyRepository agencyRepository;

    @Autowired
    private EntityRestrictionService entityRestrictionService;

    @Autowired
    RepositoryBasedServiceCategorisationService svcCatsService;

    @Override
    public ReferralSummary findOneReferralSummary(long referralId) {
        // NOTE: Removed @NotFound(IGNORE) from fields that gained an incorrect cross join (instead of left join or just get the bloomin id!) in Hib 5.4.  See https://hibernate.atlassian.net/browse/HHH-14368
        ReferralSummary result = referralRepository.findOneWithProjection(ReferralRepository.REFERRAL_SUMMARY_PROJECTION,
                referral.id.eq(referralId));
        return populateAdditionalEntities(result);
    }

    /**
     * Called by ReferralController referrals/byServiceRecipient/{serviceRecipientId}/summaryAsReferral
     * Called by ServiceRecipientBaseController (which has no endpoints itself) /service-recipient/{serviceRecipientId}
     *  in an attempt to find find the sr as a referral, then try others
     *  but that method returns a ServiceRecipientSummary, not a Referral
     */
    @Override
    public ReferralSummary findOneReferralSummaryByServiceRecipientId(int serviceRecipientId) {
        ReferralSummary result = referralRepository.findOneWithProjection(ReferralRepository.REFERRAL_SUMMARY_PROJECTION,
                referral.serviceRecipient.id.eq(serviceRecipientId)
                );
        return populateAdditionalEntities(result);
    }

    @Override
    public List<ReferralSummary> findAllIncludingHiddenAsReferralSummary(Predicate p) {
        return createHibFilterTemplate().executeUnfiltered( () -> this.findAllAsReferralSummary(p));
    }

    @Override
    public Page<ClientDetailWrapper> findAllClientsFromReferralsPredicate(Predicate p, PageRequest pr) {
        Page<ClientDetailWrapper> result = referralRepository.findAllWithProjection(ReferralRepository.CLIENT_PROJECTION,
                p, pr);
        return result;
    }

    @Override
    public List<ReferralSummary> findAllAsReferralSummary(Predicate p) {
        List<ReferralSummary> result = referralRepository.findAllWithProjection(ReferralRepository.REFERRAL_SUMMARY_PROJECTION,
                p)
                .stream().sorted(Comparator.comparing(ReferralSummary::getReferralId)).toList();
        populateAgenciesBatched(result);
        result.forEach(this::populateParentServiceRecipientId);
        return result;
    }

    @Override
    public Page<ReferralSummary> findAllAsReferralSummary(Predicate p, PageRequest pr) {
        Page<ReferralSummary> result = referralRepository.findAllWithProjection(ReferralRepository.REFERRAL_SUMMARY_PROJECTION,
        p, pr);
        populateAgenciesBatched(result.getContent());
        result.forEach(this::populateParentServiceRecipientId);
        return result;
    }

    @Override
    public List<ReferralSummary> findAllReferralSummaryByClient(long clientId) {
        List<ReferralSummary> result = referralRepository.findAllWithProjection(ReferralRepository.REFERRAL_SUMMARY_PROJECTION,
                QReferral.referral.client.id.eq(clientId))
                .stream().sorted(Comparator.comparing(ReferralSummary::getReferralId)).toList();
        populateAgenciesBatched(result);

        // load the users restrictions - which loads services and projects
        ServicesProjectsDto restrictions = EntityRestrictionService.getRestrictedServicesProjectsDto(entityRestrictionService, svcCatsService);

        // See also GroupSupportActivityController.findAllUnsecuredAclReferralSummaryByServiceRecipients
        result.forEach(r -> {
            r._readOnly = !restrictions.canAccess(r.serviceIdAcl, r.projectIdAcl);
            populateParentServiceRecipientId(r);
        });
       return result;
    }

    @Override
    public List<Tuple> findAllServiceRecipientIdByParentReferral_Ids(Long[] ids) {
        List<Tuple> result = referralRepository.findAllWithProjection(ReferralRepository.PARENT_CHILD_SRID_PROJECTION,
                referral.parentReferral.id.in(ids), q -> q.join(referral.parentReferral, parent));
        return result;
    }

    private void populateAgenciesBatched(List<ReferralSummary> input) {
        Set<Long> ids = input.stream().map(r -> r.referrerAgencyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Iterable<Agency> agencies = agencyRepository.findAllById(ids);

        Map<Long, Agency> map = stream(agencies.spliterator(), false)
                .collect(Collectors.toMap(Agency::getId, a -> a));

        input.forEach( r -> {
            if (r.referrerAgencyId != null) {
                r.agencyName = map.get(r.referrerAgencyId).getCompanyName();
            }
        });
    }

    HibFilterTemplate createHibFilterTemplate() { // Allow tests to override with a mock.
        return new HibFilterTemplate(entityManager);
    }

    private ReferralSummary populateAdditionalEntities(ReferralSummary input) {
        return populateAgency(populateParentServiceRecipientId(input));
    }
    private ReferralSummary populateAgency(ReferralSummary input) {
        if (input == null) {
            return null;
        }
        if (input.referrerAgencyId != null) {
            input.agencyName = agencyRepository.findById(input.referrerAgencyId).orElseThrow().getCompanyName();
        }
        return input;
    }

    /**
     * Load the parent serviceRecipientId is available.
     * This avoids the projection, since it causes an inner join. The repository
     * approach offers little help to control the query to get a left join specified.
     * See http://stackoverflow.com/questions/21637636/spring-data-jpa-with-querydslpredicateexecutor-and-joining-into-a-collection
     */
    public static ReferralSummary populateParentServiceRecipientId(ReferralSummary input, ReferralRepository referralRepository) {
        if (input == null) {
            return null;
        }
        if (input.parentReferralId != null) {
            input.parentServiceRecipientId = referralRepository.getServiceRecipientId(input.parentReferralId);
        }
        return input;
    }
    private ReferralSummary populateParentServiceRecipientId(ReferralSummary input) {
        return ReferralRepositoryImpl.populateParentServiceRecipientId(input, referralRepository);
    }

}

