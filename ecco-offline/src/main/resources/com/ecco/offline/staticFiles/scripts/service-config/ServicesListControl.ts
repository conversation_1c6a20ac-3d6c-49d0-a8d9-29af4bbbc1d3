import $ = require("jquery");

import BaseAsyncListControl = require("../controls/BaseAsyncListControl");
import BaseListEntryControl = require("../controls/BaseListEntryControl");
import EditServiceForm = require("./EditServiceForm");
import Service = dto.ServiceDto;
import {apiClient} from "ecco-components";
import {ServiceAjaxRepository} from "ecco-dto";
import * as dto from "ecco-dto/service-config-dto";

let repository = new ServiceAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<Service> {

    constructor(service: Service) {
        super(service, "fa fa-pencil");
    }

    protected administerEntry(): void {
        EditServiceForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        const disabled = this.entry.disabled ? "disabled" : "";
        const $ids = $("<small>").text(`[id:${this.entry.id}, service-type:${this.entry.serviceTypeId}] ${disabled} `);
        const $name = $("<span>").text(this.entry.name);
        const $row = $("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-8").append($name))
                .append($("<div>").addClass("col-xs-4").append($ids))
        return $("<span>").append($row);
    }

    protected getEntryIconClass(): string {
        return "fa fa-home";
    }
}

// admin menu 'services'
class ServicesListControl extends BaseAsyncListControl<Service> {

    constructor() {
        super("add new service", "no services defined", "fa fa-home");
    }

    protected fetchViewData(): Promise<Service[]> {
        return repository.findAllServices()
            .then (services => services.getDtos() );
    }
    protected createItemControl(service: Service) {
        return new EntryControl(service);
    }

    protected addNewEntity() {
        EditServiceForm.showInModal(null);
    };

}
export = ServicesListControl;
