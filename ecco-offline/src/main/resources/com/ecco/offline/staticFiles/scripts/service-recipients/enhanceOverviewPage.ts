import $ = require("jquery");
import ActionButton = require("../controls/ActionButton");
import ServiceRecipientInvoicesControl = require("../service-recipients/ServiceRecipientInvoicesControl");
import ServiceRecipientCreateInvoiceForm = require("../service-recipients/ServiceRecipientCreateInvoiceForm");

class EnhanceOverviewPage {

    //noinspection JSUnusedLocalSymbols - Suppressed as we use singleton to instantiate
    /** Singleton which kicks off attach() once only */
    private static instance = new EnhanceOverviewPage();

    constructor() {
        $( () => {
            this.attach();
        });
    }

    /** Find the different items we support and attach the appropriate component to each of them */
    private attach() {
        $(".create-invoices-button").each( (index, element) => {
            this.enhanceCreateInvoicesButton($(element));
        });
        $(".invoices-control").each( (index, element) => {
            this.enhanceInvoicesControl($(element));
        });
    }

    private enhanceInvoicesControl($el: $.JQuery) {
        var serviceRecipientId = parseInt($el.attr("data-service-recipient-id"));
        var editable = !!$el.attr("data-service-recipient-id");

        let invoiceControl = new ServiceRecipientInvoicesControl(serviceRecipientId, editable);
        invoiceControl.attach($el);

        // from evidence/enhanceOverviewPage.ts
        // function onCompleted() {
        //     if (onDoneUrl) {
        //         window.location.href = onDoneUrl;
        //     }
        // }
        // EvidenceDelegatingForm.loadAndAttach($el, $footer, planAllocatedId, hactAllocatedId, switcherAllocatedId,
        //         serviceRecipientId, taskName,
        //     () => {}, onCompleted, evidenceGroupName);
    }

    private enhanceCreateInvoicesButton($el: $.JQuery) {
        var serviceRecipientId = parseInt($el.attr("data-service-recipient-id"));

        var button = new ActionButton("create invoice")
            .addClass("button btn btn-link")
            .autoDisable(false)
            .clickSynchronous( () => {
                ServiceRecipientCreateInvoiceForm.showInModalByIds(serviceRecipientId);
            });
        $el.empty().append(button.element());
    }

}
