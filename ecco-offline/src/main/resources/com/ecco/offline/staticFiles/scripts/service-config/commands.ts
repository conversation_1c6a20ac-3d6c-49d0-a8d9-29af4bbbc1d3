import _ = require("lodash");
import taskSettingTypes = require("./taskSettingTypes");
import TaskSettingType = taskSettingTypes.TaskSettingType;
import * as cmdDtos from "ecco-dto/command-dto";
import * as commands from "ecco-commands";
import {assertNotNull, BaseUpdateCommandTransitioning} from "ecco-commands";
import * as dto from "ecco-dto/service-config-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {Mergeable, OutcomeDto} from "ecco-dto";


/**
 * Command to change the service type itself, as opposed to
 * a command extending common ServiceTypeCommand properties
 */
export interface ServiceTypeChangeCommandDto extends cmdDtos.UpdateCommandDto {
    id: number;
    nameChange: cmdDtos.StringChangeOptional;
}

export class ServiceTypeChangeCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private serviceTypeId: number) {
        super("service-config/servicetype/");
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null; // because we should never be called
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): ServiceTypeChangeCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    id: this.serviceTypeId,
                    operation: this.operation,
                    nameChange: this.nameChange
        });
    }
}

export type OutcomeType = "NEEDS" | "THREAT";

export interface OutcomeDefChangeDto extends cmdDtos.UpdateCommandDto {
    outcomeDefUuid: string;
    nameChange: cmdDtos.StringChangeOptional;
    /** Required when operation is "add" */
    outcomeType?: string;
}

/**
 * Command to add, remove or update
 */
export class OutcomeDefChangeCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;

    /** operation should be either "update" or "delete" */
    constructor(
            private operation: string,
            private outcomeDefUuid: string,
            /** Required when operation is add */
            private outcomeType?: OutcomeType
    ) {
        super("config/outcomeDef/");
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): OutcomeDefChangeDto {
        return({
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            outcomeDefUuid: this.outcomeDefUuid,
            outcomeType: this.outcomeType,
            operation: this.operation,
            nameChange: this.nameChange
        });
    }
}

export interface ActionGroupDefChangeDto extends cmdDtos.UpdateCommandDto {
    outcomeDefUuid: string;
    actionGroupDefUuid: string;
    nameChange: cmdDtos.StringChangeOptional;
    orderByChange: cmdDtos.NumberChangeOptional;
    disabledChange: cmdDtos.BooleanChange;
}

/**
 * Command to add, remove or update
 */
export class ActionGroupDefChangeCommand extends commands.BaseUpdateCommand {

    public static discriminator = "actionDef";

    public commandName = ActionGroupDefChangeCommand.discriminator;
    private nameChange: cmdDtos.StringChangeOptional;
    private orderByChange: cmdDtos.NumberChangeOptional;
    private disabledChange: cmdDtos.BooleanChange;

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private outcomeDefUuid: string, private actionGroupDefUuid: string) {
        super("config/actionGroupDef/");
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }
    /** Add order change data, but only if to != from */
    public changeOrderBy(from: number, to: number) {
        this.orderByChange = this.asNumberChange(from, to);
        return this;
    }


    /** Add order change data, but only if to != from */
    public changeDisabled(from: boolean, to: boolean) {
        this.disabledChange = this.asBooleanChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null || this.orderByChange != null || this.disabledChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public hasOrderByChange() {
        return this.orderByChange != null;
    }

    public getOrderByChange() {
        return this.orderByChange;
    }

    public toDto(): ActionGroupDefChangeDto {
        return({
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            outcomeDefUuid: this.outcomeDefUuid,
            actionGroupDefUuid: this.actionGroupDefUuid,
            operation: this.operation,
            nameChange: this.nameChange,
            orderByChange: this.orderByChange,
            disabledChange: this.disabledChange
        });
    }
}

export interface ActionDefChangeDto extends cmdDtos.UpdateCommandDto {
    actionDefUuid: string;
    actionGroupDefUuid: string;
    nameChange: cmdDtos.StringChangeOptional;
    orderByChange: cmdDtos.NumberChangeOptional;
    disabledChange: cmdDtos.BooleanChange;
}

/**
 * Command to add, remove or update
 */
export class ActionDefChangeCommand extends commands.BaseUpdateCommand {

    public static discriminator = "actionDef";

    public commandName = ActionDefChangeCommand.discriminator;
    private nameChange: cmdDtos.StringChangeOptional;
    private orderByChange: cmdDtos.NumberChangeOptional;
    private disabledChange: cmdDtos.BooleanChange;

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private actionDefUuid: string, private actionGroupDefUuid: string, private orderBy?: number) {
        super("config/actionDef/");
        if (orderBy) {
            this.changeOrderBy(null, orderBy)
        }
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** Add order change data, but only if to != from */
    public changeOrderBy(from: number | null, to: number) {
        this.orderByChange = this.asNumberChange(from, to);
        return this;
    }

    /** Add order change data, but only if to != from */
    public changeDisabled(from: boolean, to: boolean) {
        this.disabledChange = this.asBooleanChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null || this.orderByChange != null || this.disabledChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public getOrderByChange(): cmdDtos.NumberChangeOptional {
        return this.orderByChange;
    }

    public toDto(): ActionDefChangeDto {
        return({
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            actionDefUuid: this.actionDefUuid,
            actionGroupDefUuid: this.actionGroupDefUuid,
            operation: this.operation,
            nameChange: this.nameChange,
            orderByChange: this.orderByChange,
            disabledChange: this.disabledChange
        });
    }
}

/**
 * Command to add or remove an assocation between a goal on a referral and an activity type.
 */
export class ActionDefActivityAssociationChangeCommand extends commands.BaseUpdateCommand {
    /** operation should be either "CREATE", "UPDATE" or "DELETE" */
    constructor(private operation: string, private serviceId: number, private actionDefId: number, private activityTypeId: number) {
        super(`service-config/${serviceId}/actionDefs/${actionDefId}/linkedActivities/`);
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof ActionDefActivityAssociationChangeCommand)) {
            return false;
        }

        var otherCommand = <ActionDefActivityAssociationChangeCommand>candidate;

        return this.getCommandTargetUri() == otherCommand.getCommandTargetUri()
            && this.activityTypeId == otherCommand.activityTypeId
            && (this.operation == "add" && otherCommand.operation == "remove"
                || this.operation == "remove" && otherCommand.operation == "add");
    }

    public override merge(previousCommand: this) {
        // If we can merge - then the result of a merge is that they cancel each other out
        return null;
    }

    public toDto(): dto.ActionDefActivityAssociationChangeDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                operation: this.operation,
                actionDefId: this.actionDefId,
                activityTypeId: this.activityTypeId
        });
    }
}


export class StringChangeCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;
    private userComment?: string;


    /** operation should be "add", "update" or "remove" */
    constructor(private operation: string, private uri: string) {
        super(uri);
    }

    /** Rename this action def, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    public withWorkComment(userComment?: string) {
        this.userComment = _.isEmpty(userComment) ? undefined : userComment;
        return this;
    }


    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return this.nameChange != null || this.userComment != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): dto.StringChangeCommandDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                userComment: this.userComment,
                nameChange: this.nameChange
        });
    }

    public static createActionDefNameChange(actionDefId: number) {
        return new StringChangeCommand("update", `service-config/actionDefs/byId/${actionDefId}/`);
    }

    public static createActionGroupNameChange(actionGroupId: number) {
        return new StringChangeCommand("update", `service-config/actionGroups/${actionGroupId}/`);
    }

    public static createOutcomeNameChange(outcomeId: number) {
        return new StringChangeCommand("update", `service-config/outcomes/${outcomeId}/`);
    }

}


/** see TaskDefinitionEntrySettingCommandViewModel.java */
interface ServiceConfigTaskEntrySettingChangeDto extends cmdDtos.UpdateCommandDto {
    serviceTypeId: number;
    taskName: string;
    settingName: string;
    valueChange: cmdDtos.StringChangeOptional;
}

/**
 * Command to add, change or remove a setting on a service config task
 */
export class ServiceConfigTaskEntrySettingChangeCommand extends commands.BaseUpdateCommand {
    constructor(private settingType: TaskSettingType, private serviceTypeId: number, private taskName: string,
                private settingName: string, private fromValue: string, private toValue: string) {
        super(`service-config/${serviceTypeId}/task/${taskName}/setting/${settingName}/`);
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof ServiceConfigTaskEntrySettingChangeCommand)) {
            return false;
        }

        var that = <ServiceConfigTaskEntrySettingChangeCommand>candidate;

        // can merge if they cancel out
        return this.getCommandTargetUri() == that.getCommandTargetUri()
            && this.serviceTypeId == that.serviceTypeId
            && this.taskName == that.taskName
            && this.settingName == that.settingName
            && this.settingType.isEqual(this.fromValue, that.toValue)
            && this.settingType.isEqual(this.toValue, that.fromValue);
    }

    public override merge(previousCommand: this) {
        // If we can merge - then the result of a merge is that they cancel each other out
        return null;
    }

    public toDto(): ServiceConfigTaskEntrySettingChangeDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                serviceTypeId: this.serviceTypeId,
                taskName: this.taskName,
                settingName: this.settingName,
                valueChange: {from: this.fromValue, to: this.toValue}
        });
    }
}


/** see TaskDefinitionEntryCommandViewModel.java */
interface ServiceConfigTaskEntryChangeDto extends cmdDtos.UpdateCommandDto {
    operation: string;
    serviceTypeId: number;
    taskName: string;
    orderbyChange: cmdDtos.NumberChangeOptional;
    allowNextChange: cmdDtos.BooleanChange;
    dueDateScheduleChange: cmdDtos.StringChangeOptional;
}

/**
 * Command to add, change or remove a task definition entry
 */
export class ServiceConfigTaskEntryChangeCommand extends commands.BaseUpdateCommand {

    private orderbyChange: cmdDtos.NumberChangeOptional;
    private allowNextChange: cmdDtos.BooleanChange;
    private dueDateScheduleChange: cmdDtos.StringChangeOptional;

    /** operation should be "add", "update" or "remove" */
    constructor(private operation: string, private serviceTypeId: number, private taskName: string) {
        super(`service-config/${serviceTypeId}/task/${taskName}/`);
    }

    /** Add change data, but only if to != from */
    public changeOrderby(fromVal: number, toVal: number) {
        this.orderbyChange = this.asNumberChange(fromVal, toVal);
        return this;
    }

    /** Add change data, but only if to != from */
    public changeAllowNext(fromVal: boolean, toVal: boolean) {
        this.allowNextChange = this.asBooleanChange(fromVal, toVal);
        return this;
    }

    public changeTaskDueDateSchedule(fromVal: string, toVal: string) {
        this.dueDateScheduleChange = this.asStringChange(fromVal, toVal);
        return this;
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof ServiceConfigTaskEntryChangeCommand)) {
            return false;
        }

        const that = <ServiceConfigTaskEntryChangeCommand>candidate;

        // can merge if they cancel out
        return this.getCommandTargetUri() == that.getCommandTargetUri()
            && this.serviceTypeId == that.serviceTypeId
            && this.taskName == that.taskName
            && this.canMergeNumberChange(this.orderbyChange, that.orderbyChange)
            && this.canMergeBooleanChange(this.allowNextChange, that.allowNextChange)
            && this.canMergeStringChange(this.dueDateScheduleChange, that.dueDateScheduleChange);
            /*
            && (this.operation == "add" && otherCommand.operation == "remove"
                || this.operation == "remove" && otherCommand.operation == "add");
            */
    }

    public override hasChanges(): boolean {
        return this.hasOrderbyChange()
            || this.allowNextChange != null
            || this.dueDateScheduleChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public hasOrderbyChange() {
        return this.orderbyChange != null;
    }

    public getOrderbyChange() {
        return this.orderbyChange;
    }

    public override merge(previousCommand: this) {
        // If we can merge - then the result of a merge is that they cancel each other out
        return null;
    }

    public isDelete() {
        return this.operation == "remove";
    }

    public getTaskName() {
        return this.taskName;
    }

    public toDto(): ServiceConfigTaskEntryChangeDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                operation: this.operation,
                serviceTypeId: this.serviceTypeId,
                taskName: this.taskName,
                orderbyChange: this.orderbyChange,
                allowNextChange: this.allowNextChange,
                dueDateScheduleChange: this.dueDateScheduleChange
        });
    }
}


export interface ServiceCommandDto extends cmdDtos.UpdateCommandDto {
    serviceId: number;

    serviceTypeId: number;

    name: cmdDtos.StringChangeOptional;

    projectsToAdd?: number[] | null;
    projectsToRemove?: number[] | null;

    //deleted?: cmdDtos.BooleanChange; // see buildings/commands
}

export class ServiceCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;
    private projectsToAdd = new Array<number>();
    private projectsToRemove = new Array<number>();

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private serviceId: number, private serviceTypeId: number) {
        super("config/service/");
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** Add projects */
    public setProjectsToAdd(ids: number[]) {
        this.projectsToAdd = ids;
        return this;
    }

    /** Remove projects */
    public setProjectsToRemove(ids: number[]) {
        this.projectsToRemove = ids;
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null
            || this.operation == "add" || this.operation == "remove"
            || this.projectsToAdd.length > 0 || this.projectsToRemove.length > 0;
    }

    public toDto(): ServiceCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    serviceId: this.serviceId,
                    serviceTypeId: this.serviceTypeId,
                    operation: this.operation,
                    name: this.nameChange,
                    projectsToAdd: this.projectsToAdd.length == 0 ? null : this.projectsToAdd,
                    projectsToRemove: this.projectsToRemove.length == 0 ? null : this.projectsToRemove
        });
    }
}

/** matches ListCommmandViewModel.java */
export interface ListChangeDto extends cmdDtos.UpdateCommandDto {
    id: number;
    nameChange: cmdDtos.StringChangeOptional;
    servicesToAdd: number[] | null;
    servicesToRemove: number[] | null;
}

class ListChangeCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;
    protected servicesToAdd = new Array<number>();
    protected servicesToRemove = new Array<number>();

    /** operation should be either "update" or "delete" */
    constructor(uri: string, private operation: string, private entityId: number) {
        super(uri);
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null
            || this.operation == "add" || this.operation == "remove"
            || this.servicesToAdd.length > 0 || this.servicesToRemove.length > 0;
    }

    public toDto(): ListChangeDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    id: this.entityId,
                    operation: this.operation,
                    nameChange: this.nameChange,
                    servicesToAdd: this.servicesToAdd.length == 0 ? null : this.servicesToAdd,
                    servicesToRemove: this.servicesToRemove.length == 0 ? null : this.servicesToRemove
        });
    }
}

export interface ProjectCommandDto extends cmdDtos.UpdateCommandDto {
    projectId: number;
    changeName: cmdDtos.StringChangeOptional;
    ldapGroupsToAdd: string[] | null;
    ldapGroupsToRemove: string[] | null;
}

/**
 * Command to add, remove or update
 */
export class ProjectCommand extends commands.BaseUpdateCommand {

    private changeName: cmdDtos.StringChangeOptional;
    private ldapGroupsToAdd = new Array<string>();
    private ldapGroupsToRemove = new Array<string>();

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private projectId: number) {
        super("config/project/");
    }

    public setLdapGroupsToAdd(names: string[]) {
        this.ldapGroupsToAdd = names;
        return this;
    }

    public setLdapGroupsToRemove(names: string[]) {
        this.ldapGroupsToRemove = names;
        return this;
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public nameChange(from: string, to: string) {
        this.changeName = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null
            || this.operation == "add" || this.operation == "remove"
            || this.ldapGroupsToAdd.length > 0 || this.ldapGroupsToRemove.length > 0;
    }

    public toDto(): ProjectCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    projectId: this.projectId,
                    operation: this.operation,
                    changeName: this.changeName,
                    ldapGroupsToAdd: this.ldapGroupsToAdd.length == 0 ? null : this.ldapGroupsToAdd,
                    ldapGroupsToRemove: this.ldapGroupsToRemove.length == 0 ? null : this.ldapGroupsToRemove
        });
    }
}

export class ListWithServicesChangeCommand extends ListChangeCommand {

    public addService(serviceId: number) {
        var idxInRemoves = this.servicesToRemove.indexOf(serviceId);
        if (idxInRemoves >= 0) {
            this.servicesToRemove.splice(idxInRemoves,1);
        }
        else {
            this.servicesToAdd.push(serviceId);
        }
    }

    public removeService(serviceId: number) {
        var idxInAdds = this.servicesToAdd.indexOf(serviceId);
        if (idxInAdds >= 0) {
            this.servicesToAdd.splice(idxInAdds,1);
        }
        else {
            this.servicesToRemove.push(serviceId);
        }
    }
}


export interface QuestionChangeDto extends cmdDtos.UpdateCommandDto {
    id: number;
    questionGroupId: number;
    nameChange: cmdDtos.StringChangeOptional;
    typeChange: cmdDtos.StringChangeOptional;
    listNameChange: cmdDtos.StringChangeOptional;
    orderByChange: cmdDtos.NumberChangeOptional;
    disableChange: cmdDtos.BooleanChange;
}

/**
 * Command to add, remove or update
 */
export class QuestionChangeCommand extends commands.BaseUpdateCommand {

    public static discriminator = "questionDef";

    private nameChange: cmdDtos.StringChangeOptional;
    private typeChange: cmdDtos.StringChangeOptional;
    private listNameChange: cmdDtos.StringChangeOptional;
    private orderByChange: cmdDtos.NumberChangeOptional;
    private disableChange: cmdDtos.BooleanChange;
    public commandName = QuestionChangeCommand.discriminator;

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private questionGroupId: number, private questionId: number, private orderBy?: number) {
        super("config/questionnaire/question/");
        if (orderBy) {
            this.changeOrderBy(null, orderBy);
        }
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** Add name change data, but only if to != from */
    public changeType(from: string, to: string) {
        this.typeChange = this.asStringChange(from, to);
        return this;
    }

    /** Add name change data, but only if to != from */
    public changeListName(from: string, to: string) {
        this.listNameChange = this.asStringChange(from, to);
        return this;
    }

    /** Add order change data, but only if to != from */
    public changeOrderBy(from: number | null, to: number) {
        this.orderByChange = this.asNumberChange(from, to);
        return this;
    }

    public changeDisabled(from: boolean, to: boolean) {
        this.disableChange = this.asBooleanChange(from, to);
        return this;
    }

    public getOrderByChange(): cmdDtos.NumberChangeOptional {
        return this.orderByChange;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null || this.typeChange != null || this.listNameChange != null || this.orderByChange != null || this.disableChange != null
        || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): QuestionChangeDto {
        return({
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            id: this.questionId,
            questionGroupId: this.questionGroupId,
            operation: this.operation,
            nameChange: this.nameChange,
            listNameChange: this.listNameChange,
            orderByChange: this.orderByChange,
            typeChange: this.typeChange,
            disableChange: this.disableChange
        });
    }
}


export interface QuestionChoiceChangeDto extends cmdDtos.UpdateCommandDto {
    id: number;
    questionId: number;
    nameChange: cmdDtos.StringChangeOptional;
    valueChange: cmdDtos.StringChangeOptional;
    disableChange: cmdDtos.BooleanChange;
}

/**
 * Command to add, remove or update
 */
export class QuestionChoiceChangeCommand extends commands.BaseUpdateCommand {

    public static discriminator = "questionChoiceDef";

    private nameChange: cmdDtos.StringChangeOptional;
    private valueChange: cmdDtos.StringChangeOptional;
    private orderByChange: cmdDtos.NumberChangeOptional;
    private disableChange: cmdDtos.BooleanChange;
    public commandName = QuestionChoiceChangeCommand.discriminator;

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private questionId: number, private questionChoiceId: number, orderBy?: number) {
        super("config/questionnaire/questionChoice/");
        if (orderBy) {
            this.changeOrderBy(null, orderBy)
        }
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    public changeValue(from: string, to: string) {
        this.valueChange = this.asStringChange(from, to);
        return this;
    }

    /** Add order change data, but only if to != from */
    public changeOrderBy(from: number | null, to: number) {
        this.orderByChange = this.asNumberChange(from, to);
        return this;
    }

    public changeDisabled(from: boolean, to: boolean) {
        this.disableChange = this.asBooleanChange(from, to);
        return this;
    }

    public getOrderByChange(): cmdDtos.NumberChangeOptional {
        return this.orderByChange;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null || this.valueChange != null || this.orderByChange != null
            || this.disableChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): QuestionChoiceChangeDto {
        return({
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            id: this.questionChoiceId,
            questionId: this.questionId,
            operation: this.operation,
            nameChange: this.nameChange,
            // TODO orderBy??
            valueChange: this.valueChange,
            disableChange: this.disableChange
        });
    }
}


export interface QuestionGroupChangeDto extends cmdDtos.UpdateCommandDto {
    /** id is null on first change that creates a question group */
    id?: number;
    nameChange: cmdDtos.StringChangeOptional;
}

/**
 * Command to add, remove or update
 */
export class QuestionGroupChangeCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private questionGroupId?: number) {
        super("config/questionnaire/questionGroup/");
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): QuestionGroupChangeDto {
        return({
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            id: this.questionGroupId,
            operation: this.operation,
            nameChange: this.nameChange
        });
    }
}


export interface FlagCommandDto extends cmdDtos.UpdateCommandDto {
    id: number;
    nameChange: cmdDtos.StringChangeOptional;
}

/**
 * Command to add, remove or update
 */
export class FlagCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;

    /** operation should be either "update" or "delete" */
    constructor(private operation: string, private flagId: number) {
        super("config/flag/");
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): FlagCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    id: this.flagId,
                    operation: this.operation,
                    nameChange: this.nameChange
            });
    }
}

// for legacy audits - now a list def command
export interface CommentTypeCommandDto extends cmdDtos.UpdateCommandDto {
    id: number;
    serviceTypeIdChange: cmdDtos.NumberChangeOptional;
    nameChange: cmdDtos.StringChangeOptional;
    disabledChange: cmdDtos.BooleanChange;
}

export interface OutcomeCloneCommandDto extends cmdDtos.UpdateCommandDto {
    outcomeViewModel: OutcomeDto;
}

/**
 * Command to add, remove or update
 */
export class OutcomeCloneCommand extends commands.BaseUpdateCommand {

    constructor(private outcomeViewModel: OutcomeDto) {
        super("config/outcome/clone/");
    }

    public override canMerge(candidate: commands.BaseUpdateCommand) {
        return false;
    }

    public override merge(previousCommand: this): this {
        throw null;
    }

    public toDto(): OutcomeCloneCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    outcomeViewModel: this.outcomeViewModel
            });
    }
}


export interface FormDefChangeDto extends cmdDtos.UpdateCommandDto {
    operation: string;

    formDefUuid: string;

    name: cmdDtos.StringChangeOptional;

    orderby: cmdDtos.NumberChangeOptional;

    disabled: cmdDtos.BooleanChange;

    definition: cmdDtos.StringChangeOptional;
}

/**
 * Base Command to add, remove or update an address
 */
export class FormDefChangeCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "formDef";

    private nameChange: cmdDtos.StringChangeOptional;

    private orderbyChange: cmdDtos.NumberChangeOptional;

    private definitionChange: cmdDtos.StringChangeOptional;

    private disabledChange: cmdDtos.BooleanChange;

    constructor(private operation: "add" | "update",
                commandUuid: Uuid,
                private formDefUuid: Uuid) {
        super(`formDef/`, commandUuid, FormDefChangeCommand.discriminator);
        assertNotNull(formDefUuid, "FormDefChangeCommand.uuid");
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    public changeDefinition(from: string, to: string) {
        this.definitionChange = this.asStringChange(from, to);
        return this;
    }

    public changeOrderby(from: number, to: number) {
        this.orderbyChange = this.asNumberChange(from, to);
        return this;
    }

    public changeDisabled(from: boolean, to: boolean) {
        this.disabledChange = this.asBooleanChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.nameChange != null || this.definitionChange != null || this.orderbyChange != null
            || this.operation == "add";
    }

    public getCommandDto(): FormDefChangeDto {
        return {
            commandName: FormDefChangeCommand.discriminator, // must match ORM discriminator at server end
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            operation: this.operation,
            formDefUuid: this.formDefUuid.toString(),
            orderby: this.orderbyChange,
            name: this.nameChange,
            definition: this.definitionChange,
            disabled: this.disabledChange
        };
    }
}
