{"type": "object", "id": "./$schema", "title": "Example Form", "description": "Some descriptive info to show above the form", "$schema": "http://json-schema.org/draft-03/schema#", "properties": {"personsName": {"type": "string", "id": "order:01_name", "required": true, "maxLength": 60, "minLength": 1, "title": "Name"}, "emailAddress": {"type": "string", "id": "order:02_emailAddress", "required": true, "maxLength": 60, "minLength": 1, "description": "Please enter an email address", "title": "Email address", "pattern": "^[A-Za-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[A-Za-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[A-Za-z0-9](?:[A-Za-z0-9-]*[A-Za-z0-9])?\\.)+[A-Za-z0-9](?:[A-Za-z0-9-]*[A-Za-z0-9])?$"}, "telephoneNumber": {"type": "number", "id": "order:03_telephoneNumber", "required": true, "maxLength": 60, "minLength": 1, "title": "Telephone number"}, "officeNumber": {"type": "string", "id": "order:04_officeNumber", "required": true, "description": "", "title": "Office number", "pattern": "^[a-zA-Z0-9]{5}$"}, "officeName": {"type": "string", "id": "order:05_officeName", "required": true, "maxLength": 60, "minLength": 1, "title": "Office name"}, "amount": {"type": "string", "id": "order:07_amount", "required": true, "maxLength": 50, "minLength": 1, "description": "", "title": "Amount", "pattern": "^[1-9.][0-9.]*$"}, "currency": {"fieldName": "currency", "type": "string", "id": "order:08_currency", "required": true, "title": "Please select the currency you are using to pay", "enum": ["GBP", "USD", "EUR"]}, "comments": {"type": "array", "id": "order:09_comments", "required": false, "description": "", "title": "Comments", "items": {"type": "string", "format": "textarea"}}, "declaration": {"type": "boolean", "id": "order:10_declaration", "description": "I accept all responsibility.", "title": "Confirmation of consent", "required": true}}}