import ActionButton = require("../controls/ActionButton");
import commands = require("./commands");
import Element = require("../controls/Element");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextInput = require("../controls/TextInput");
import Action = serviceConfigDomain.Action;
import ActionGroup = serviceConfigDomain.ActionGroup;
import Outcome = serviceConfigDomain.Outcome;
import {CommandQueue} from "ecco-commands";
import DialogContent from "../controls/DialogContent";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import * as serviceConfigDomain from "ecco-dto";
import {ActionsChangedCallback} from "@eccosolutions/ecco-common";
import {getCommandQueueRepository, showErrorAsAlert} from "ecco-offline-data";


class EditNameForm implements Element, DialogContent {

    private submitButton: ActionButton;
    private form = new Form();
    private name = new TextInput("name");
    private commandQueue = new CommandQueue(getCommandQueueRepository());

    /** callback should be called when the Form has successfully saved it's content and the dialog can close.
     *  This is usually in response to a "done" button being clicked in the footer, for example */
    private onFinished: () => void;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private changeCommand: commands.StringChangeCommand,
            private title: string, private initialValue: string,
            private onSubmit: (commandQueue: CommandQueue) => Promise<void>) {

        this.submitButton = new ActionButton("done")
                .addClass("btn btn-primary")
                .clickSynchronous( () => this.submitForm() )
                .disable();

        this.name.setVal(initialValue);
        this.form.append( new InputGroup("name", this.name) );

        this.submitButton.enable(); // probably want to have this be linked to length of commandQueue (based on event?)
    }

    public submitForm() {
        this.changeCommand
            .changeName(this.initialValue, this.name.val());

        if (this.changeCommand.hasChanges()) {
            this.commandQueue.addCommand(this.changeCommand);
        }
        this.onSubmit( this.commandQueue )
            .then(() => this.onFinished())
            .catch(showErrorAsAlert);
    }

    public getTitle() {
        return this.title;
    }

    registerActionsChangeListener(updateActions: ActionsChangedCallback) {
        // NO-OP
    }

    public element() {
        return this.form.element();
    }

    public getFooter() {
        return this.submitButton.element();
    }

    public setOnFinished(onFinished: () => void) {
        this.onFinished = onFinished;
    }

    public static editActionDefInModal(action: Action) {
        var cmd = commands.StringChangeCommand.createActionDefNameChange(action.getId());
        var form = new EditNameForm(cmd, "System Admin: Edit goal name", action.getName(),
            (commandQueue: CommandQueue) => commandQueue.flushCommands() );
        showFormInModalDom(form);
    }

    public static editActionGroupInModal(actionGroup: ActionGroup) {
        var cmd = commands.StringChangeCommand.createActionGroupNameChange(actionGroup.getId());
        var form = new EditNameForm(cmd, "System Admin: Edit evidence group name", actionGroup.getName(),
            (commandQueue: CommandQueue) => commandQueue.flushCommands() );
        showFormInModalDom(form);
    }

    public static editOutcomeInModal(outcome: Outcome) {
        var cmd = commands.StringChangeCommand.createOutcomeNameChange(outcome.getId());
        var form = new EditNameForm(cmd, "System Admin: Edit outcome name", outcome.getName(),
            (commandQueue: CommandQueue) => commandQueue.flushCommands() );
        showFormInModalDom(form);
    }
}

export = EditNameForm;
