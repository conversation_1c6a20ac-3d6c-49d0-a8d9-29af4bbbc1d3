import {OutcomeDto, RiskAreaDto} from 'ecco-dto';

export interface OutcomeRepository {
    findOneOutcome(outcomeId: number): Promise<OutcomeDto>;
    findAllOutcomeSupports(): Promise<OutcomeDto[]>;
    findAllOutcomesByService(serviceName: string): Promise<OutcomeDto[]>;
    createOutcomeSupport(outcome: OutcomeDto): Promise<void>;
    createOutcomeRisk(outcome: RiskAreaDto): Promise<void>;

    findAllOutcomeThreats(): Promise<RiskAreaDto[]>;
}
