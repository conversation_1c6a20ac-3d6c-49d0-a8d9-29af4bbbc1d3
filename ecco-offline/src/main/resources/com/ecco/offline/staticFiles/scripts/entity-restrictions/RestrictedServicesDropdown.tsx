import {dropdownList} from "ecco-components-core";
import {useRestrictedServices} from "./entityRestrictionHooks";

interface Props<T> {
    label: string
    setter: (newState: T) => void
    state: T
    propertyKey: Extract<keyof T, string>
}

export function RestrictedServicesDropdown<T>({label, setter, state, propertyKey}: Props<T>) {
    const {services} = useRestrictedServices()

    return dropdownList(label, setter, state, propertyKey, services)
}
