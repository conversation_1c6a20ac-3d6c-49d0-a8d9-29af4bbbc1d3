import $ = require("jquery");

import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import BaseControl = require("../controls/BaseControl");
import CheckboxInput = require("../controls/CheckboxInput");
import TaskDefinitionEntry = domain.TaskDefinitionEntry;
import TaskDefinition = dto.TaskDefinition;
import {apiClient} from "ecco-components";
import * as domain from "ecco-dto";
import {ServiceTypeAjaxRepository, TaskCommandAjaxRepository} from "ecco-dto";
import * as dto from "ecco-dto/service-config-dto";
import {showModalWithSubmitCancel} from "../components/MUIConverterUtils";

const taskCommandRepository = new TaskCommandAjaxRepository(apiClient.withCachePeriod(0));
const serviceTypeRepository = new ServiceTypeAjaxRepository(apiClient.withCachePeriod(0));

class BackingData {
    constructor(public taskDefs: TaskDefinition[], public taskDefEntries: TaskDefinitionEntry[]) {
    }
}

class EntryControl extends BaseControl {

    constructor(private taskDef: TaskDefinition,
                private callback: (value: string, state: boolean) => void) {

        super( $("<li>") );

        const label = taskDef.name;
        const id = taskDef.name;
        const value = taskDef.name;
        const $cb = new CheckboxInput(label, id, value);
        $cb.setChecked(false);
        //$cb.setReadOnly();
        $cb.change((val, state) => this.callback(label, state));

        const $controlArea = $("<span>").append($cb.element());
        const $descriptionArea = $("<span>").append(taskDef.description).css("font-size", "0.8em");

        this.element().addClass("row")
            .append(
                $("<div>").addClass("col-xs-4").append($controlArea)
            )
            .append(
                $("<div>").addClass("col-xs-8").append($descriptionArea)
            );
    }

}

/** This editor started life with notions of adding and editing a task entry,
 *  but it is currently used to simply select the remaining tasks available and return to the underlying form
 */
class TaskDefinitionEntryEditor extends BaseAsyncDataControl<BackingData> {

    public static showInModal(
            callback: (namesSelected: string[]) => void,
            serviceTypeId: number,
            editTaskName: string,
            taskDefEntries?: TaskDefinitionEntry[]) {
        const form = new TaskDefinitionEntryEditor(serviceTypeId, editTaskName, taskDefEntries, callback);
        form.load();
        showModalWithSubmitCancel("new task definition", "ok", "back", form.element(), () => {callback(form.namesSelected)});
    }

    private $el = $("<ul>").addClass("entry-list list-unstyled");
    private $main = $("<div>");
    private namesSelected: string[] = [];

    //noinspection JSUnusedLocalSymbols editTaskName - see commented out code
    constructor(
            private serviceTypeId: number,
            private editTaskName: string,
            private taskDefEntries?: TaskDefinitionEntry[],
            private callback?: (namesSelected: string[]) => void) {
        super();
    }

    /**
     * BaseAsyncDataControl
     */
    protected fetchViewData(): Promise<BackingData> {
        // if we are provided with the entries so far - use that, else load everything we need
        return taskCommandRepository.getTaskDefinitions()
            .then(defs =>
                this.taskDefEntries
                    ? new BackingData(defs, this.taskDefEntries)
                    : serviceTypeRepository.findOneServiceType(this.serviceTypeId).then(serviceType =>
                        new BackingData(defs, serviceType.getTaskDefinitionEntries())));
    }

    protected render(data: BackingData) {

        this.$main.empty().append(this.$el);
        this.element().empty().append($("<div>").append(this.$main));

        const listOfTaskEntryNamesInUse = data.taskDefEntries.map(taskDefEntry => taskDefEntry.getName());
        const unusedTaskDefs = data.taskDefs.filter(taskDef => listOfTaskEntryNamesInUse.indexOf(taskDef.name) == -1)
            .sort((a, b) => a.name.localeCompare(b.name));
        const allowedNewTaskDefs = unusedTaskDefs.filter(td => td.type != "DEPRECATED")
        let controls = allowedNewTaskDefs.map( td => this.createEntryControl(td) );
        this.$el.append( controls.map(control => control.element() ) );
    }

    private createEntryControl(taskDefEntry: TaskDefinition): EntryControl {
        return new EntryControl(taskDefEntry,
            (value: string, state: boolean) => this.updateNamesSelected(value, state));
    }

    private updateNamesSelected(value: string, state: boolean) {
        if (state) {
            this.namesSelected.push(value);
        } else {
            this.namesSelected.splice(this.namesSelected.indexOf(value), 1);
        }
    }
}

export = TaskDefinitionEntryEditor;
