import hopscotch = require("hopscotch");

/**
 * Manage introductory tours of new features
 */
class Introductions {

    private static isDone(tour: TourDefinition) {
        return localStorage.getItem("ecco.introductions." + tour.id) == "done";
    }

    private static markDone(tour: TourDefinition) {
        localStorage.setItem("ecco.introductions." + tour.id, "done");
    }

    public static welcomePage() {
        var announceMenuChanges: TourDefinition = {
            id: "pre-announce-NRv2",
            steps: [
                {
                    title: "We've updated referral creation",
                    content: "You can use the simpler new referrals screens now.",
                    arrowOffset: "center",
                    xOffset: "center",
                    placement: "bottom",
                    target: "menuitem-30"
                },
                {
                    title: "The old way is still here",
                    content: "You can still use the old screens for a little while, but it will disappear soon.",
                    arrowOffset: "center",
                    xOffset: "center",
                    placement: "bottom",
                    target: "menuitem-80"
                },
                {
                    title: "And ...",
                    content: "We'll always keep recent feature updates in the 'quick guide', so you can check back at any time",
                    arrowOffset: "center",
                    xOffset: "center",
                    placement: "bottom",
                    target: document.querySelector(".navbar-default")!
                }
            ],
            onEnd: () => Introductions.markDone(announceMenuChanges)
        };

        // Start the tour!
        if (!Introductions.isDone(announceMenuChanges)) {
            hopscotch.startTour(announceMenuChanges);
        }
    }
}
export = Introductions

