import {OutcomeRepository} from "./OutcomeRepository"
import {ApiClient, OutcomeDto, RiskAreaDto} from "ecco-dto";

export class OutcomeAjaxRepository implements OutcomeRepository {
    constructor(private apiClient: ApiClient) {
    }

    public findOneOutcome(outcomeId: number): Promise<OutcomeDto> {
        const path = `outcomes/byId/${outcomeId}/`;

        return this.apiClient.get<OutcomeDto>(path);
    }

    /**
     * Get all outcomes (support and threat)
     */
    public findAllOutcomes(): Promise<OutcomeDto[]> {
        const path = "outcomes/";
        return this.apiClient.get<OutcomeDto[]>(path);
    }

    /**
     * Get all support outcomes
     */
    public findAllOutcomeSupports(): Promise<OutcomeDto[]> {
        const path = "outcomes/support/";
        return this.apiClient.get<OutcomeDto[]>(path);
    }

    public findOneRiskArea(riskAreaId: number): Promise<RiskAreaDto> {
        const path = `outcomes/byId/${riskAreaId}/`;
        return this.apiClient.get<RiskAreaDto>(path);
    }

    /**
     * Get all threat outcomes
     */
    public findAllOutcomeThreats(): Promise<RiskAreaDto[]> {
        const path = "outcomes/threat/";

        return this.apiClient.get<RiskAreaDto[]>(path);
    }

    public createOutcomeSupport(outcome: OutcomeDto): Promise<void> {
        const path = "outcomes/support/";
        return this.apiClient.post<void>(path, outcome);
    }

    public createOutcomeRisk(outcome: RiskAreaDto): Promise<void> {
        const path = "outcomes/risk/";
        return this.apiClient.post<void>(path, outcome);
    }

    /**
     * Get all support outcomes associated with this serviceName
     */
    public findAllOutcomesByService(serviceName: string): Promise<OutcomeDto[]> {
        const path = `outcomes/byServiceName/${serviceName}/`;
        return this.apiClient.get<OutcomeDto[]>(path);
    }
}
