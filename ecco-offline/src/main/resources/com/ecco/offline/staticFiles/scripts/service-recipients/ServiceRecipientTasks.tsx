import $ = require("jquery");
import EvidenceDelegatingForm from "../evidence/EvidenceDelegatingForm";
import * as React from "react";
import {FC, useEffect, useRef} from "react";
import {useParams} from "react-router";

interface Params {
    srId: string;
    taskName: string;
    taskHandle?: string | undefined;
}

interface Props {
    /** Allow srId to be provided here if not via router params */
    srId?: number | undefined

    /** Allow task name to be provided here if not via router params */
    taskName?: string | undefined
}

/**
 * Used in the router as /task/ as called from ReferralOverview
 */
const ServiceRecipientTasks: FC<Props> = props => {

    let {srId, taskName} = useParams<Params>();
    const ref = useRef<HTMLDivElement>();

    srId = srId ?? props.srId.toString()
    taskName = taskName ?? props.taskName;

    function load(): void {
        const $elem = $(ref.current).empty();
        // previously here was ReferralTaskControlFactory.getEmbeddedViewTaskControl which was always about evidence only
        EvidenceDelegatingForm.enhance($elem, parseInt(srId), taskName, () => onFormCompletion());
    }

    function onFormDirty(): void {
        console.info("cancel");
        // this.$backLink.click(false);
    }

    function onFormCompletion(): void {
        console.info("save");
        // window.location.href = this.$backLink.attr('href');
    }

    useEffect( () => {
        // trigger load here if not already loaded
        load();
    }, [srId, taskName]);

    return <div ref={ref} className="container-fluid">loading...</div>;
}

export default ServiceRecipientTasks
