{
    "compilerOptions": {
        "lib": ["ES2017"],
        "composite": true, // switches --declaration on which means .d.ts get generated (for use with project references)
        "declaration": true,
//        "declarationDir": "../build/typings", // Don't use this here. It results in one dir with all the typings in
        "downlevelIteration": true,
        "forceConsistentCasingInFileNames": true,
        "noImplicitThis": true,
        "noFallthroughCasesInSwitch": true,
        "noImplicitOverride" : true,
        "noImplicitReturns": true,

        "removeComments": true,
        "skipDefaultLibCheck": true,
        "skipLibCheck": true,
        "sourceMap": true,
        "stripInternal": true,

        "types": []
    }
}
