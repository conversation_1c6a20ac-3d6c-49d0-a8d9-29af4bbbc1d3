import $ = require("jquery");
import BaseAsyncListControl = require("../controls/BaseAsyncListControl");
import BaseListEntryControl = require("../controls/BaseListEntryControl");
import EditServiceTypeForm = require("./EditServiceTypeForm");
import {apiClient} from "ecco-components";
import {ServiceAjaxRepository, Services, ServiceType, ServiceTypeAjaxRepository} from "ecco-dto";

const repository = new ServiceTypeAjaxRepository(apiClient.withCachePeriod(0));
const repositoryServices = new ServiceAjaxRepository(apiClient.withCachePeriod(0));

class ServiceTypeWithServices {
    constructor(public serviceType: ServiceType, public services: Services) {}
}

class EntryControl extends BaseListEntryControl<ServiceTypeWithServices> {

    constructor(serviceType: ServiceTypeWithServices) {
        super(serviceType, "fa fa-cogs");
    }

    protected administerEntry(): void {
        EditServiceTypeForm.showInModal(this.entry.serviceType.getId());
    }

    protected getEditElement(): $.JQuery {
        const serviceNameIds = this.entry.services.getServicesFromServiceType(this.entry.serviceType.id)
                .map(s => {
                    return `id:${s.getServiceId()}, name:${s.getDto().name}`
                })
                .join("; ");
        const $id = $("<small>").text(`[id:${this.entry.serviceType.id}]`)
        const $serviceIds = $("<small>").text(`  [services: ${serviceNameIds}]`);
        const $rhs = $("<span>")
                .append($("<span>").append($id))
                .append($("<span>").append($serviceIds))
        const $name = $("<a>").attr("href", this.entry.serviceType.getId() + "/").text(this.entry.serviceType.getName());
        const $row = $("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-8").append($name))
                .append($("<div>").addClass("col-xs-4").append($rhs))
        return $("<span>").append($row);
    }

    protected getEntryIconClass(): string {
        return "fa fa-cogs";
    }

}

// TODO: Extract base AdminCapableListControl from this and ChartListControl
class ServiceTypesListControl extends BaseAsyncListControl<ServiceTypeWithServices> {

    constructor() {
        super("add new service type", "no service types defined", "fa fa-file-code-o");
    }

    protected fetchViewData(): Promise<ServiceTypeWithServices[]> {
        return repository.findAllServiceTypes().then(st =>
            repositoryServices.findAllServices().then(s => {
                return st.map(t => new ServiceTypeWithServices(t, s));
            })
        )
    }

    protected createItemControl(serviceType: ServiceTypeWithServices) {
        return new EntryControl(serviceType);
    }

    protected addNewEntity() {
        EditServiceTypeForm.showInModal(null);
    };

}
export = ServiceTypesListControl;
