// see context/referral.jsp (and id-badge.tag)
import {FC, useEffect, useState, useRef, useMemo} from "react";
import {Card, CardContent} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {
    SchemaProps, useCounter, useEventHandler,
    useSchema,
    useServiceRecipient,
    useServicesContext
} from "ecco-components";
import {
    link,
} from "ecco-components-core";
import {HactControlEvent, IncidentDto, ResourceList, ServiceRecipient} from "ecco-dto";
import {useHactIntegration} from "../evidence/hact/useHactIntegration";
import {FlagReloadEvent, RiskStatusAreaControl} from "../evidence/risk/RiskStatusAreaControl";
import {useControl} from "../components/ControlWrapper";
import {NewIncident} from "../incidents/inbound/NewIncident";
import {EccoDate} from "@eccosolutions/ecco-common";

/**
 * Render the flag area for a single service recipient.
 * Also see flags.tsx
 * @param srIdFile - the file that we are viewing, so that we can display differently info from other files
 * @param serviceRecipient
 * @constructor
 */
export const FlagPerServiceRecipient: FC<{srIdFile: number, serviceRecipient: ServiceRecipient}> = ({
                                                                                                        srIdFile,
                                                                                                        serviceRecipient
                                                                                                    }) => {
    // we can force the control to reload with an additional argument
    // so we register an event handler to simply do that
    const [v, incV] = useCounter();
    useEventHandler(FlagReloadEvent.bus, () => incV());

    // use a className to avoid the default 'container-fluid v-gap-15'
    const Control = useControl(RiskStatusAreaControl, [serviceRecipient.serviceRecipientId, serviceRecipient, serviceRecipient.serviceRecipientId != srIdFile, v], "container-fluid");
    return <>
        <Control />
    </>;
}

const FlagArea: FC<{serviceRecipient: ServiceRecipient}> = ({serviceRecipient}) => {
    const {sessionData, clientRepository, referralRepository} = useServicesContext();
    const serviceType = sessionData.getServiceTypeByServiceCategorisationId(serviceRecipient.serviceAllocationId);
    const riskTaskName = serviceType.getFirstRiskTaskName(sessionData);
    const scopeClient = serviceType.taskDefinitionSettingHasFlag(riskTaskName, "scope", "client");
    const [serviceRecipients, setServiceRecipients] = useState<ServiceRecipient[]>([serviceRecipient]);

    useEffect(() => {
        // scopeClient for referrals
        if (scopeClient) {
            clientRepository.findOneClientByServiceRecipientId(serviceRecipient.serviceRecipientId)
                    .then(c => referralRepository().findAllReferralWithoutSecuritySummaryByClient(c.clientId)
                            .then(refs => {
                                // filter out the current serviceRecipient and put it at the front
                                refs = refs.filter(r => r.serviceRecipientId != serviceRecipient.serviceRecipientId);
                                const srs = [serviceRecipient].concat(refs);
                                setServiceRecipients(srs)
                            })
                    );
        }
    }, []);

    return <>
        {serviceRecipients?.map(sr =>
                                        <FlagPerServiceRecipient srIdFile={serviceRecipient.serviceRecipientId}
                                                                 serviceRecipient={sr}
                                                                 key={`status-panel-risk-${serviceRecipient.serviceRecipientId}`} />
        )}
    </>;
}

interface IncidentDisplayProps {
    showTitle: boolean;
    serviceRecipient: ServiceRecipient;
    incidentsOpen: number | null;
    incidentsClosed: number | null;
    showingBackToDate: EccoDate | null;
}

const newRenderer = (click: () => void) => link("new", click, "#");
const IncidentDisplay: FC<{dto: IncidentDisplayProps}> = ({dto}) => {
    const open = dto.incidentsOpen ? `open: ${dto.incidentsOpen} ` : '';
    const closed = dto.incidentsClosed ? `closed: ${dto.incidentsClosed} ` : '';
    const some = dto.incidentsOpen || dto.incidentsClosed;
    const since = (some && dto.showingBackToDate) ? `since ${dto.showingBackToDate.formatShort()}` : '';
    return <>
        <div>incidents: {open}{closed}{since} <NewIncident renderer={newRenderer} linkContactId={dto.serviceRecipient.contactId} /></div>
    </>;
}

const IncidentArea: FC<{serviceRecipient: ServiceRecipient}> = ({serviceRecipient}) => {
    // load a page worth of incidents - that can be classed as 'recent'
    const schemaListProps: SchemaProps = useMemo(() => ({
        src: `incidents/?contactSrId=${serviceRecipient.serviceRecipientId}`,
        filters: {},
        onFilter: filters => {
        },
        page: 0,
        onPage: page => {
        },
    }), [serviceRecipient.serviceRecipientId]);
    const {error, resolved, reload} = useSchema(schemaListProps);

    if (!resolved) {
        return <></>;
    }

    const incidents = (resolved.data as ResourceList<IncidentDto>).data;
    const oldestIncidents = incidents
            .sort((a, b) =>
                          a.receivedDate && b.receivedDate
                                  ? EccoDate.parseIso8601(a.receivedDate).earlierThan(EccoDate.parseIso8601(b.receivedDate)) ? 1 : -1
                                  : a.receivedDate ? -1 : 1);
    const oldestIncidentDate = oldestIncidents && oldestIncidents.length > 0 && oldestIncidents[0].receivedDate
    const display: IncidentDisplayProps = {
        showTitle: true,
        serviceRecipient: serviceRecipient,
        incidentsOpen: incidents.filter(inc => inc.statusMessageKey != "status.exited").length || 0,
        incidentsClosed: incidents.filter(inc => inc.statusMessageKey == "status.exited").length || 0,
        showingBackToDate: oldestIncidentDate && EccoDate.parseIso8601(oldestIncidentDate)
    }
    return <>
        <IncidentDisplay dto={display} key={`status-panel-i-${serviceRecipient.serviceRecipientId}`} />
    </>;
}

// panel on the top right
const StatusPanel: FC<{srId: number}> = ({srId}) => {
    const {sessionData, clientRepository} = useServicesContext();
    const {serviceRecipient} = useServiceRecipient(srId);
    useHactIntegration();

    const isReferral = serviceRecipient && serviceRecipient.prefix == "r";
    // trigger HACT control - see EvidencePage
    const hactEl = useRef<HTMLDivElement>(null);
    useEffect(() => {
        if (
                isReferral && // see HactNotificationControl.hactEnabled
                hactEl.current &&
                sessionData.hasHactForService(sessionData.getServiceCategorisation(serviceRecipient.serviceAllocationId).serviceId)
        ) {
            clientRepository
                    .findOneClientByServiceRecipientId(serviceRecipient.serviceRecipientId)
                    .then(c => {
                        // need to load the HactNotificationControl: ControlWrapper would be good, but we don't have access to the component from ecco-ui
                        HactControlEvent.bus.fire(
                                new HactControlEvent(
                                        serviceRecipient.serviceRecipientId,
                                        c.clientId!,
                                        hactEl.current!
                                )
                        );
                    });
        }
    }, [serviceRecipient?.serviceRecipientId, hactEl]);

    if (!serviceRecipient) {
        return <></>;
    }

    const hasIncidents = sessionData.isEnabled("menu.incidents");

    return <>
        <Card elevation={2} style={{backgroundColor: "rgba(0,0,0,0.02)"}}>
            <CardContent>
                <div ref={hactEl} />
                <FlagArea serviceRecipient={serviceRecipient} />
                {isReferral && hasIncidents && <IncidentArea serviceRecipient={serviceRecipient} />}
            </CardContent>
        </Card>
    </>;
}
export default StatusPanel;