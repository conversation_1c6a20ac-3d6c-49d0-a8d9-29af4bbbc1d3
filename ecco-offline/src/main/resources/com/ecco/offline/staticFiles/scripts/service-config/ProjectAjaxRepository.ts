import {SparseArray} from "@eccosolutions/ecco-common";
import {ProjectDto} from "ecco-dto/service-config-dto";
import {ProjectRepository} from "./ProjectRepository";
import {ApiClient} from 'ecco-dto';

export class ProjectAjaxRepository implements ProjectRepository {

    private cache: SparseArray<Promise<ProjectDto>> = {};

    constructor(private apiClient: ApiClient) {
    }

    public findAll(): Promise<ProjectDto[]> {
        return this.apiClient.get<ProjectDto[]>("project/");
    }

    public findOneProject(projectId: number): Promise<ProjectDto> {
        var project = this.cache[projectId];
        if (!project) {
            project = this.apiClient.get<ProjectDto>(`project/${projectId}/`);
            this.cache[projectId] = project;
        }
        return project;
    }
}
