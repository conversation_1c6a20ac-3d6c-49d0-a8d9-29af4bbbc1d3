import {
    App<PERSON>arBase,
    SidebarMenuBuilder,
    useAdminModeMenu,
    UserMenu,
    useServicesContext, useStaff,
    useUser,
    withSessionData,
    LoadingSpinner, useAppBarContext, AppBarContextProvider
} from "ecco-components";
import {EccoModal} from "ecco-components-core";
import {GROUPSUPPORTACTIVITY_LISTNAME, isOffline, VENUE_LISTNAME} from "ecco-dto";
import * as React from "react";
import {FC, ReactElement, useEffect, useRef, useMemo} from "react";
import {Redirect, Route, Switch, useHistory, useParams, useRouteMatch} from "react-router";
import {lazyControlWrapper} from "../components/ControlWrapper";
import {ResizeEvent} from "@eccosolutions/ecco-common";
import {UserHeader, UserView} from "../admin/UserView"
import {MenuUpdateEvent} from "../common/events";
import {StaffHeader, StaffView} from "../hr/StaffView";

const MainMenuItems:FC<{base: string}> = ({base}) => {

    return withSessionData(sessionData =>
        new SidebarMenuBuilder(base)
            .addOwnRoute("users", "fa fa-user", "users",
                 sessionData.hasRoleUserAdmin()
            )
            .addOwnRoute("staff", "fa fa-user", "staff",
                    sessionData.hasRoleHr()
            )
            .addExternalRoute("deletions", "fa fa-eraser", "nav/secure/referrals/delete.html",
                sessionData.hasRoleReferralDelete()
            )
            .addDivider("1")
            .addSubHeader("settings",
                sessionData.hasRoleAdmin())
            .addOwnRoute("services", "fa fa-home", "services",
                sessionData.hasRoleAdmin())
            .addOwnRoute(sessionData.getMessages()["projects"], "fa fa-home", "projects",
                sessionData.hasRoleAdmin())
            .addOwnRoute("list definitions", "fa fa-list", "listDefinitions",
                sessionData.hasRoleAdmin())
            .addDivider("2")
            .addSubHeader("group support",
            sessionData.hasRoleAdmin() || sessionData.hasRoleGroupSupportAdmin())
            .addOwnRoute("activities", "fa fa-group","groupActivities",
                sessionData.hasRoleAdmin() || sessionData.hasRoleGroupSupportAdmin())
            .addOwnRoute("venues", "fa fa-group","groupVenues",
                sessionData.hasRoleAdmin() || sessionData.hasRoleGroupSupportAdmin())
            .addDivider("3")
            .addExternalRoute("old admin menu", "fa fa-wrench", "nav/secure/admin/",
                sessionData.hasRoleSysAdmin()
            )
            .addExternalRoute("back to menu", "fa fa-arrow-left",
                "nav/secure/welcome.html",
                true, isOffline() ? "currently offline" : undefined, isOffline()
            )
            .build()
        );
}

function ModalUserView() {
    const params = useParams<{username:string}>()
    const history = useHistory()
    const {user} = useUser(params.username);
    return <EccoModal
        title={user ? <UserHeader user={user} summary/> : params.username}
        fullScreen={true}
        maxWidth="md"
        show={true}
        onEscapeKeyDown={() => history.goBack()}
    >
        <UserView username={params.username}/>
    </EccoModal>
}

const UsersList = React.lazy( () => import("ecco-components").then(i => ({default: i.UsersList})));

type MenuProps = { setUserMenu: (items: null | ReactElement | ReactElement[]) => void };

// @ts-ignore because ... pain!
const ListDefinitionListControl = lazyControlWrapper(() => import("../feature-config/ListDefListControl"), undefined);
const ListDefinitionList: FC<MenuProps> = ({setUserMenu}) => {
    useAdminModeMenu("manage lists");
    return <ListDefinitionListControl/>;
};

const ProjectsListControl = lazyControlWrapper(() => import("../service-config/ProjectsListControl"));
const ProjectsList: FC<MenuProps> = ({setUserMenu}) => {
    useAdminModeMenu("manage projects");
    return <ProjectsListControl/>;
};

const ServicesListControl = lazyControlWrapper(() => import("../service-config/ServicesListControl"));
const ServicesList: FC<MenuProps> = ({setUserMenu}) => {
    useAdminModeMenu("manage services");
    return <ServicesListControl/>;
};

const gsHandler = (menuRef: React.MutableRefObject<HTMLDivElement | undefined>) => (event: MenuUpdateEvent) => {
    if (!menuRef.current) return
    while (menuRef.current.firstChild) {
        menuRef.current.removeChild(menuRef.current.firstChild);
    }
    menuRef.current.append(event.content[0]);
    ResizeEvent.bus.fire(new ResizeEvent());
}

const MenuArea: FC = () => {
    const menuRef = useRef<HTMLDivElement>()

    useEffect( () => {
        const handler = gsHandler(menuRef)
        if (menuRef.current) {
            MenuUpdateEvent.bus.addHandler(handler)
        }
        return () => {
            MenuUpdateEvent.bus.removeHandler(handler)
        }
    }, [menuRef])

    return <div style={{padding: "20px"}} ref={menuRef}/>
}

const GroupActivityMappingControl = lazyControlWrapper(() => import("../admin/group-support/ManageServiceActivityTypesControl"));
const GroupActivityMappingWrapper: FC<MenuProps> = ({setUserMenu}) => {

    useAdminModeMenu("manage service mappings");

    return <>
        <MenuArea/>
        <GroupActivityMappingControl/>
    </>
};
const GroupActivityMapping: FC<MenuProps> = ({setUserMenu}) => {
    return useMemo(() => <GroupActivityMappingWrapper setUserMenu={setUserMenu}/>, [])
}
const GroupSupportTypesControl = lazyControlWrapper(() => import("../feature-config/ListDefListControl"), GROUPSUPPORTACTIVITY_LISTNAME);
const GroupSupportTypes: FC<MenuProps> = ({setUserMenu}) => {
    useAdminModeMenu("manage lists");
    return <GroupSupportTypesControl/>;
};
const VenueListControl = lazyControlWrapper(() => import("../feature-config/ListDefListControl"), VENUE_LISTNAME);
const GroupSupportVenues: FC<MenuProps> = ({setUserMenu}) => {
    useAdminModeMenu("manage lists");
    return <VenueListControl/>;
};

const StaffList = React.lazy( () => import("../hr/StaffList").then(i => ({default: i.StaffList})));
function ModalStaffView() {
    const params = useParams<{workerId:string}>()
    const history = useHistory()
    const {staff} = useStaff(parseInt(params.workerId))
    return <EccoModal
            title={staff ? <StaffHeader worker={staff}/> : params.workerId}
            fullScreen={true}
            maxWidth="md"
            show={true}
            onEscapeKeyDown={() => history.goBack()}
    >
        {!staff ? <LoadingSpinner/> : <StaffView staff={staff}/>}
    </EccoModal>
}


function AdminAppBar() {
    return <AppBarContextProvider>
        <AdminAppBarBase/>
    </AppBarContextProvider>
}

const AdminAppBarBase: FC = () => {
    let {path} = useRouteMatch();
    path = path == "/" ? "/" : path + "/";
    const history = useHistory();
    const {sessionData} = useServicesContext();
    const wideMode = path.startsWith("/nav/w/");

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }
    return <AppBarBase appColour={ctx.appColour} title={"admin overview"}
                       right={<UserMenu extraMenuItems={ctx.extraMenuItems}/>}
                       drawerContent={<MainMenuItems base={path}/>}
                       wideMode={wideMode}
                       onLogoClick={() => history.push("/nav/r/welcome/")}>
        <Switch>
            <Route path={`${path}listDefinitions`}>
                <ListDefinitionList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}services`}>
                <ServicesList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}projects`}>
                <ProjectsList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}groupActivityMapping`}>
                <GroupActivityMapping setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}groupActivities`}>
                <GroupSupportTypes setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}groupVenues`}>
                <GroupSupportVenues setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route exact path={`${path}staff`}><StaffList/></Route>
            {/*<Route exact path={`${path}staff`}><StaffSearchPage/></Route>*/}
            <Route exact path={`${path}staff/:workerId`}>
                <ModalStaffView/>
            </Route>
            <Route exact path={`${path}users/:username`}>
                <ModalUserView/>
            </Route>
            <Route exact path={[`${path}users`]}>
                {sessionData.hasRoleUserAdmin() ? <UsersList/> : <div/>}
            </Route>
            <Route exact path={`${path}`}>
                {sessionData.hasRoleUserAdmin() && !(sessionData.hasRoleAdmin())
                        ? <Redirect to={`${path}users`}/> : <div/>}
            </Route>
        </Switch>
    </AppBarBase>;
}

export default AdminAppBar;