import $ = require("jquery");
import Lazy = require("lazy");

import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextInput = require("../controls/TextInput");
import ldapDto = require("../ldap/dto");
import commands = require("./commands");
import ProjectDto = dto.ProjectDto;
import AclDomains = ldapDto.AclDomains;
import LdapGroupAggregateDto = ldapDto.LdapGroupAggregateDto;
import LdapGroupLocalAcl = ldapDto.LdapGroupLocalAcl;
import {apiClient} from "ecco-components";
import * as dto from "ecco-dto/service-config-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {LdapAjaxRepository} from "../ldap/LdapAjaxRepository";
import {ProjectAjaxRepository} from "./ProjectAjaxRepository";
import {handle404AsNullResult} from "ecco-dto";

var repository = new ProjectAjaxRepository(apiClient);
var ldapRepository = new LdapAjaxRepository(apiClient);

class BackingData {
    constructor(public projectDto: ProjectDto,
            public ldapEnabled: boolean,
            public ldapGroups: LdapGroupAggregateDto[]) {
    }
}

class EditProjectForm extends BaseAsyncCommandForm<BackingData> {

    public static showInModal(projectId: number) {
        var form = new EditProjectForm(projectId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("project name");
    private ldap = new TextInput("AD group");
    private ldapGroup = new InputGroup("AD group", this.ldap);
    private ldapGroups = $("<div>").addClass("form-group");
    private origDto: ProjectDto;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private projectId: number) {
        super(!projectId ? "add new project" : "edit project");
        this.form
            .append( new InputGroup("project name", this.name) );
        this.form
            .append( this.ldapGroup );
        this.form
            .append( this.ldapGroups );
    }

    protected fetchViewData(): Promise<BackingData> {
        var projectQ = Promise.resolve(null);
        if (this.projectId) {
            projectQ = repository.findOneProject(this.projectId);
        }

        const ldapEnabledQ = ldapRepository.enableLdap().catch(handle404AsNullResult);

        return Promise.all([ldapEnabledQ, projectQ])
            .then(([ldapEnabled, project]:[boolean, ProjectDto]) => {
                let ldapGroupsQ = Promise.resolve(null);
                if (ldapEnabled) {
                    ldapGroupsQ = ldapRepository.listEntries();
                }
                return ldapGroupsQ.then(ldapGroups => {
                    return new BackingData(project, ldapEnabled, ldapGroups);
                });
            });
    }

    protected render(data: BackingData) {
        this.origDto = data.projectDto;

        if (data.projectDto) {
            this.name.setVal(data.projectDto.name);
        }

        if (!data.ldapEnabled) {
            this.ldapGroup.element().hide();
        } else {
            var items = this.listLdapsForProject(data);
            items.forEach((group, idx) => {
                var ctl = new TextInput("existing_group_"+idx.toString());
                ctl.setReadOnly();
                ctl.setVal(group);
                var grp = new InputGroup("assigned AD group", ctl);
                this.ldapGroups.append(grp.element());
            });
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        var cmd;
        if (this.origDto) {
            cmd = new commands.ProjectCommand("update", this.origDto.id)
                .nameChange(this.origDto.name, this.name.val());
        }
        else {
            cmd = new commands.ProjectCommand("add", null)
                .nameChange(null, this.name.val());
        }

        if (this.ldap.val() != "") {
            cmd.setLdapGroupsToAdd(new Array<string>(this.ldap.val()));
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

    private listLdapsForProject(data: BackingData): string[] {
        var items = Lazy(data.ldapGroups)
            .map((group: LdapGroupAggregateDto) =>
                group.localAcls
                    .map((acl: LdapGroupLocalAcl) => {
                        var ret: LdapFlatten = {
                            ldapGroup: group.ldapGroup,
                            localClass: acl.localClass,
                            localId: acl.localId
                        }
                        return ret;
                    })
                    .filter((struct: LdapFlatten) => {
                        var applies = (struct.localClass == AclDomains.PROJECT)
                            && (struct.localId == this.projectId);
                        return applies;
                    })
                    .map((struct: LdapFlatten) => {
                        return struct.ldapGroup;
                    })
            )
        .flatten<string>()
        .toArray();

        return items;
    }

}

/*

*/

class LdapFlatten {
    ldapGroup: string;
    localClass: string;
    localId: number;
}


export = EditProjectForm;
