import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextInput = require("../controls/TextInput");
import commands = require("./commands");
import {Uuid} from "@eccosolutions/ecco-crypto";
import {apiClient} from "ecco-components";
import {ActionGroupDto, OutcomeDto} from "ecco-dto/service-config-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {OutcomeAjaxRepository} from "./OutcomeAjaxRepository";

const repository = new OutcomeAjaxRepository(apiClient);


class EditActionGroupForm extends BaseAsyncCommandForm<OutcomeDto> {

    public static showInModal(outcomeId: number, actionGroupId: number) {
        var form = new EditActionGroupForm(outcomeId, actionGroupId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("group name");
    private origDto: ActionGroupDto;
    private outcome: OutcomeDto;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private outcomeId: number, private actionGroupId: number) {
        super(!actionGroupId ? "add new group" : "edit group");
        this.form
            .append( new InputGroup("group name", this.name) );
    }

    protected fetchViewData(): Promise<OutcomeDto> {
        if (this.outcomeId) {
            return repository.findOneOutcome(this.outcomeId);
        }
        return Promise.resolve(null);
    }

    protected render(outcome: OutcomeDto) {
        this.outcome = outcome;
        this.origDto = outcome.actionGroups.filter(ag => ag.id == this.actionGroupId).pop();

        if (this.origDto) {
            this.name.setVal(this.origDto.name);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }

    protected override submitForm(): Promise<void> {
        let cmd;
        if (this.origDto) {
            cmd = new commands.ActionGroupDefChangeCommand("update", this.outcome.uuid, this.origDto.uuid)
                .changeName(this.origDto.name, this.name.val());
        }
        else {
            cmd = new commands.ActionGroupDefChangeCommand("add", this.outcome.uuid, Uuid.randomV4().toString())
                .changeName(null, this.name.val());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

}

export = EditActionGroupForm;
