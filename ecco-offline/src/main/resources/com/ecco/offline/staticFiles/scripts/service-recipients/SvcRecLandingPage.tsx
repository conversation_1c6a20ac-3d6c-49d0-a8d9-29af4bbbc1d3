import {<PERSON>, CardContent, Grid, Typography, Divider} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC} from "react";
import {
    AuditHistoryController,
    useCurrentServiceRecipientWithEntities,
    useExternalSync,
    useServiceRecipientAuditAccess, useServicesContext
} from "ecco-components";
import {Workflow} from "../workflow/WorkflowPanels";
import {ReferralContacts} from "../referral/ReferralContacts";
import {SupportHistoryWrapper} from "../offline/router";

const InfoPanel = React.lazy(() => import("../service-recipients/InfoPanel"));
const StatusPanel = React.lazy(() => import("../service-recipients/StatusPanel"));
const WorkflowPanels = React.lazy(() => import("../workflow/WorkflowPanels"));

// repeated in InfoPanel etc
const PadInCenter: FC = (props) => {
    return <Grid container justify="center">
        <Grid item xs={12} md={8}>
            {props.children}
        </Grid>
    </Grid>
}
const PanelTitle: FC<{label: string}> = ({label}) => {
    if (!label) {
        return null;
    }
    return (<>
        <Typography align={"center"} color="textSecondary" gutterBottom variant="body2">
            {label}
        </Typography>
        <Divider variant="middle" />
    </>);
}
const PanelCard = (props: {label: string, children: React.ReactNode}) => {
    return <Card elevation={2} style={{backgroundColor: "rgba(0,0,0,0.02)"}}>
        <PanelTitle label={props.label}/>
        <CardContent>
            <Grid container wrap={"nowrap"}>
                <PadInCenter>
                    {props.children}
                </PadInCenter>
            </Grid>
        </CardContent>
    </Card>;
}

export const SvcRecLandingPage = (props: {srId: number}) => {

    // NB triggers only on the landing page - for other places, see commit
    useExternalSync();
    useServiceRecipientAuditAccess(props.srId)

    const {sessionData} = useServicesContext();
    const {resolved: context, reload} = useCurrentServiceRecipientWithEntities();

    const IncidentPage = <Grid container>
        <Grid item xs={12} md={6}>
            <InfoPanel srId={props.srId}/>
        </Grid>
        <Grid item xs={12} md={6}>
            <StatusPanel srId={props.srId}/>
        </Grid>
        <Grid container>
            <Grid item xs={12} md={6}>
                <Grid item xs={12} style={{paddingTop: "8px"}}>
                    <PanelCard label={"pathway"}><Workflow srId={props.srId}/></PanelCard>
                </Grid>
                <Grid item xs={12} style={{paddingTop: "8px"}}>
                    <PanelCard label={"history"}><SupportHistoryWrapper srId={props.srId}/></PanelCard>
                </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
                <Grid item xs={12} style={{paddingTop: "8px"}}>
                    <PanelCard label={"people"}><ReferralContacts serviceRecipientId={props.srId} sessionData={sessionData} printView={false}/></PanelCard>
                </Grid>
                <Grid item xs={12} style={{paddingTop: "8px"}}>
                    <PanelCard label={"audits"}><AuditHistoryController serviceRecipientId={props.srId} showWithoutSearch={true} sessionData={sessionData}/></PanelCard>
                </Grid>
            </Grid>
        </Grid>
    </Grid>;

    const RepairPage = <Grid container>
        <Grid item xs={12} md={6}>
            <InfoPanel srId={props.srId}/>
        </Grid>
        <Grid item xs={12} md={6}>
            <StatusPanel srId={props.srId}/>
        </Grid>
        <Grid item xs={12} md={6}>
            <PanelCard label={"pathway"}><Workflow srId={props.srId}/></PanelCard>
        </Grid>
        <Grid item xs={12} md={6}>
            <PanelCard label={"contacts"}><ReferralContacts serviceRecipientId={props.srId} sessionData={sessionData} printView={false}/></PanelCard>
        </Grid>
        <Grid item xs={12} md={6}>
            <PanelCard label={"audits"}><AuditHistoryController serviceRecipientId={props.srId} showWithoutSearch={true} sessionData={sessionData}/></PanelCard>
        </Grid>
    </Grid>;

    const DefaultPage = <Grid container>
        <Grid item xs={12} md={6}>
            <InfoPanel srId={props.srId}/>
        </Grid>
        <Grid item xs={12} md={6}>
            <StatusPanel srId={props.srId}/>
        </Grid>
        <Grid item xs={12}>
            <WorkflowPanels srId={props.srId}/>
        </Grid>
    </Grid>;

    switch (context?.serviceRecipient?.prefix) {
        case "i":
            return IncidentPage;
        case "m":
            return RepairPage;
        case "mv":
            return DefaultPage; // TODO
        default:
            return DefaultPage;
    }

}
