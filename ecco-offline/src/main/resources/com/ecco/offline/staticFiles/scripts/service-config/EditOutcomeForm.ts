import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextInput = require("../controls/TextInput");
import commands = require("./commands");
import {Uuid} from "@eccosolutions/ecco-crypto";
import {apiClient} from "ecco-components";
import {OutcomeDto} from "ecco-dto/service-config-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {OutcomeAjaxRepository} from "./OutcomeAjaxRepository";

var repository = new OutcomeAjaxRepository(apiClient);


class EditOutcomeForm extends BaseAsyncCommandForm<OutcomeDto> {

    public static showInModal(outcomeId: number) {
        var form = new EditOutcomeForm(outcomeId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("outcome name");
    private origDto: OutcomeDto;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private outcomeId: number) {
        super(!outcomeId ? "add new outcome" : "edit outcome");
        this.form
            .append( new InputGroup("outcome name", this.name) );
    }

    protected fetchViewData(): Promise<OutcomeDto> {
        if (this.outcomeId) {
            return repository.findOneOutcome(this.outcomeId);
        }
        return Promise.resolve(null);
    }

    protected render(outcome: OutcomeDto) {
        this.origDto = outcome;

        if (outcome) {
            this.name.setVal(outcome.name);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        let cmd;
        if (this.origDto) {
            cmd = new commands.OutcomeDefChangeCommand("update", this.origDto.uuid)
                .changeName(this.origDto.name, this.name.val());
        }
        else {
            cmd = new commands.OutcomeDefChangeCommand("add", Uuid.randomV4().toString(), "NEEDS")
                .changeName(null, this.name.val());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

}

export = EditOutcomeForm;
