import $ = require("jquery");

import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import events = require("../common/events");
import BaseControl = require("../controls/BaseControl");
import commands = require("./commands");
import taskSettingTypes = require("./taskSettingTypes");
import TaskSettingType = taskSettingTypes.TaskSettingType;
import {AdminMode, iterable} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {
    adminModeEnabled,
    ServiceType,
    ServiceTypeAjaxRepository,
    SessionData,
    TaskCommandAjaxRepository,
    TaskDefinition,
    TaskSetting
} from "ecco-dto";
import {ExportType} from "./taskSettingTypes";
import {getFeatureConfigRepository} from "ecco-offline-data";
import URI = require("URI");
import MvcUtils from "../mvc/MvcUtils";
import {applicationRootPath} from "application-properties";

const serviceTypeRepository = new ServiceTypeAjaxRepository(apiClient.withCachePeriod(0));
const taskCommandRepository = new TaskCommandAjaxRepository(apiClient.withCachePeriod(0));


class EntryControl extends BaseControl {

    private $labelArea: $.JQuery;
    private readonly $valueArea: $.JQuery;

    constructor(private settingType: TaskSettingType, private settingName: string,
            private changedValue: (oldValue: string, newValue: string) => void,
            private taskSetting: TaskSetting, private serviceTypeId: number, private taskName: string) {

        super( $("<li>") );
        const iconClass = "fa fa-cog";

        this.$labelArea = $("<span>").text(settingName);

        this.$valueArea = $("<span>");
        settingType.renderSettingValue(adminModeEnabled(), this.$valueArea, () => {}, taskSetting, serviceTypeId, taskName);

        this.element().addClass("row")
            .append(
                $("<div>").addClass("col-xs-1").append(
                    $("<span>").addClass("fa fa-lg").addClass(iconClass)
                )
            )
            .append(
                $("<div>").addClass("col-xs-2").append(
                    this.$labelArea
                )
            )
            .append(
                $("<div>").addClass("col-xs-5").append(
                    $("<span>").css("font-size", "0.8em").text(settingType.getSettingComment())
                )
            )
            .append(
                $("<div>").addClass("col-xs-3").append(this.$valueArea)
            )
    }

    public getSettingName() {
        return this.settingName;
    }

    public setAdminMode(enabled: boolean) {
        this.settingType.renderSettingValue(
            enabled,
            this.$valueArea,
            (newValue: string) => {
                    this.labelAsChanged();
                    this.changedValue(this.taskSetting.getValue(), newValue);
                    this.taskSetting.setValue(newValue);
                },
            this.taskSetting,
            this.serviceTypeId, this.taskName);
    }

    public labelAsUnchanged() {
        this.$labelArea.css("background-color", "");
    }

    private labelAsChanged() {
        this.$labelArea.css("background-color", "yellow");
    }

}

// TODO: Extract base AdminCapableListControl from this and ChartListControl
class TaskDefinitionEntrySettingsListControl extends BaseAsyncCommandForm<TaskSetting[]> {

    private controls: EntryControl[] = [];
    private serviceType: ServiceType;
    private taskDefinitions: TaskDefinition[];
    private sessionData: SessionData;
    private footer: $.JQuery;

    private static readonly NonExportSettings: string[] = [ "flagListName",  "outcomesById",
         "locationListName",  "meetingStatusListName",
         "locationListName",  "meetingStatusListName",
         "clientStatusListName",  "actionDefaultListName",
         "questionGroupsById",  "flagThreatsById",
         "questions",  "formDefinitions",
         "formDefinition",  "outcomes",
         "commentTypeListName", "commentTypesById"
    ];

    constructor(private servicetypeId: number, private taskName: string) {
        super("-not used-");
        this.setOnFinished( () => {} );
    }

    protected fetchViewData(): Promise<TaskSetting[]> {
        return getFeatureConfigRepository().getSessionData()
                .then(sessionData => {
                    this.sessionData = sessionData;
                    return taskCommandRepository.getTaskDefinitions().then(defs => {
                        this.taskDefinitions = defs;
                        return serviceTypeRepository.findOneServiceType(this.servicetypeId)
                            .then(st => {
                                this.serviceType = st;
                                return st.getTaskDefinitionEntries()
                                    .filter((def) => def.getName() == this.taskName)
                                    .map((def) => def.getSettings())
                                    .pop()
                            })
                    })
                });
    }

    protected render(items: TaskSetting[]): void {
        if (!items) {
            throw new Error("task does not exist on service type: " + this.taskName + ", on " + this.servicetypeId);
        }
        const $el = $("<ul>").addClass("entry-list list-unstyled");


        const allSettingTypesForTaskName = taskSettingTypes.taskSettingTypesBuilder(this.taskName, this.taskDefinitions);

        if (allSettingTypesForTaskName.size == 0) {
            $el.append( $("<li>").text("no task definition settings available") );
        } else {
            this.controls = Array.from(iterable.map(allSettingTypesForTaskName.keys(), settingName =>
                new EntryControl(
                    allSettingTypesForTaskName.get(settingName),
                    settingName,
                    (oldValue: string, newValue: string) =>
                        this.createCommand(allSettingTypesForTaskName.get(settingName), settingName, oldValue, newValue),
                    this.findOrCreateTaskSetting(settingName, items),
                    this.servicetypeId, this.taskName
                )
            ));
            $el.append( this.controls.map( (control) => control.element() ) );
        }

        this.element().empty().append($el);

        this.footer = this.getFooter();

        let uri = URI(window.location.href)
        const pathParts = MvcUtils.getAppPathComponents(uri)
        //const entityId = parseInt(pathParts[pathParts.length - 3])
        //const redirect = `${applicationRootPath}nav/service-config/servicetype/${entityId}/`
        const pathPartsNotEmpty = pathParts[pathParts.length-1] == "" ? pathParts.slice(0,-2) : pathParts.slice(0,-1)
        const redirect = `${applicationRootPath}${pathPartsNotEmpty.join("/")}/`
        const $mainConfig = $("<a>").text("back to service type tasks")
                .click(() => window.location.href = redirect)
        const $nav = $("<div>")
                .append($("<p>").append("manage settings for: " + this.taskName + " [" + this.serviceType.getName() + "]", this.footer))
                .append($("<p>").append($mainConfig))
        events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $nav) );

        this.setAdminMode(adminModeEnabled());

        AdminMode.bus.addHandler(event => {
            this.setAdminMode(event.enabled);
        });
    }

    private setAdminMode(adminMode: boolean) {
        if (adminMode) {
            this.footer.show();
        } else {
            this.footer.hide();
        }

        this.controls && this.controls.forEach( (item) => item.setAdminMode(adminMode) );
    }

    private findOrCreateTaskSetting(settingName: string, items: TaskSetting[]): TaskSetting {
        const taskSetting = items.filter((item) => item.getName() == settingName).pop();
        if (taskSetting) {
            return taskSetting;
        } else {
            return new TaskSetting(settingName, null);
        }
    }

    private createCommand(settingType: TaskSettingType, settingName: string, oldValue: string, newValue: string) {
        const cmd = new commands.ServiceConfigTaskEntrySettingChangeCommand(settingType, this.servicetypeId, this.taskName, settingName, oldValue, newValue);
        this.commandQueue.addCommand(cmd);
        if (this.commandQueue.size() > 0) {
            this.enableSubmit();
        } else {
            // turn off the css changes if there is nothing to save (eg merged cmd results in nothing)
            this.disableSubmit();
            this.controls && this.controls.forEach( (item) => item.labelAsUnchanged() );
        }
    }

    public static exportOriginalAsNewCommand(exportType: ExportType, taskName: string, settingName: string,
                serviceType: ServiceType, newValue: string, taskDefinitions: TaskDefinition[], sessionData: SessionData): commands.ServiceConfigTaskEntrySettingChangeCommand {

        if (exportType == "CONFIG_ONLY") {
           if (this.NonExportSettings.includes(settingName)) {
               return null;
           }
        }

        // if exportType, then swap the 'outcomesById' to be 'transientOutcomesById"
        if (exportType == "OFFSITE") {
            if (settingName ==  "outcomesById") {
                let uuids = serviceType.getOutcomesForTaskName(taskName, sessionData)
                .map(o => o.uuid).reduce((prev, curr) => prev + "," + curr);
                return new commands.ServiceConfigTaskEntrySettingChangeCommand(
                    taskSettingTypes.taskSettingTypesBuilder(taskName, taskDefinitions).get( "transientOutcomesByUuid"),
                    serviceType.id, taskName,  "transientOutcomesByUuid", null, uuids);
            }
        }

        return new commands.ServiceConfigTaskEntrySettingChangeCommand(
                taskSettingTypes.taskSettingTypesBuilder(taskName, taskDefinitions).get(settingName),
                serviceType.id, taskName, settingName, null, newValue);
    }

}
export = TaskDefinitionEntrySettingsListControl;
