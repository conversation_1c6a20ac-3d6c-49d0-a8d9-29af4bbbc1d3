import $ = require("jquery");
import BaseAsyncListControl = require("../controls/BaseAsyncListControl");
import BaseListEntryControl = require("../controls/BaseListEntryControl");
import EditRiskAreaForm = require("./EditRiskAreaForm");
import {apiClient} from "ecco-components";
import {OutcomeAjaxRepository} from "./OutcomeAjaxRepository";
import URI from "URI";
import { RiskAreaDto } from 'ecco-dto';

const repository = new OutcomeAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<RiskAreaDto> {

    constructor(riskArea: RiskAreaDto) {
        super(riskArea, "fa fa-star-o");
    }

    protected administerEntry(): void {
        EditRiskAreaForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        let uri = URI(window.location.href)
        return $("<a>").attr("href", `${uri}${this.entry.id}/`).text(this.entry.name)
    }

    protected getEntryIconClass(): string {
        return "fa fa-star-o";
    }

}

// TODO: Extract base AdminCapableListControl from this and ChartListControl
class RiskAreasListControl extends BaseAsyncListControl<RiskAreaDto> {

    constructor() {
        super("add new risk area", "no risk areas defined", "fa fa-star-o");
    }

    protected fetchViewData(): Promise<RiskAreaDto[]> {
        return repository.findAllOutcomeThreats();
    }

    protected createItemControl(riskArea: RiskAreaDto) {
        return new EntryControl(riskArea);
    }

    protected addNewEntity() {
        EditRiskAreaForm.showInModal(null);
    };

}
export = RiskAreasListControl;
