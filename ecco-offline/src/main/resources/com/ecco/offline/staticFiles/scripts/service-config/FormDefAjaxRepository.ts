import URI = require("URI");
import {ApiClient} from "ecco-dto";
import {apiClient} from "ecco-components";
import {FormDefinition} from "ecco-dto";
import FormDefRepository from "./FormDefRepository";


export class FormDefAjaxRepository implements FormDefRepository {

    static instance = new FormDefAjaxRepository(apiClient);

    constructor(private apiClient: ApiClient) {
    }

    findAllFormDefinitions(): Promise<FormDefinition[]> {
        const apiPath = URI("formDef/").segmentCoded("");

        return this.apiClient.get<FormDefinition[]>(apiPath);
    }

    findFormDefinitionDefByUuid(uuid: string): Promise<FormDefinition> {
        var apiPath = URI("formDef/")
            .segmentCoded(uuid)
            .segmentCoded("");
        return this.apiClient.get<FormDefinition>(apiPath);
    }

}
