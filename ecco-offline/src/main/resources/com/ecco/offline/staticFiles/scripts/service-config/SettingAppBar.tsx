import {
    App<PERSON>ar<PERSON><PERSON>,
    AppBarContextProvider,
    SidebarMenuBuilder,
    useAdminModeMenu, useAppBarContext,
    UserMenu,
    useServicesContext,
    withSessionData
} from "ecco-components";
import {isOffline} from "ecco-dto";
import * as React from "react";
import {FC, ReactElement, useEffect, useState, useRef} from "react";
import {Route, Switch, useHistory, useParams, useRouteMatch} from "react-router";
import {lazyControlWrapper, useControl} from "../components/ControlWrapper";
import {delay, ResizeEvent} from "@eccosolutions/ecco-common";
import {MenuUpdateEvent} from "../common/events";
import QuestionGroupControl from "./QuestionGroupControl";
import OutcomeControl from "./OutcomeControl";
import RiskAreaControl from "./RiskAreaControl";
import {AuditHistory} from "../service-recipients/components/AuditHistory";
import {applicationRootPath} from "application-properties";
import {<PERSON><PERSON>, <PERSON>rid} from "@eccosolutions/ecco-mui";
import {CacheCommand} from "../admin/cache/commands";


const MainMenuItems:FC<{base: string}> = ({base}) => {

    return withSessionData(sessionData =>
        new SidebarMenuBuilder(base)
            .addOwnRoute("pathway definitions", "fa fa-cog", "servicetypes/",
                sessionData.hasRoleAdmin())
            .addOwnRoute("question groups", "fa fa-question", "questiongroups/",
                sessionData.hasRoleAdmin())
            .addOwnRoute("outcomes", "fa fa-star-o", "outcomes/",
                sessionData.hasRoleAdmin())
            .addOwnRoute("risks", "fa fa-star-o", "riskAreas/",
                sessionData.hasRoleAdmin())
            .addOwnRoute("form definitions", "fa fa-file-o", "formDefinitions",
                sessionData.hasRoleAdmin())
            .addOwnRoute("list definitions", "fa fa-list", "listDefinitions",
                sessionData.hasRoleAdmin())
            .addDivider("1")
            .addOwnRoute("features", "fa fa-group", "features/",
                sessionData.hasRoleSysAdmin())
            .addOwnRoute("clear cache", "fa fa-server", "clearCache",
                sessionData.hasRoleAdmin())
            .addDivider("1")
            .addOwnRoute("config audits", "fa fa-history", "audits",
                sessionData.hasRoleAdmin())
            .addDivider("1")
            .addExternalRoute("back to menu", "fa fa-arrow-left",
                "nav/secure/welcome.html",
                true, isOffline() ? "currently offline" : null, isOffline()
            )
            .build()
        , null);
}

type MenuProps = { setUserMenu: (items: null | ReactElement | ReactElement[]) => void };

const ListDefinitionListControl = lazyControlWrapper(() => import("../feature-config/ListDefListControl"), undefined);
const ListDefinitionList: FC<MenuProps> = () => {
    useAdminModeMenu("manage lists");
    return <ListDefinitionListControl/>;
};

const FormDefinitionListControl = lazyControlWrapper(() => import("../service-config/FormDefinitionListControl"));
const FormDefinitionList: FC<MenuProps> = () => {
    useAdminModeMenu("manage forms");
    return <FormDefinitionListControl/>;
};

const ServiceTypesListControl = lazyControlWrapper(() => import("../service-config/ServiceTypesListControl"));
const ServiceTypesList: FC<MenuProps> = () => {
    useAdminModeMenu("manage pathway config");
    return <ServiceTypesListControl/>;
};

/*
export function useMenuUpdate(name: string) {
    const appBarContext = useAppBarContext();
    if (!appBarContext) {
        return;
    }
    const {setExtraMenus} = appBarContext;
    useEffect(() => {
        sessionStorage['admin-mode'] = 'n'; // i.e. disable each time we navigate here
        setExtraMenus(<AdminModeMenuItem name={name}/>);
        return () => setExtraMenus(null);
    }, []);
}
*/

const getHandler = (menuRef: React.MutableRefObject<HTMLDivElement>) => (event: MenuUpdateEvent) => {
    while (menuRef.current.firstChild) {
        menuRef.current.removeChild(menuRef.current.firstChild);
    }
    menuRef.current.append(event.content[0]);
    ResizeEvent.bus.fire(new ResizeEvent());
}

const MenuArea: FC = () => {
    const menuRef = useRef<HTMLDivElement>()

    useEffect( () => {
        const handler = getHandler(menuRef)
        if (menuRef.current) {
            MenuUpdateEvent.bus.addHandler(handler)
        }
        return () => {
            MenuUpdateEvent.bus.removeHandler(handler)
        }
    }, [menuRef])

    return <div style={{padding: "20px"}} ref={menuRef}/>
}

const FeatureListControl = lazyControlWrapper(() => import("../admin/feature-config/FeatureConfigForm"));
const FeatureList: FC<MenuProps> = () => {
    return <>
        <FeatureListControl />
    </>
};

const TaskDefinitionsList: FC<MenuProps> = () => {
    const params = useParams<{id:string}>()

    useAdminModeMenu("manage pathway config");

    const servicetypeId = parseInt(params.id)
    // @ts-ignore because ... pain!
    const TaskDefinitions = lazyControlWrapper(() => import("./TaskDefinitionEntriesListControl"), servicetypeId);

    return <>
        <MenuArea/>
        <TaskDefinitions/>
    </>;
};

const TaskDefinitionSettingsList: FC<MenuProps> = () => {
    const params = useParams<{id:string, taskName: string}>()

    useAdminModeMenu("manage pathway config");

    const servicetypeId = parseInt(params.id)
    // @ts-ignore because ... pain!
    const TaskDefinitionSettings = lazyControlWrapper(() => import("./TaskDefinitionEntrySettingsListControl"), servicetypeId, params.taskName)

    return <>
        <MenuArea/>
        <TaskDefinitionSettings/>
    </>
};

const QuestionGroupListControl = lazyControlWrapper(() => import("../service-config/QuestionGroupsListControl"));
const QuestionGroupList: FC<MenuProps> = () => {
    useAdminModeMenu("manage questions");
    return <QuestionGroupListControl/>;
};

const QuestionGroup: FC<MenuProps> = () => {
    const params = useParams<{id:string}>()

    useAdminModeMenu("manage questions");

    const questionGroupId = parseInt(params.id)
    const QuestionGroupCtl = useControl(QuestionGroupControl, [questionGroupId])

    return <>
        <MenuArea/>
        <QuestionGroupCtl/>
    </>
};

const OutcomeListControl = lazyControlWrapper(() => import("../service-config/OutcomesListControl"));
const OutcomeList: FC<MenuProps> = () => {
    useAdminModeMenu("manage outcomes");
    return <OutcomeListControl/>;
};

const Outcome: FC<MenuProps> = () => {
    const params = useParams<{id:string}>()

    useAdminModeMenu("manage outcomes");

    const outcomeId = parseInt(params.id)
    const OutcomeCtl = useControl(OutcomeControl, [outcomeId])

    return <>
        <MenuArea/>
        <OutcomeCtl/>
    </>
};


const RiskAreaListControl = lazyControlWrapper(() => import("../service-config/RiskAreasListControl"));
const RiskAreaList: FC<MenuProps> = () => {
    useAdminModeMenu("manage risk outcomes");
    return <RiskAreaListControl/>;
};

const RiskArea: FC<MenuProps> = () => {
    const params = useParams<{id:string}>()

    useAdminModeMenu("manage risk outcomes");

    const riskAreaId = parseInt(params.id)
    const RiskAreaCtl = useControl(RiskAreaControl, [riskAreaId])

    return <>
        <MenuArea/>
        <RiskAreaCtl/>
    </>
};


const SettingAppBar: FC = () => {
    return <AppBarContextProvider>
        <SettingAppBarBase/>
    </AppBarContextProvider>
}
const SettingAppBarBase: FC = () => {
    let {path} = useRouteMatch();
    path = path == "/" ? "/" : path + "/";
    const history = useHistory();
    const {getCommandRepository, sessionData} = useServicesContext();
    const wideMode = path.startsWith("/nav/w/");
    const [cacheConfigText, setCacheConfigText] = useState<string>("clear config caches");
    const [cacheAdminText, setCacheAdminText] = useState<string>("clear admin caches");
    const [cacheAllText, setCacheAllText] = useState<string>("clear all caches");

    const clearConfigCaches = () => {
        const cmd = new CacheCommand(CacheCommand.CONFIG);
        getCommandRepository().sendCommand(cmd)
                .then(() => {
                    setCacheConfigText("... ... cleared config caches");
                    delay(2000).then(() => {
                        setCacheConfigText("clear config caches");
                    })
                })
                .catch((e) => {
                    alert("clear config caches: FAILED: " + e.reason.message); // e.reason.message if wanted to expose to user
                    throw e; // allows .fail to log the error on console and throw for NR
                });
    }

    const clearAdminCaches = () => {
        const cmd = new CacheCommand(CacheCommand.ADMIN);
        getCommandRepository().sendCommand(cmd)
                .then(() => {
                    setCacheAdminText("... ... cleared admin caches");
                    delay(2000).then(() => {
                        setCacheAdminText("clear admin caches");
                    })
                })
                .catch((e) => {
                    alert("clear admin caches: FAILED: " + e.reason.message); // e.reason.message if wanted to expose to user
                    throw e; // allows .fail to log the error on console and throw for NR
                });
    }

    const clearAllCaches = () => {
        const cmd = new CacheCommand(null);
        getCommandRepository().sendCommand(cmd)
            .then(() => {
                setCacheAllText("... ... cleared all caches");
                delay(2000).then(() => {
                    setCacheAllText("clear all caches");
                })
            })
            .catch((e) => {
                alert("clear all caches: FAILED: " + e.reason.message); // e.reason.message if wanted to expose to user
                throw e; // allows .fail to log the error on console and throw for NR
            });
    }

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }
    return <AppBarBase appColour={ctx.appColour}
                       title={"settings overview"}
                       right={<UserMenu extraMenuItems={ctx.extraMenuItems}/>}
                       drawerContent={<MainMenuItems base={path}/>}
                       wideMode={wideMode}
                       onLogoClick={() => history.push("/nav/r/welcome/")}>
        <Switch>
            <Route path={`${path}clearCache`}>
                <div style={{paddingTop: "25px"}}/>
                <Grid container>
                    <Grid item xs={2}>
                    </Grid>
                    <Grid item xs={8}>
                        After some configuration changes clearing the config cache is required, to flush the changes for
                        the end users to pick up.
                        Users pick up a new cache every hour, so it can take up to one hour to see the changes. If you
                        would like to see them
                        sooner, then you will need to reset your own browser/app cache - there are instructions in our
                        help area.
                        <br /><br />
                        <Button onClick={clearConfigCaches}>{cacheConfigText}</Button>

                        <br /><br />
                        <br /><br />
                        After some admin changes clearing the admin cache is required, to flush the changes for
                        the end users to pick up.
                        <br /><br />
                        <Button onClick={clearAdminCaches}>{cacheAdminText}</Button>

                        <br /><br />
                        <br /><br />
                        {sessionData.hasRoleSysAdmin() &&
                            <div>
                                <Button onClick={clearAllCaches}>{cacheAllText}</Button>
                            </div>
                        }
                    </Grid>
                    <Grid item xs={2}>
                    </Grid>
                </Grid>
            </Route>
            <Route path={`${path}features/`}>
                <FeatureList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}listDefinitions`}>
                <ListDefinitionList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route exact path={`${path}servicetypes/:id/:taskName`}>
                <TaskDefinitionSettingsList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route exact path={`${path}servicetypes/:id`}>
                <TaskDefinitionsList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route exact path={`${path}servicetypes/`}>
                <ServiceTypesList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}questiongroups/:id/`}>
                <QuestionGroup setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}questiongroups/`}>
                <QuestionGroupList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}outcomes/:id/`}>
                <Outcome setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}outcomes/`}>
                <OutcomeList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}riskAreas/:id/`}>
                <RiskArea setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}riskAreas/`}>
                <RiskAreaList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route path={`${path}formDefinitions`}>
                <FormDefinitionList setUserMenu={ctx.setExtraMenuItems}/>
            </Route>
            <Route exact path={[`${path}audits`]}>
                <AuditHistory sessionData={sessionData} resource={{links:[{
                        rel: "audit-history",
                        href: `${applicationRootPath}api/config/commands` // TODO: This should be a relation on sessionData resource
                    }]}}/>
            </Route>
            <Route exact path={`${path}`}>
                <div/>
            </Route>
        </Switch>
    </AppBarBase>;
}

export default SettingAppBar;