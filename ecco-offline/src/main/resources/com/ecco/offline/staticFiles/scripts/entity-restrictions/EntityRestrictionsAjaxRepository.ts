import {ApiClient} from "ecco-dto";
import {EntityRestrictionsRepository} from "./EntityRestrictionsRepository"
import { ServiceCategorisation } from "ecco-dto/service-config-dto";
import {ServiceDto as Service} from "ecco-dto/service-config-dto";
import {SessionDataService} from "../feature-config/SessionDataService";


export class EntityRestrictionsAjaxRepository implements EntityRestrictionsRepository {


    constructor(private unused: ApiClient) {
    }

    /**
     * @return {Promise<Service[]>}
     */
    public findRestrictedServicesProjects(filterOutHideOnNew: boolean = false, filterOutHideOnList: boolean = true,
                                          hideDisabledExceptId?: boolean | number): Promise<Service[]> {
        return SessionDataService.getFeatures()
            .then(config => {

                let services = config.getRestrictedServices(hideDisabledExceptId)
                    .filter( service => service.id >= 0);

                if (filterOutHideOnNew) {
                    services = services.filter(service => !config.getServiceTypeById(service.serviceTypeId).isHideOnNew())
                }
                if (filterOutHideOnList) {
                    services = services.filter(service => !config.getServiceTypeById(service.serviceTypeId).isHideOnList())
                }

                return services;
            });
    }

    /**
     * @return {Promise<ServiceCategorisation[]>}
     */
    public findRestrictedServicesCategorisations(filterOutHideOnNew: boolean = false,
                                                 filterOutHideOnList: boolean = true,
                                                 hideDisabledExceptId?: boolean | number): Promise<ServiceCategorisation[]> {
        return SessionDataService.getFeatures()
            .then( config => {

                let serviceCats = config.getRestrictedServiceCategorisations(hideDisabledExceptId)
                    .filter( serviceCat => serviceCat.id >= 0);

                if (filterOutHideOnNew) {
                    serviceCats = serviceCats.filter(serviceCat => !config.getServiceTypeByServiceCategorisationId(serviceCat.id).isHideOnNew())
                }
                if (filterOutHideOnList) {
                    serviceCats = serviceCats.filter(serviceCat => !config.getServiceTypeByServiceCategorisationId(serviceCat.id).isHideOnList())
                }

                return serviceCats;
            });
    }
}
