import $ = require("jquery");

import BaseListEntryControl = require("../controls/BaseListEntryControl");
import ReportDefEditor = require("./FormDefEditor");
import SummarisedElement = require("../controls/SummarisedElement");
import {FormDefinition } from 'ecco-dto';
import {AdminMode} from "@eccosolutions/ecco-common";

class FormDefinitionListEntryControl extends BaseListEntryControl<FormDefinition> implements SummarisedElement<FormDefinition> {

    constructor(entry: FormDefinition) {
        super(entry, "fa fa-cogs");
        AdminMode.bus.addHandler(event => {
            this.setAdminMode(event.enabled);
        });

    }

    protected administerEntry(): void {
        ReportDefEditor.showInModal(this.entry.uuid);
    }

    protected getEditElement(): $.JQuery {
        let title = this.entry.name;
        let subTitle = this.entry.uuid;
        let $a = $("<a>").attr("href", this.entry.uuid + "/")
            .append($("<span>").addClass("clearfix").text(title));
        if (subTitle) {
            $a.append($("<small>").text(" " + subTitle));
        }

        return $a;
    }

    protected getEntryIconClass() {
        return "fa fa-file-o";
    }
    public searchId() {
        return this.entry.uuid;
    }

    public title(): $.JQuery {
        return this.element();
    }

    public body(): $.JQuery {
        return null;
    }

    initiallyHidden():boolean {
        return this.entry.deleted;
    }

    public target() {
        return this.entry;
    }
}
export = FormDefinitionListEntryControl;