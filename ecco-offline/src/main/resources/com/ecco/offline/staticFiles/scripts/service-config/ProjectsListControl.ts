import $ = require("jquery");

import BaseAsyncListControl = require("../controls/BaseAsyncListControl");
import BaseListEntryControl = require("../controls/BaseListEntryControl");
import EditProjectForm = require("./EditProjectForm");
import Project = dto.ProjectDto;
import {apiClient} from "ecco-components";
import * as dto from "ecco-dto/service-config-dto";
import {ProjectAjaxRepository} from "./ProjectAjaxRepository";
import {SessionDataGlobal} from "ecco-dto";

var repository = new ProjectAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<Project> {

    constructor(project: Project) {
        super(project, "fa fa-pencil");
    }

    protected administerEntry(): void {
        EditProjectForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        const $ids = $("<small>").text(`[id:${this.entry.id}]  `);
        const $name = $("<span>").text(this.entry.name);
        const $row = $("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-8").append($name))
                .append($("<div>").addClass("col-xs-4").append($ids))
        return $("<span>").append($row);
    }

    protected getEntryIconClass(): string {
        return "fa fa-home";
    }
}

class ProjectsListControl extends BaseAsyncListControl<Project> {

    constructor() {
        super("add new project", "no projects defined", "fa fa-home");
    }

    protected fetchViewData(): Promise<Project[]> {
        return repository.findAll().then(list => list.sort(SessionDataGlobal.compare));
    }
    protected createItemControl(project: Project) {
        return new EntryControl(project);
    }

    protected addNewEntity() {
        EditProjectForm.showInModal(null);
    };

}
export = ProjectsListControl;
