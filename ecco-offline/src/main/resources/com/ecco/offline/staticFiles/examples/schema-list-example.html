<!DOCTYPE html>
<html>
<head>
    <title>schema-list-example</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../bootstrap/css/bootstrap-theme.min.css">
    <link rel="stylesheet" href="../css/common/common.css">

    <!--
    Use http://localhost:8888/ecco-war/resources/noCache/examples/schema-form-example.html to access
    Schemas at:
    http://localhost:8888/ecco-war/api/$schemas/
    http://localhost:8888/ecco-war/api/referrals/list/$schema/
    -->

    <script src="../scripts/lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../build/";
        var requirejs_devMode = "true";
    </script>
    <script src="../scripts/common/require-boot.js"></script>
    <script>
        require(["elements/ecco-list"]);
    </script>
<!-- Looks like  we can define elements after the page has loaded:
 http://stackoverflow.com/questions/40378115/custom-html-tags-on-page-render-skip-html-parsing-for-some-reason -->
</head>
<body>
    <pre>TODO:
    ecco-list should pass data src to delegate </pre>
    <ecco-list src="referrals/list/?statusGroup=ongoing"></ecco-list>
</body>
</html>
