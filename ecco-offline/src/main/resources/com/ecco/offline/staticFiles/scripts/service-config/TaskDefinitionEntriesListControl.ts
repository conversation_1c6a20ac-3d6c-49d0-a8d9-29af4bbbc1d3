import $ = require("jquery");
import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import events = require("../common/events");

import ActionButton = require("../controls/ActionButton");
import BaseControl = require("../controls/BaseControl");
import CheckboxInput = require("../controls/CheckboxInput");
import Form = require("../controls/Form");

import HtmlElement = require("../controls/HtmlElement");
import Modal = require("../controls/Modal");
import TextInput = require("../controls/TextInput");
import TaskDefinitionEntrySettingsListControl = require ("../service-config/TaskDefinitionEntrySettingsListControl");
import commandsTask = require("./commands");
import ImportCommandForm = require("./ImportCommandForm");
import TaskDefinitionEntryEditor = require("./TaskDefinitionEntryEditor");
import ServiceConfigTaskEntryChangeCommand = commandsTask.ServiceConfigTaskEntryChangeCommand;
import ServiceType = domain.ServiceType;
import TaskDefinitionEntry = domain.TaskDefinitionEntry;
import {AdminMode} from "@eccosolutions/ecco-common";
import {CommandQueue} from "ecco-commands";
import {apiClient} from "ecco-components";
import * as domain from "ecco-dto";
import {
    adminModeEnabled,
    ServiceTypeAjaxRepository,
    SessionData,
    TaskCommandAjaxRepository,
    TaskDefinition
} from "ecco-dto";
import {OutcomeCloneCommand} from "./commands";
import {getCommandQueueRepository, getFeatureConfigRepository} from "ecco-offline-data";
import {clearCacheLinkEnhance} from "../admin/admin-menu-init";
import {ExportType} from "./taskSettingTypes";
import URI from "URI";
import {showTaskDefinitionCreateModal} from "ecco-admin"
import * as ReactDOM from "react-dom";

const serviceTypeRepository = new ServiceTypeAjaxRepository(apiClient.withCachePeriod(0));
const taskCommandRepository = new TaskCommandAjaxRepository(apiClient.withCachePeriod(0));

class EntryControl extends BaseControl {

    private $importExportArea: $.JQuery;
    private copyChk: CheckboxInput;
    private $labelArea: $.JQuery;
    private readonly $valueArea: $.JQuery;
    private orderbyValue: number;

    /**
     * TaskDefinitionEntry's are rendered in the order received from the server - which is set on ServiceType.java referralAspects
     */
    constructor(private sessionData: SessionData, private serviceType: ServiceType, private taskDef: TaskDefinitionEntry,
                private taskDefinitions: TaskDefinition[],
                private generatedCommand: (cmd: ServiceConfigTaskEntryChangeCommand) => void) {

        super( $("<li>") );
        const iconClass = "fa fa-cog";

        this.copyChk = new CheckboxInput("");
        this.$importExportArea = $("<span>");

        let uri = URI(window.location.href)
        const redirect = `${uri}${taskDef.getName()}/`
        this.$labelArea = $("<a>").attr("href", redirect).text(taskDef.getDisplayName());
        this.$valueArea = $("<span>");
        this.orderbyValue = taskDef.getOrderBy();
        this.renderValues(adminModeEnabled());

        this.element().addClass("row")
            .append(
                $("<div>").addClass("col-xs-1").append(
                    $("<span>").addClass("fa fa-lg").addClass(iconClass)
                )
            )
            .append(
                $("<div>").addClass("col-xs-1").append(this.$importExportArea)
            )
            .append(
                $("<div>").addClass("col-xs-6").append(this.$labelArea)
            )
            .append(this.$valueArea);
    }

    public setAdminMode(enabled: boolean) {
        this.renderValues(enabled);
    }

    public indicateAsUnchanged() {
        this.$labelArea.css("background-color", "");
    }

    public indicateAsChanged() {
        this.$labelArea.css("background-color", "yellow");
    }

    public getTaskName() {
        return this.taskDef.getName();
    }

    public getOrderbyValue() {
        return this.orderbyValue;
    }

    public setExportCheckbox(state: boolean) {
        this.copyChk.setChecked(state);
    }

    public exportOriginalAsNewCommands(type: ExportType): CommandQueue {
        const queue = new CommandQueue(getCommandQueueRepository());

        if (this.copyChk.isChecked()) {

            // if type then add associated data early, such as the outcomes
            if (type == "OFFSITE") {
                queue.addQueue(this.exportAssociatedDataAsNewCommands());
            }
            const cmd = new ServiceConfigTaskEntryChangeCommand("add", this.serviceType.id, this.taskDef.getName());
            cmd.changeOrderby(null, this.taskDef.getOrderBy());
            cmd.changeAllowNext(null, this.taskDef.isAllowNext());
            queue.addCommand(cmd);

            this.taskDef.getSettings().forEach( (item) => {
                const cmd = TaskDefinitionEntrySettingsListControl.exportOriginalAsNewCommand(
                        type, this.taskDef.getName(), item.getName(),
                        this.serviceType, item.getValue(), this.taskDefinitions, this.sessionData)
                cmd && queue.addCommand(cmd);
            });
        }
        return queue;
    }

    public exportAssociatedDataAsNewCommands(): CommandQueue {
        const queue = new CommandQueue(getCommandQueueRepository());
        this.serviceType.getOutcomesForTaskName(this.getTaskName(), this.sessionData)
            .map(o => new OutcomeCloneCommand(o.asDto()))
            .forEach(cmd => queue.addCommand(cmd));
        // this.serviceType.getRiskAreas(this.getTaskName())
        //     .map(o => new RiskAreaCloneCommand(o.asDto()))
        //     .forEach(cmd => queue.addCommand(cmd));
        return queue;
    }

    /**
     * Trigger the taskDef to generate the command and update the display.
     * This needs to be public so it can be called from outside the object so it follows the same mechanism as remove,
     * changedOrderby etc, and avoids circular errors.
     */
    public add() {
        const cmd = new ServiceConfigTaskEntryChangeCommand("add", this.serviceType.id, this.taskDef.getName());
        this.generatedCommand(cmd);
    }

    private renderValues(adminMode: boolean) {
        const orderby = this.renderOrderby(adminMode);
        const allowNext = this.renderAllowNext(adminMode);
        const taskSchedule = this.renderTaskDueDateSchedule(adminMode);
        const remove = this.renderDelete(adminMode);
        this.renderExport(adminMode);

        this.$valueArea.empty();
        this.$valueArea.append($("<div>").addClass("col-xs-1").append(orderby));
        this.$valueArea.append($("<div>").addClass("col-xs-1").append(allowNext));
        this.$valueArea.append($("<div>").addClass("col-xs-1").append(taskSchedule));
        this.$valueArea.append($("<div>").addClass("col-xs-1").append(remove));
    }

    private renderExport(adminMode: boolean) {
        this.$importExportArea.empty();
        if (!adminMode) {
            this.$importExportArea.append(this.copyChk.element());
        }
    }

    private renderOrderby(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            return $("<span>").text(this.taskDef.getOrderBy());
        } else {
            const ti = new TextInput("");
            // ti.setMaxLengthAndSize(3);
            ti.setVal(this.taskDef.getOrderBy().toString());
            ti.change((val) => this.changedOrderby(val == "" ? this.taskDef.getOrderBy() : parseInt(val)));
            return ti.element().addClass("form-control");
        }
    }

    private renderAllowNext(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            return $("<span>").text(this.taskDef.isAllowNext() ? "allow next" : "");
        } else {
            const cb = new CheckboxInput("");
            cb.setChecked(this.taskDef.isAllowNext());
            cb.change((val, state) => this.changedAllowNext(state));
            return cb.element();
        }
    }

    private renderTaskDueDateSchedule(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            return $("<span>").text(this.taskDef.getTaskDueDateSchedule());
        } else {
            const ti = new TextInput("");
            ti.setVal(this.taskDef.getTaskDueDateSchedule()?.toString());
            ti.change((val) => this.changedTaskDueDateSchedule(val));
            return ti.element().addClass("form-control");
        }
    }

    private renderDelete(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            return null;
        } else {
            const cb = new ActionButton("del").addClass("btn");
            // <span class="btn btn-danger"><i class="fa fa-trash-o btn-sm"></i></span>
            cb.click(() => {
                this.remove();
                return Promise.resolve(null);
            });
            return cb.element();
        }
    }

    private changedOrderby(orderby: number) {
        this.orderbyValue = orderby;
        const cmd = new ServiceConfigTaskEntryChangeCommand("update", this.serviceType.id, this.taskDef.getName());
        cmd.changeOrderby(this.taskDef.getOrderBy(), orderby);
        this.generatedCommand(cmd);
    }

    private changedAllowNext(allowNext: boolean) {
        const cmd = new ServiceConfigTaskEntryChangeCommand("update", this.serviceType.id, this.taskDef.getName());
        cmd.changeAllowNext(this.taskDef.isAllowNext(), allowNext);
        this.generatedCommand(cmd);
    }

    private changedTaskDueDateSchedule(schedule: string) {
        const cmd = new ServiceConfigTaskEntryChangeCommand("update", this.serviceType.id, this.taskDef.getName());
        cmd.changeTaskDueDateSchedule(this.taskDef.getTaskDueDateSchedule(), schedule);
        this.generatedCommand(cmd);
    }

    private remove() {
        const cmd = new ServiceConfigTaskEntryChangeCommand("remove", this.serviceType.id, this.taskDef.getName());
        this.generatedCommand(cmd);
    }

}

class ModalControl extends HtmlElement {

    private modal: Modal;
    private form = new Form();
    private $input : $.JQuery;
    private footer = new HtmlElement($("<span>"));

    constructor(private textIn: string) {
        super($("<div>"));
        this.modal = new Modal("modal-full");
        this.layoutForm();
        this.constructModal();
    }

    private layoutForm() {
        this.$input = $("<textarea>")
            .addClass("form-control")
            .css('margin-left', 'auto')
            .css('margin-right', 'auto')
            .attr("rows", "20")
            .val(this.textIn);
        this.form.append(this.$input);
        this.element().append($("<div>").text(
                "Take this to the 'import' on the service type desired and the serviceTypeId will be overwritten (eg serviceType and commandUri)"
            ));
        this.element().append(this.form.element());
    }

    private getFooter() {
        return this.footer.element();
    }

    private constructModal() {
        this.modal.title("commands");
        this.modal.setFooter(this.getFooter());
        this.modal.popWithJQueryContent(this.element());
    }

}

// TODO: Extract base AdminCapableListControl from this and ChartListControl
class TaskDefinitionEntriesListControl extends BaseAsyncCommandForm<ServiceType> {

    private controls: EntryControl[] = [];
    private importExportToolbar = $("<div>");
    private $main = $("<div>");
    private $newDefn = $("<li>").addClass("add-entry");
    private $newEntry = $("<li>").addClass("add-entry");
    private $el = $("<ul>").addClass("entry-list list-unstyled");
    private orderbyIncrements = 5;
    private serviceType: ServiceType;
    private taskDefinitions: TaskDefinition[];
    private sessionData: SessionData;
    private footer: $.JQuery;

    constructor(private servicetypeId: number) {
        super("-not used-");

        this.setOnFinished( () => {} );
    }

    protected fetchViewData(): Promise<ServiceType> {
        return getFeatureConfigRepository().getSessionData()
            .then(sd => {
                this.sessionData = sd;
                return taskCommandRepository.getTaskDefinitions()
                    .then(defs => {
                        this.taskDefinitions = defs;
                        return serviceTypeRepository.findOneServiceType(this.servicetypeId);
                    })
            });
    }

    protected render(serviceType: ServiceType): void {

        this.serviceType = serviceType;

        let taskDefEntries: TaskDefinitionEntry[] = serviceType.getTaskDefinitionEntries();

        this.renderImportExportButton();

        this.renderNewEntryButton(taskDefEntries);

        this.renderEntries(taskDefEntries);

        this.footer = super.getFooter();
        this.$main.empty().append(this.$el);
        this.element().empty().append($("<div>").append(this.$main));

        const $nav = $("<div>").addClass("clearfix")
                .append("manage tasks for service type: " + this.serviceType.getName(), this.importExportToolbar, this.footer);
        events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $nav) );

        this.setAdminMode(adminModeEnabled());

        AdminMode.bus.addHandler(event => {
            this.setAdminMode(event.enabled);
            this.controls && this.controls.forEach( (item) => item.setAdminMode(event.enabled) );
        });

        clearCacheLinkEnhance();
    }

    private renderEntries(taskDefEntries: TaskDefinitionEntry[]) {

        if (taskDefEntries.length == 0) {
            this.$el.append( $("<li>").text("no task definition entries defined") );
        }
        else {
            this.controls = taskDefEntries.map( td => this.createEntryControl(this.serviceType, td) );
            this.$el.append( this.controls.map(control => control.element() ) );
        }

    }

    private renderImportExportButton() {
        const imp = new ActionButton("import").addClass("btn btn-xs");
        imp.autoDisable(false);
        imp.clickSynchronous(() => {
            this.importCommandsDialog();
        });

        const copyChkAll = new CheckboxInput(null);
        copyChkAll.element().css("margin", "0px");
        copyChkAll.change((value: string, state: boolean) => this.checkAllToggle(state));
        const cpy = new ActionButton("export on-site").addClass("btn btn-xs");
        cpy.autoDisable(false);
        cpy.clickSynchronous(() => {
            this.exportAsNewCommands("ONSITE");
        });
        let exp = new ActionButton("export off-site").addClass("btn btn-xs");
        exp.autoDisable(false);
        exp.clickSynchronous(() => {
            this.exportAsNewCommands("OFFSITE");
        });
        let confOnly = new ActionButton("config only").addClass("btn btn-xs");
        confOnly.autoDisable(false);
        confOnly.clickSynchronous(() => {
            this.exportAsNewCommands("CONFIG_ONLY");
        });

        const clearConfigCache = `<a id="cache-configuration-link">clear config caches</a>`;
        this.importExportToolbar = $("<div>").addClass("container-fluid")
                    .append(
                        $("<div>").addClass("row")
                        .append($("<div>").addClass("col-xs-1")
                            .append(imp.element())
                        )
                        .append($("<div>").addClass("col-xs-1")
                            .append(
                                $("<span>").addClass("btn-group").append(copyChkAll.element()).append(cpy.element()).append(exp.element()).append(confOnly.element())
                            )
                        )
                    )
                    .append(
                        $("<div>").addClass("row").append($("<div>").addClass("col-xs-3").html(clearConfigCache))
                    );
    }

    private checkAllToggle(state: boolean) {
        this.controls && this.controls.forEach(item => {
            item.setExportCheckbox(state);
        });
    }

    private setAdminMode(adminMode: boolean) {
        if (adminMode) {
            this.importExportToolbar.hide();
        } else {
            this.importExportToolbar.show();
        }
        if (adminMode) {
            this.$newDefn.show();
            this.$newEntry.show();
            this.footer.show();
        } else {
            this.$newDefn.hide();
            this.$newEntry.hide();
            this.footer.hide();
        }
    }

    public static showInReactModal() {
        //const form: BaseEvidenceForm = new SmartStepEvidenceForm(referral, [], evidenceDef, title);
        let $modalEl = $("#modal-for-form");
        if ($modalEl.length == 0) {
            $modalEl = $("<div>").appendTo($("body"));
        }
        const unmount = () => { ReactDOM.unmountComponentAtNode($modalEl[0])};
        showTaskDefinitionCreateModal($modalEl[0], unmount);
    }

    private renderNewEntryButton(taskDefEntries: TaskDefinitionEntry[]) {

        const lastOrderby = taskDefEntries.length > 0
                ? taskDefEntries[taskDefEntries.length - 1].getOrderBy()
                : this.orderbyIncrements;

        this.$newDefn
                .append( $("<i>").addClass("fa fa-file-code-o") )
                .append( $("<span>").text(" create task definition") )
                .hide()
                .click( () => {
                    TaskDefinitionEntriesListControl.showInReactModal();
                });

        this.$newEntry
            .append( $("<i>").addClass("fa fa-file-code-o") )
            .append( $("<span>").text(" add task definition") )
            .hide()
            .click( () => {
                TaskDefinitionEntryEditor.showInModal(
                    (taskNames: string[]) => this.createNewTaskDefinitonEntries(lastOrderby, taskNames),
                    this.servicetypeId,
                    null,
                    taskDefEntries);
                return Promise.resolve(null);
            });
        this.$el.append( this.$newDefn);
        this.$el.append( this.$newEntry);
    }

    private createNewTaskDefinitonEntries(lastOrderby: number, taskNames: string[]) {
        taskNames.forEach((taskName) => {
            lastOrderby += this.orderbyIncrements;
            const taskDefEntry = new TaskDefinitionEntry({
                name: taskName,
                orderby: lastOrderby,
                allowNext: false,
                dueDateSchedule: null,
                settings: {},
                outcomeSettings: {}
            }, this.sessionData.getDto().messages);
            const ctl = this.createEntryControl(this.serviceType, taskDefEntry);
            this.controls.push(ctl);
            this.$el.append(ctl.element());
            ctl.add();
        });
    }

    private createEntryControl(serviceType: ServiceType, taskDefEntry: TaskDefinitionEntry): EntryControl {
        return new EntryControl(this.sessionData, serviceType, taskDefEntry, this.taskDefinitions,
            (cmd: ServiceConfigTaskEntryChangeCommand) => this.processCommand(cmd));
    }

    private processCommand(cmd: ServiceConfigTaskEntryChangeCommand) {
        if (!cmd.hasChanges) {
            return;
        }

        this.commandQueue.addCommand(cmd);

        this.renderCommand(cmd);
    }

    private renderCommand(cmd: ServiceConfigTaskEntryChangeCommand) {

        const ctrl = this.controls.filter(ctrl => ctrl.getTaskName() == cmd.getTaskName())[0];

        if (cmd.isDelete()) {
            ctrl.element().hide();
        } else {
            ctrl.indicateAsChanged();
            const newOrderby = cmd.getOrderbyChange()?.to || ctrl.getOrderbyValue();
            const newIndex = this.findNewIndexInElements(newOrderby) + 1;
            const callback = () => {
                ctrl.element().insertAfter(ctrl.element().siblings(':eq(' + newIndex + ')'));
            };
            ctrl.element().slideUp(500, callback).slideDown(500, callback);
        }

        if (this.commandQueue.size() > 0) {
            this.enableSubmit();
        } else {
            // turn off the css changes if there is nothing to save (eg merged cmd results in nothing)
            this.disableSubmit();
            this.controls && this.controls.forEach(item => item.indicateAsUnchanged() );
        }
    }

    private findNewIndexInElements(newOrderby: number) {
        return this.controls
            .filter(ctrl => ctrl.getOrderbyValue() < newOrderby)
            .length;
    }

    private importCommandsDialog() {
        ImportCommandForm.showInModal(this.servicetypeId);
    }

    private exportAsNewCommands(type: ExportType) {
        const queue = new CommandQueue(getCommandQueueRepository());
        this.controls && this.controls.forEach(item => {
            queue.addQueue(item.exportOriginalAsNewCommands(type));
        });
        queue.addQueue(this.commandQueue);

        const cmdsAsStrArrQ = queue.getCommands()
                .then(cmds => cmds.map(cmd => JSON.stringify(cmd.toCommandDto())));

        cmdsAsStrArrQ.then(cmdsAsStrArr => {
            const cmdsAsStr = '[' + cmdsAsStrArr.join() + ']';
            new ModalControl(cmdsAsStr);
        });

    }

}
export = TaskDefinitionEntriesListControl;
