import $ = require("jquery");
import _ = require("lodash");
import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import BaseAsyncListControl = require("../controls/BaseAsyncListControl");
import BaseAsyncTableControl = require("../controls/BaseAsyncTableControl");
import BaseListEntryControl = require("../controls/BaseListEntryControl");
import BaseTableRowControl = require("../controls/BaseTableRowControl");
import Checkbox = require("../controls/Checkbox");
import {EccoDate, EccoDateTime, StringToObjectMap} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {InvoiceDto, InvoiceLineDto, InvoicesAjaxRepository} from "ecco-dto";

var repository = new InvoicesAjaxRepository(apiClient);


class InvoiceLineWorkRowControl extends BaseTableRowControl<InvoiceLineDto> {

    constructor(private invoiceLine: InvoiceLineDto,
                private checkboxChange: (workUuid: string) => void,
                private checkboxValue: boolean) {
            super(invoiceLine);
    }

    protected override getColumnMapping(): StringToObjectMap<(dto: InvoiceLineDto) => string|$.JQuery> {
        return {
            //"line-id":      item => item.lineUuid,
            "assign":           item => this.createButton(item),
            "unassign":         item => this.createButton(item),
            "planned date":     item => EccoDateTime.iso8601ToFormatShort(item.plannedDate),
            "planned mins":     item => item.plannedMinutes && item.plannedMinutes.toString(),
            "actual w-id":      item => item.workUuid,
            "actual date":      item => EccoDateTime.iso8601ToFormatShort(item.workDate),
            "actual mins":      item => item.workMinutesSpent && item.workMinutesSpent.toString(),
            "type":             item => item.type,
            "description":      item => item.description,
            "netAmount":        item => item.netAmount && item.netAmount.toString(),
            "taxRate":          item => item.taxRate && item.taxRate.toString()
        };
    }

    private createButton(item: InvoiceLineDto) {
        let btn = new Checkbox(item.workUuid,
            (source: Checkbox, id: string) => this.checkboxChange(item.workUuid),
            this.checkboxValue);
        return btn.element();
    }

}
class InvoiceLineWorkListControl extends BaseAsyncTableControl<InvoiceLineDto> {

    constructor(private workLines: InvoiceLineDto[], private uninvoiced: boolean,
                private moveInvoiceLine: (workUuid: string) => void) {
        super();
    }

    protected fetchViewData(): Promise<InvoiceLineDto[]> {
        return Promise.resolve(this.workLines);
    }

    protected getHeaders() { // OVERRIDES default impl
        let headers = this.uninvoiced ? ["assign"] : ["unassign"];
        headers.push("planned date", "planned mins", "actual w-id", "actual date", "actual mins", "type", "description", "netAmount", "taxRate");
        return headers;
    }

    protected createRowControl(workLine: InvoiceLineDto) {
        return new InvoiceLineWorkRowControl(workLine,
            (workUuid: string) => this.moveInvoiceLine(workUuid),
            !this.uninvoiced);
    }

}


class InvoiceLineAdjustmentControl extends BaseListEntryControl<InvoiceLineDto> {

    constructor(invoiceLine: InvoiceLineDto) {
        super(invoiceLine, "fa fa-pencil");
    }

    protected administerEntry(): void {
        // TODO edit
    }

    protected getEditElement(): $.JQuery {
        return $("<span>").text("edit"); //$("<a>").attr("href", this.entry.buildingId + "/").text(this.entry.name)
    }

    protected getEntryIconClass(): string {
        return "fa fa-file-o";
    }
}

class InvoiceLineAdjustmentListControl extends BaseAsyncListControl<InvoiceLineDto> {

    constructor(private adjustmentLines: InvoiceLineDto[]) {
        super("add new adjustment", "no adjustments defined", "fa fa-cogs");
    }

    protected fetchViewData(): Promise<InvoiceLineDto[]> {
        return Promise.resolve(this.adjustmentLines);
    }

    protected createItemControl(adjustmentLine: InvoiceLineDto) {
        return new InvoiceLineAdjustmentControl(adjustmentLine);
    }

    protected addNewEntity() {
        // TODO new
    };

}


class BackingData {
    constructor(public invoice: InvoiceDto,
                public uninvoiced: InvoiceLineDto[]) {
    }
}

/**
 * A control to add/edit invoice lines (and overall status).
 * In the spirit of invoices being read only with adjustments, we use a table for the lines (eg controls/ReferralsListControl)
 * and entity based for adjustments (eg BaseAsyncListControl)
 */
class ServiceRecipientInvoiceControl extends BaseAsyncDataControl<BackingData> {

    constructor(private invoiceId: number) {
        super();
    }

    private originalInvoiceLines: InvoiceLineDto[];
    private originalUninvoicedLines: InvoiceLineDto[];

    protected fetchViewData(): Promise<BackingData> {
        return repository.findOneInvoice(this.invoiceId)
            .then(invoice => {
                return repository.findUninvoicedByServiceRecipientIdAndEndDate(invoice.serviceRecipientId,
                        EccoDate.parseIso8601(invoice.invoiceDate))
                    .then(uninvoiced => {
                        this.originalInvoiceLines = invoice.lines.slice(0); // shallow clone
                        this.originalUninvoicedLines = uninvoiced.slice(0); // shallow clone
                        return new BackingData(invoice, uninvoiced);
                    })
            });
    }

    protected render(data: BackingData) {
        this.element().empty();
        this.element().append($("<h1>").text("invoiced items"));
        let workLinesControl = new InvoiceLineWorkListControl(this.findWorkLines(data), false,
            (workUuid: string) => this.moveInvoiceLine(data, workUuid));
        this.element().append(workLinesControl.element());
        let adjustmentLinesControl = new InvoiceLineAdjustmentListControl(this.findAdjustmentLines(data));
        this.element().append(adjustmentLinesControl.element());
        this.element().append($("<br><br>"));
        this.element().append($("<h1>").text("uninvoiced items"));
        let uninvoicedLinesControl = new InvoiceLineWorkListControl(this.findUninvoicedWorkLines(data), true,
            (workUuid: string) => this.moveInvoiceLine(data, workUuid));
        this.element().append(uninvoicedLinesControl.element());
        workLinesControl.load();
        adjustmentLinesControl.load();
        uninvoicedLinesControl.load();
    }

    private findWorkLines(data: BackingData): InvoiceLineDto[] {
        return data.invoice.lines.filter(line => line.workUuid != null);
    }
    private findUninvoicedWorkLines(data: BackingData): InvoiceLineDto[] {
        var diffWorkUuid = _.difference(data.uninvoiced.map(work => work.workUuid),
                                        data.invoice.lines.map(work => work.workUuid));
        return data.uninvoiced.filter(work => diffWorkUuid.indexOf(work.workUuid) > -1);
    }
    private findAdjustmentLines(data: BackingData): InvoiceLineDto[] {
        return data.invoice.lines.filter(line => line.workUuid == null);
    }

    private moveInvoiceLine(data: BackingData, workUuid: string) {
        let assignedItem = data.invoice.lines.filter(line => line.workUuid == workUuid).pop();
        if (assignedItem) {
            // remove from invoiced
            data.invoice.lines = data.invoice.lines.filter(line => {
                return line.workUuid != assignedItem.workUuid;
            });
        }
        if (!assignedItem) {
            let unassignedItem = data.uninvoiced.filter(line => line.workUuid == workUuid).pop();
            // add to invoiced
            data.invoice.lines.push(unassignedItem);
        }
        this.render(data);
    }

}
export = ServiceRecipientInvoiceControl;
