import {useMemo} from "react";
import {EntityRestrictionsAjaxRepository} from "./EntityRestrictionsAjaxRepository";
import {usePromise, useServicesContext} from "ecco-components";

export function useRestrictedServices() {
    const {apiClient} = useServicesContext()
    const repository = useMemo(() => new EntityRestrictionsAjaxRepository(apiClient), [apiClient])
    const {resolved, error, loading} = usePromise(() => repository.findRestrictedServicesProjects(), []);
    return {services: resolved, error, loading};
}

// also see ServiceCategorisationSelect
export function useRestrictedServiceCategorisations() {
    const {apiClient} = useServicesContext()
    const repository = useMemo(() => new EntityRestrictionsAjaxRepository(apiClient), [apiClient])
    const {resolved, error, loading} = usePromise(() => repository.findRestrictedServicesCategorisations(), []);
    return {serviceCats: resolved, error, loading};
}
