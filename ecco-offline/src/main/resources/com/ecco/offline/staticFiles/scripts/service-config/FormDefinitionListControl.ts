import {Bloodhound, BloodhoundConfig} from "bloodhound";

import {withAuthErrorHandler} from "ecco-offline-data";
import {ResizeEvent} from "@eccosolutions/ecco-common";
import {FormDefAjaxRepository} from "./FormDefAjaxRepository"
import FormDefinitionListEntryControl = require("./FormDefinitionListEntryControl");
import ListContainerControl = require("../controls/data/ListContainerControl");
import FormDefEditor from "./FormDefEditor";
import SearchableListControl = require("../controls/SearchableListControl");
import SearchableListControlOptions = require("../controls/SearchableListControlOptions");
import {FormDefinition} from "ecco-dto";

var repository = FormDefAjaxRepository.instance;


class FormDefinitionListControlOptions implements SearchableListControlOptions<FormDefinition> {

    constructor() {
    }

    public placeholderText = "enter part of the form name";

    /** Create a control for the given item */
    public createControl(item: FormDefinition): FormDefinitionListEntryControl {
        return new FormDefinitionListEntryControl(item);
    }

    public loadInner(callback: (items: FormDefinition[]) => void): void {
        withAuthErrorHandler(repository.findAllFormDefinitions())
        .then( items => {
            callback(items);
            ResizeEvent.bus.fire();
        });
    }

    public generateKey(item: FormDefinition) {
        return String(item.uuid);
    }

    public addEntryText = "define new form";
    public addEntryIconClasses = "fa fa-cogs";
    public addEntryIsAdminOnly = true;

    public addNewEntry() {
        FormDefEditor.showInModal(null);
    }

    public generateSearchConfig(items: FormDefinition[]) {
        var searchConfig: BloodhoundConfig<FormDefinition> = {
            datumTokenizer: (form: FormDefinition) => {
                var result = new Array<string>();
                result.push.apply(result, Bloodhound.tokenizers.nonword(form.name));
                result.push.apply(result, Bloodhound.tokenizers.nonword(form.uuid));
                return result;
            },
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            local: items,
            limit: 20
        };
        return searchConfig;
    }
}

class FormDefinitionListControl extends SearchableListControl<FormDefinition> {

    constructor() {
        super(ListContainerControl, new FormDefinitionListControlOptions());
    }

}
export default FormDefinitionListControl;