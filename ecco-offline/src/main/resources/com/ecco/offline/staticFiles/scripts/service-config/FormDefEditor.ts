
import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import commands = require("./commands");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import {showFormInModalDom} from "../components/MUIConverterUtils";
import TextAreaInput = require("../controls/TextAreaInput");
import TextInput = require("../controls/TextInput");
import {Uuid} from "@eccosolutions/ecco-crypto";
import {FormDefAjaxRepository} from "./FormDefAjaxRepository";
import {FormDefinition} from "ecco-dto";

const repository = FormDefAjaxRepository.instance;


/**
 * CLONE of ReportDefEditor.
 */
class FormDefEditor extends BaseAsyncCommandForm<FormDefinition> {

    public static showInModal(uuid: string) {
        var form = new FormDefEditor(uuid);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form();
    private definition = new TextAreaInput("definition", 25);
    private name = new TextInput("name");
    private orderby = new TextInput("order by");
    private origDto: FormDefinition;


    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private uuid: string) {
        super("Admin: Edit Form Definition");
        this.definition.element().addClass("input-sm");
    }

    protected fetchViewData(): Promise<FormDefinition> {
        if (this.uuid) {
            return repository.findFormDefinitionDefByUuid(this.uuid);
        }
        else {
            return Promise.resolve(null);
        }
    }

    protected render(formDto: FormDefinition) {
        this.origDto = formDto;

        if (formDto) {
            this.name.setVal(formDto.name);
            this.orderby.setVal(formDto.orderby.toString());
            this.definition.setVal(JSON.stringify(formDto.definition, null, '    '));
        }
        this.form.append( new InputGroup("name", this.name).enableValidation() );
        this.form.append( new InputGroup("order", this.orderby).enableValidation() );
        this.form.append( new InputGroup("definition", this.definition).enableValidation() );

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        var cmd;
        try {
            const parsedDef = JSON.parse(this.definition.val());
        } catch (e) {
            alert("Not saving due to parse error: " + e);
            return Promise.reject<void>(e);
        }
        if (this.origDto) {
            var origDefJson = JSON.stringify(this.origDto.definition, null, '    ');
            cmd = new commands.FormDefChangeCommand("update", Uuid.randomV4(), Uuid.parse(this.uuid))
                .changeName(this.origDto.name, this.name.val())
                .changeDefinition(origDefJson, this.definition.val())
                .changeOrderby(this.origDto.orderby, parseInt(this.orderby.val()));
        }
        else {
            cmd = new commands.FormDefChangeCommand("add", Uuid.randomV4(), Uuid.randomV4())
                .changeName(null, this.name.val())
                .changeDefinition(null, this.definition.val())
                .changeOrderby(null, parseInt(this.orderby.val()));
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }
}

export = FormDefEditor;
