import $ = require("jquery");

import BaseCommandForm = require("../cmd-queue/BaseCommandForm");
import CheckboxList = require("../controls/CheckboxList");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextInput = require("../controls/TextInput");
import commands = require("./commands");
import Action = serviceConfigDomain.Action;
import ActivityType = serviceConfigDtos.ActivityType;
import {apiClient} from "ecco-components";
import * as serviceConfigDomain from "ecco-dto";
import * as serviceConfigDtos from "ecco-dto/service-config-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {GroupSupportActivityTypeAjaxRepository} from "ecco-dto";

var activityTypeRepository = new GroupSupportActivityTypeAjaxRepository(apiClient);


class EditActionDefForm extends BaseCommandForm {  // Used for smartsteps/AddGoalForm - test at https://test.eccosolutions.co.uk/testdaycentre/

    public static showInModal(action: Action, serviceId?: number) {
        var form = new EditActionDefForm(action, serviceId);
        showFormInModalDom(form);
    }


    private form = new Form();
    private name = new TextInput("name");
    private $activitySubForm = $("<div>");


    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private action: Action, private serviceId: number) {
        super("System Admin: Edit Goal Definition");
        this.name.setVal(this.action.getName());
        this.form.append( new InputGroup("name", this.name) );

        if (this.serviceId) {
            this.form.append(this.getActivitySubform());
        }
        else {
            this.form.append( $("<p>").text("To configure activities a service is needed, as these relate to a service, not service type."
                + " Please edit via the Add Goal hidden action (click on 'Activities for this goal')") );
        }
        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.append(this.form);
    }


    private getActivitySubform() {
        // Create control and kick off request to populate it with associatedActivities
        var activitiesControl = new CheckboxList( (id, checked) => this.activityChange(id, checked) );
        activitiesControl.element().addClass("input-group");
        var allTypesForService = activityTypeRepository.findActivityTypesByServiceId(this.serviceId);

        allTypesForService.then( (allActivities: ActivityType[]) => {
                var selectedActivities = this.action.activityTypes;
                activitiesControl.populateFromList(allActivities.sort( (a, b) => a.name.localeCompare(b.name) ),
                    (activity) => ({key: activity.id.toString(), value : activity.name}),
                    selectedActivities.map( (activity) => activity.id.toString()) );
            });

        this.$activitySubForm
            .empty()
            .addClass("alert alert-warning")
            .append(
                $("<h4>")
                    .text("Select which activities may be suitable for fulfilling this goal"))
            .append(activitiesControl.element());
        return this.$activitySubForm;
    }

    private activityChange(gsatId: string, checked: boolean) {
        var cmd = new commands.ActionDefActivityAssociationChangeCommand(checked ? "add" : "remove",
            this.serviceId, this.action.getId(), parseInt(gsatId));
        this.commandQueue.addCommand(cmd);
    }

    protected override submitForm(): Promise<void> {
        var cmd = commands.StringChangeCommand.createActionDefNameChange(this.action.getId())
            .changeName(this.action.getName(), this.name.val());

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }
}

export = EditActionDefForm;
