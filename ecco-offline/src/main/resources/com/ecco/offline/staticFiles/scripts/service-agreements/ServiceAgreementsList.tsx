import * as React from "react"

import {ServiceAgreementsView, withCommandForm} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";

/**
 * Command-based editing of an agreement.
 * This is currently called from TaskControl. It did load the same ServiceAgreementsListControl as per other hooks (see this comment)
 * but now loads <ServiceAgreementsView>
 */
export const AgreementsListEditor = (props: {serviceRecipientId: number, taskHandle?: string | undefined}) => {
    // NB AgreementsList is not a form, but we like the flexibility of possiblyModal
    // although possiblyModalForm does need a wrapper, so we need to use a modal directly
    return withCommandForm(commandForm => // FIXME: This one is just a plain list - commandForm isn't used by the whole list
        possiblyModalForm(
            "visit schedules",
            true, true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm(),
            commandForm.getErrors().length > 0, // TODO: Doesn't work dynamically as it needs to be a proper component
            true,
            <ServiceAgreementsView
                serviceRecipientId={props.serviceRecipientId}
            />
        )
    );
};
