# Fix for ClassCastException in advancedChargesForBldgAddressChanges

## Problem
The test `advancedChargesForBldgAddressChanges` was failing with:
```
class java.util.LinkedHashMap cannot be cast to class com.ecco.buildings.dom.FixedContainer$ChargeCategoryInfo
```

## Root Cause
When <PERSON> deserializes JSON data through Hibernate's `JSONUserTypeStringToObjectMap`, it creates `LinkedHashMap` objects instead of `ChargeCategoryInfo` objects. This happens because <PERSON> doesn't have proper type information to deserialize the nested objects correctly.

## Solution (Clean Approach - No instanceof checks)
Created a specialized Hibernate UserType that provides <PERSON> with the exact type information needed for proper deserialization.

## Changes Made

### 1. Created JSONUserTypeChargeCategoryInfoList
- Located in `ecco-buildings/src/main/java/com/ecco/buildings/hibernate/`
- Specialized Hibernate UserType for `Map<String, List<ChargeCategoryInfo>>`
- Provides <PERSON> with exact type information via JavaType construction
- Eliminates the need for runtime type checking or casting

### 2. Updated FixedContainer field annotation
- Changed from generic `JSONUserTypeStringToObjectMap`
- To specialized `com.ecco.buildings.hibernate.JSONUserTypeChargeCategoryInfoList`
- This ensures proper deserialization at the Hibernate level

### 3. Simplified getChargeCategoryCombinations() method
- Removed all instanceof checks and type conversion logic
- Clean, simple implementation that trusts the UserType to handle deserialization
- No runtime type checking needed

### 4. Updated field type
- Changed from `Map<String, ChargeCategoryInfo[]>` to `Map<String, List<ChargeCategoryInfo>>`
- Lists are more compatible with JSON serialization/deserialization

## Benefits
- **Type Safety**: Proper deserialization at the Hibernate level
- **Performance**: No runtime type checking or conversion
- **Maintainability**: Clean code without defensive programming
- **Reliability**: Jackson gets exact type information for proper deserialization

## Testing
The fix should resolve the ClassCastException by ensuring Jackson deserializes the JSON data directly into `ChargeCategoryInfo` objects rather than `LinkedHashMap` objects.
