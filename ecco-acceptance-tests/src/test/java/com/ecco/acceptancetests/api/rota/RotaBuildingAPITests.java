package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.infrastructure.time.Clock;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.taskFlow.ReferralTaskClientResidenceCommandDto;
import kotlin.Pair;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Rota endToEnd test for appointments to workers. Also see RotaReferralAPITests.
 * <ol>
 *     <li>create care run appointments (= demand), to be allocated to workers availability (= resource) on the live rota</li>
 *     <li>create referral appointments (= demand), to be allocated to care runs availability (= resource) on the run builder</li>
 *     <li>create workers availability (= resource)</li>
 *     <li>create care run availability (= resource), for the referral appointments (= demand) on the run builder</li>
 *     <li>allocate care run appointments (= demand) to workers (= resource) on the live rota</li>
 *     <li>allocate workers (= resource) to a referral appointments (= demand) - NB this need to be happen, but we have to manually currently</li>
 * </ol>
 */
@SuppressWarnings({"unused"})
public class RotaBuildingAPITests extends BaseJsonTest {

    private Clock clock = Clock.DEFAULT;
    protected final org.joda.time.DateTime now = clock.now();
    protected final java.time.ZonedDateTime nowJdk = clock.nowJdk();


    /**
     * BUILDING CONFIG on -100
     * NB config from Building checks (ecco system config).odt
     * and from ql
     */
    @Test
    @Disabled("only for local setup for now")
    public void configSetupBuilding() {
        // This will only fail if you have not started the server with -Ddb.extraContexts=acceptanceTests

        // QL CONFIG
        // ========
        /*
        TO IMPORT to -100
        overview / communication log (on site)
        [{"uuid":"af26fab0-a274-4f73-7ac2-e31e6264d4c5","commandUri":"service-config/-100/task/referralView/","timestamp":"2023-09-20T10:33:58.286Z","operation":"add","serviceTypeId":-100,"taskName":"referralView","orderbyChange":{"from":null,"to":5},"allowNextChange":{"from":null,"to":true}},{"uuid":"40a23d4f-4db5-46e3-7ec6-7af394b243be","commandUri":"service-config/-100/task/referralView/setting/extraTabs/","timestamp":"2023-09-20T10:33:58.287Z","serviceTypeId":-100,"taskName":"referralView","settingName":"extraTabs","valueChange":{"from":null,"to":"supportStaffNotes"}},{"uuid":"1817377a-978c-4b89-7a40-a8b9a389fc7c","commandUri":"service-config/-100/task/supportStaffNotes/","timestamp":"2023-09-20T10:33:58.287Z","operation":"add","serviceTypeId":-100,"taskName":"supportStaffNotes","orderbyChange":{"from":null,"to":10},"allowNextChange":{"from":null,"to":true}},{"uuid":"cb668774-9a60-43e5-7564-f3a394693f59","commandUri":"service-config/-100/task/supportStaffNotes/setting/commentWidth/","timestamp":"2023-09-20T10:33:58.287Z","serviceTypeId":-100,"taskName":"supportStaffNotes","settingName":"commentWidth","valueChange":{"from":null,"to":"wide"}},{"uuid":"eb5523d9-09d3-4068-7d3a-e81b1c419394","commandUri":"service-config/-100/task/supportStaffNotes/setting/showOutcomes/","timestamp":"2023-09-20T10:33:58.287Z","serviceTypeId":-100,"taskName":"supportStaffNotes","settingName":"showOutcomes","valueChange":{"from":null,"to":"none"}},{"uuid":"9e95898a-af9a-4f49-7f48-046cdaf6cdd9","commandUri":"service-config/-100/task/supportStaffNotes/setting/titleRaw/","timestamp":"2023-09-20T10:33:58.287Z","serviceTypeId":-100,"taskName":"supportStaffNotes","settingName":"titleRaw","valueChange":{"from":null,"to":"communication log"}},{"uuid":"809205ee-7be9-4d87-7613-04ddb3f94968","commandUri":"service-config/-100/task/supportStaffNotes/setting/tookPlaceOn/","timestamp":"2023-09-20T10:33:58.287Z","serviceTypeId":-100,"taskName":"supportStaffNotes","settingName":"tookPlaceOn","valueChange":{"from":null,"to":"dateTime"}},{"uuid":"68f57d0e-2085-4437-7edb-74f9ca61f219","commandUri":"service-config/-100/task/supportStaffNotes/setting/embeddedComponents/","timestamp":"2023-09-20T10:33:58.288Z","serviceTypeId":-100,"taskName":"supportStaffNotes","settingName":"embeddedComponents","valueChange":{"from":null,"to":"history"}},{"uuid":"44dcfdd4-a7f0-4db3-712b-0b9e7d932ab5","commandUri":"service-config/-100/task/supportStaffNotes/setting/showMenus/","timestamp":"2023-09-20T10:33:58.288Z","serviceTypeId":-100,"taskName":"supportStaffNotes","settingName":"showMenus","valueChange":{"from":null,"to":"overview"}}]
        TODO team meetings - has a qnr
        needs attachment (on site)
        [{"uuid":"6902b4e9-43f0-485a-77c2-3a2d3ec4e27f","commandUri":"service-config/-100/task/needsAttachment/","timestamp":"2023-09-20T10:34:47.038Z","operation":"add","serviceTypeId":-100,"taskName":"needsAttachment","orderbyChange":{"from":null,"to":15},"allowNextChange":{"from":null,"to":true}},{"uuid":"669ca047-6ff6-4d7e-7f83-aed0fa9123fa","commandUri":"service-config/-100/task/needsAttachment/setting/commentWidth/","timestamp":"2023-09-20T10:34:47.039Z","serviceTypeId":-100,"taskName":"needsAttachment","settingName":"commentWidth","valueChange":{"from":null,"to":"wide"}},{"uuid":"877a21d3-0432-4912-70a9-e0cc8558e4fd","commandUri":"service-config/-100/task/needsAttachment/setting/showOutcomes/","timestamp":"2023-09-20T10:34:47.039Z","serviceTypeId":-100,"taskName":"needsAttachment","settingName":"showOutcomes","valueChange":{"from":null,"to":"none"}},{"uuid":"71279034-332e-4730-7c75-2c763ef5f6fb","commandUri":"service-config/-100/task/needsAttachment/setting/tookPlaceOn/","timestamp":"2023-09-20T10:34:47.039Z","serviceTypeId":-100,"taskName":"needsAttachment","settingName":"tookPlaceOn","valueChange":{"from":null,"to":"date"}},{"uuid":"594e047e-5803-43d8-7698-3037c55e2480","commandUri":"service-config/-100/task/needsAttachment/setting/titleRaw/","timestamp":"2023-09-20T10:34:47.039Z","serviceTypeId":-100,"taskName":"needsAttachment","settingName":"titleRaw","valueChange":{"from":null,"to":null}},{"uuid":"ce59d667-**************-149f90844d15","commandUri":"service-config/-100/task/needsAttachment/setting/showCommentComponents/","timestamp":"2023-09-20T10:34:47.040Z","serviceTypeId":-100,"taskName":"needsAttachment","settingName":"showCommentComponents","valueChange":{"from":null,"to":"attachments"}},{"uuid":"c791e07c-1af3-4a7c-7f25-e9c0f23b240c","commandUri":"service-config/-100/task/needsAttachment/setting/showVisualStyleAs/","timestamp":"2023-09-20T10:34:47.040Z","serviceTypeId":-100,"taskName":"needsAttachment","settingName":"showVisualStyleAs","valueChange":{"from":null,"to":"tabular"}},{"uuid":"ad222d82-1fa0-4192-7c2a-73682592bef8","commandUri":"service-config/-100/task/needsAttachment/setting/showMenus/","timestamp":"2023-09-20T10:34:47.040Z","serviceTypeId":-100,"taskName":"needsAttachment","settingName":"showMenus","valueChange":{"from":null,"to":"attachments"}}]
        regular checks (export off site)
        [{"uuid":"97322959-27d9-4cf7-7c90-975579ffa1aa","commandUri":"config/outcome/clone/","timestamp":"2023-09-20T10:35:20.686Z","outcomeViewModel":{"id":111875,"uuid":"b2c0cf6d-cd62-4925-75fe-19de95de9791","name":"daily checks","actionGroups":[{"id":111876,"uuid":"5d7d4d94-0866-460c-70aa-e0930a6a0818","name":"daily cleaning tasks  ","actions":[{"id":111877,"uuid":"b30379a1-d21c-4772-783e-55c7cf6d3c32","name":"daily balance checks"},{"id":111883,"uuid":"edad6b86-1988-40fb-7e87-b531ae7ed9c2","name":"check service emails and landline phone messages"},{"id":111878,"uuid":"ed377363-9b1b-4ccb-7426-37a2e3e44790","name":"read communication book"},{"id":111879,"uuid":"53488c7c-b07e-4184-752f-c3ad26ccf344","name":"assistive technology checks"},{"id":111881,"uuid":"0e9e71b4-5296-41cd-754d-3f90625f9721","name":"check fire panel"},{"id":111882,"uuid":"a965cce8-5b27-4848-79b3-9448a97d450f","name":"check communal areas (internal)"},{"id":111880,"uuid":"50ebb744-7222-4e3a-7f10-12500667aa3c","name":"check communal grounds "}]}]}},{"uuid":"2f885ad8-f046-41f9-72a6-ee53b64f8b53","commandUri":"config/outcome/clone/","timestamp":"2023-09-20T10:35:20.686Z","outcomeViewModel":{"id":111884,"uuid":"4ae674d0-3ccd-47bb-7bcf-56db3a38efcb","name":"weekly checks","actionGroups":[{"id":111885,"uuid":"68c8d09c-eae5-45a7-748c-d8015ee1b627","name":"take bins out","actions":[{"id":111889,"uuid":"eeef73bb-8486-4645-77d1-477837c15bf6","name":"bring bins in"},{"id":111890,"uuid":"e5f03586-e087-4895-748a-1a5ba8f329f5","name":"fire alarm test"},{"id":111891,"uuid":"4b1479cd-94dd-4de2-7b81-354338e115a3","name":"fire safety equipment checks"},{"id":111886,"uuid":"ef9c6e79-0f5d-4623-749b-607d663848cf","name":"fire door release tests"},{"id":111888,"uuid":"34ba69ea-4fa6-4096-790d-9c4b5156ce32","name":"water temperature checks (< 41 or > 60 deg C must be reported to maint.)"},{"id":111892,"uuid":"e978e524-f1d8-40cb-7285-a2300b0d3d9c","name":"fridge/freezer temperature checks"},{"id":111887,"uuid":"6b07b154-5691-422f-75ae-d7004ff52967","name":"weekly void property checks"}]}]}},{"uuid":"53d699e3-4e7c-48a7-7bd4-0f01068cb2f3","commandUri":"config/outcome/clone/","timestamp":"2023-09-20T10:35:20.686Z","outcomeViewModel":{"id":111893,"uuid":"517137e9-070b-47eb-7fd9-1219c1dc6b9c","name":"monthly checks","actionGroups":[{"id":111894,"uuid":"8d66b8e9-0e69-4e3f-781d-24f3b0826350","name":"fire drill","actions":[{"id":111896,"uuid":"61960d1a-6995-4f36-7449-928a75f0bdea","name":"PPE ordered "},{"id":111895,"uuid":"b7b93936-02b8-43f0-7972-183a95d9497d","name":"vehicle safety checks"}]}]}},{"uuid":"0060d574-fc8e-445d-7cf2-5b5c4b6ab17c","commandUri":"service-config/-100/task/needsAssessment/","timestamp":"2023-09-20T10:35:20.686Z","operation":"add","serviceTypeId":-100,"taskName":"needsAssessment","orderbyChange":{"from":null,"to":20},"allowNextChange":{"from":null,"to":true}},{"uuid":"ddcd436c-9256-462e-7426-ccf8e6b7a22b","commandUri":"service-config/-100/task/needsAssessment/setting/transientOutcomesByUuid/","timestamp":"2023-09-20T10:35:20.686Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"transientOutcomesByUuid","valueChange":{"from":null,"to":"b2c0cf6d-cd62-4925-75fe-19de95de9791,4ae674d0-3ccd-47bb-7bcf-56db3a38efcb,517137e9-070b-47eb-7fd9-1219c1dc6b9c"}},{"uuid":"39fb3ac1-6cd4-4b66-7e36-f436c2ce4ea3","commandUri":"service-config/-100/task/needsAssessment/setting/commentWidth/","timestamp":"2023-09-20T10:35:20.686Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"commentWidth","valueChange":{"from":null,"to":"wide"}},{"uuid":"f8d0488b-5ad7-47c9-7a66-17b0c5a92fec","commandUri":"service-config/-100/task/needsAssessment/setting/showOutcomes/","timestamp":"2023-09-20T10:35:20.686Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"showOutcomes","valueChange":{"from":null,"to":"all"}},{"uuid":"21da9bf2-0256-4428-7da1-0e986a780249","commandUri":"service-config/-100/task/needsAssessment/setting/tookPlaceOn/","timestamp":"2023-09-20T10:35:20.686Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"tookPlaceOn","valueChange":{"from":null,"to":"date"}},{"uuid":"bb82b9fe-ef19-4642-7292-11835bf093ff","commandUri":"service-config/-100/task/needsAssessment/setting/titleRaw/","timestamp":"2023-09-20T10:35:20.687Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"titleRaw","valueChange":{"from":null,"to":"regular checks"}},{"uuid":"12e5ca18-b55e-4206-7d8d-964d43ffe509","commandUri":"service-config/-100/task/needsAssessment/setting/showActionComponents/","timestamp":"2023-09-20T10:35:20.687Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"showActionComponents","valueChange":{"from":null,"to":"targetSchedule,comment,link,status"}},{"uuid":"52427b2b-05fb-4dd0-77a9-1ec43495aeb7","commandUri":"service-config/-100/task/needsAssessment/setting/showNewActions/","timestamp":"2023-09-20T10:35:20.687Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"showNewActions","valueChange":{"from":null,"to":"y"}},{"uuid":"609b5dda-d679-481d-7c7d-76a927c88643","commandUri":"service-config/-100/task/needsAssessment/setting/showMenus/","timestamp":"2023-09-20T10:35:20.687Z","serviceTypeId":-100,"taskName":"needsAssessment","settingName":"showMenus","valueChange":{"from":null,"to":"overview"}}]
        ad-hoc checks (config only) then add the new outcomes as regular checks
        [{"uuid":"99f0a23b-6406-46c6-761d-91ea702b9136","commandUri":"service-config/-100/task/needsReduction/","timestamp":"2023-09-20T10:35:56.804Z","operation":"add","serviceTypeId":-100,"taskName":"needsReduction","orderbyChange":{"from":null,"to":25},"allowNextChange":{"from":null,"to":true}},{"uuid":"7bac0ba2-e40a-4114-7d52-6a52937c4592","commandUri":"service-config/-100/task/needsReduction/setting/commentWidth/","timestamp":"2023-09-20T10:35:56.804Z","serviceTypeId":-100,"taskName":"needsReduction","settingName":"commentWidth","valueChange":{"from":null,"to":"wide"}},{"uuid":"bbb4ce6b-aa82-4e25-7ded-76305c6dfd8b","commandUri":"service-config/-100/task/needsReduction/setting/outcomesById/","timestamp":"2023-09-20T10:35:56.804Z","serviceTypeId":-100,"taskName":"needsReduction","settingName":"outcomesById","valueChange":{"from":null,"to":"111875,111884,111893"}},{"uuid":"3d446d21-c47f-470f-7408-3cb8d10a23b3","commandUri":"service-config/-100/task/needsReduction/setting/titleRaw/","timestamp":"2023-09-20T10:35:56.805Z","serviceTypeId":-100,"taskName":"needsReduction","settingName":"titleRaw","valueChange":{"from":null,"to":"ad-hoc checks"}},{"uuid":"7a6a0b37-b9c7-4f63-77bf-066a1c31a008","commandUri":"service-config/-100/task/needsReduction/setting/tookPlaceOn/","timestamp":"2023-09-20T10:35:56.805Z","serviceTypeId":-100,"taskName":"needsReduction","settingName":"tookPlaceOn","valueChange":{"from":null,"to":"dateTime"}},{"uuid":"dac838f9-9a0b-457a-79f4-2e6b04c2389a","commandUri":"service-config/-100/task/needsReduction/setting/showVisualStyleAs/","timestamp":"2023-09-20T10:35:56.805Z","serviceTypeId":-100,"taskName":"needsReduction","settingName":"showVisualStyleAs","valueChange":{"from":null,"to":"checks"}},{"uuid":"0614e05b-2cda-4d7c-7aa2-a09c97648c82","commandUri":"service-config/-100/task/needsReduction/setting/showMenus/","timestamp":"2023-09-20T10:35:56.805Z","serviceTypeId":-100,"taskName":"needsReduction","settingName":"showMenus","valueChange":{"from":null,"to":"addGoal"}}]
        */

        // SETTINGS
        // ========
        /*
        Enable the buildings module in cfg_modules
        Possibly need evangelist or buildings admin permission
        Enable security access to the buildings
        Use ui admin mode on buildings menu item to create buildings
        Disable features: buildings.overview.tabs.support [now on ‘history’]
        Disable features: checklist
        Disable features: agreements [disable by default]
        */

        // ? add global outcomes for buildings to 'add goal' from

        // CONFIG
        // ======
        /*
        service config for -100 'building-default'
        referralView (allowNext for all steps...)
        needsAttachment
            commentWidth wide
            showOutcomes 'none'
            showCommentComponents attachments
            menu attachments
            tookPlaceOn date (? dateTime else out of sync with below)
        needsAssessment
            outcomes eg room checklist
            showNewActions y
            menu 'overview' (history)
            titleRaw 'regular checks'
            tookPlaceOn dateTime (else checks shown as later!)
            showActionComponents: name + targetSchedule (not targetDate and ? comment)
        needsReduction
            [crossed out] enable 'add goal' [fixed smart steps]
            outcomes eg room checklist
            tookPlaceOn dateTime
            titleRaw 'ad-hoc checks'
            showVisualStyleAs checks
        [crossed out] needsChecklist
                configure outcomes
        */

    }

    @Test
    public void buildingEndToEndTest() {
        loginAsSysadmin();


        // *************
        // GIVEN DEMAND

        // CREATE BUILDING DEMAND - these are bldg appointments, for workers to be allocated to
        String aptTypeRun = unique.nameFor("typeBldRun1");
        final FixedContainerViewModel building = rotaSteps.createBuilding(unique.nameFor("typeParentBld"));

        rotaSteps.createBuildingAgreement(building);
        rotaSteps.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(building, LocalDateTime.now());

        // CREATE REFERRAL DEMAND - these are individual referral appointments, for workers to be allocated to
        // NB this is just to test the flexibility of the building rota - to have bldg appointments and individual ones together
        final Pair<Long, String> referralPair = rotaSteps.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement("aptType1", LocalDate.now().atTime(22, 0), null);
        ReferralViewModel rvm = referralActor.getReferralById(referralPair.component1()).getBody();

        // referrals need to be on a building - see getWherePredicateForDemandSchedulesOfClientsInBuildingAndBuilding
        final FixedContainerViewModel unit = rotaSteps.createBuilding(unique.nameFor("typeUnitBld"), building);
        ReferralTaskClientResidenceCommandDto residence = new ReferralTaskClientResidenceCommandDto(rvm.serviceRecipientId, "residence");
        residence.residence = ChangeViewModel.changeNullTo(unit.buildingId);
        residence.startDate = ChangeViewModel.changeNullTo(now.toLocalDate().minusDays(5));
        commandActor.executeCommand(residence);


        // *************
        // GIVEN RESOURCE

        // CREATE WORKER RESOURCE - these are workers, to be allocated to runs (they have their primaryLocation set to appear on the run rota)
        String userName = unique.userNameFor("rb2-user1");
        String password = unique.passwordFor(userName);
        IndividualUserSummaryViewModel uvm = userActor.createIndividualWithUser(userName, password, unique.firstNameFor(userName), unique.lastNameFor(userName), null);
        // we don't need to be assigned to the building
        final String workerName = rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, building.buildingId).getName();


        // *************
        // CHECK CAN ASSIGN RESOURCES TO DEMAND

        // ASSIGN WORKER RESOURCE TO BLDG DEMAND to represent the worker being put on a bldg appointment
        rotaSteps.checkCanAssignResourceToFirstAppointmentToday("workers:all", "buildings:" + building.buildingId, building.name, workerName);

        // ASSIGN WORKER RESOURCE TO REFERRAL DEMAND to represent the worker being put on an individual's appointment
        rotaSteps.checkCanAssignResourceToFirstAppointmentToday("workers:all", "buildings:" + building.buildingId, referralPair.getSecond(), workerName);

        logout();
    }

}
