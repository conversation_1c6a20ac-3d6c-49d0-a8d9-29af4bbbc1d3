package com.ecco.acceptancetests.api.keys;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.infrastructure.rest.ThrowOnlyOn400And5xxErrorHandler;
import com.ecco.webApi.viewModels.UserDeviceViewModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.assertj.core.api.Assertions;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeMatcher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientResponseException;

import java.util.Base64;
import java.util.UUID;

import static org.assertj.core.api.Assertions.catchThrowableOfType;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;

@SuppressWarnings("ConstantConditions")
public class UserDeviceAPITests extends BaseJsonTest {
    private static UUID userDeviceIdentifier = null; // The equivalent of a new client accessing the system.

    @BeforeEach
    public void changeRestTemplateErrorHandler() {
        restTemplate.setErrorHandler(new ThrowOnlyOn400And5xxErrorHandler());
    }

    @Test
    public void givenDeviceIdentifierIsUnknownWhenVerifyingThenReturns404AndInvalidUserDevice() throws JsonProcessingException {
        final UUID guid = unknownGuid();
        var response = catchThrowableOfType(
                () -> userDeviceActor.getValidUserDeviceKey(guid),
                RestClientResponseException.class
        );
        Assertions.assertThat(response.getRawStatusCode()).isEqualTo(HttpStatus.NOT_FOUND.value());
        var responseBody = response.getResponseBodyAsString();
        assertUserDeviceNotFound(guid, objectMapper.readValue(responseBody, UserDeviceViewModel.class));
    }

    @Test
    public void whenCreatingThenReturnsNewKey() {
        final ResponseEntity<UserDeviceViewModel> response = userDeviceActor.createNewUserDeviceKey();
        assertEquals("status code", HttpStatus.CREATED, response.getStatusCode());
        final Matcher<String> matcher = allOf(notNullValue(String.class), validUuid());
        assertUserDeviceReturned(response.getBody(), is(matcher));
    }

    @Test
    public void givenDeviceIdentifierIsKnownAndKeyIsValidWhenVerifyingThenReturnsKey() {
        final UUID guid = validGuid();

        final ResponseEntity<UserDeviceViewModel> response = userDeviceActor.getValidUserDeviceKey(guid);
        assertEquals("status code", HttpStatus.OK, response.getStatusCode());
        assertUserDeviceReturned(response.getBody(), is(equalTo(guid.toString())));
    }

    @Test
    public void givenDeviceIdentifierIsKnownAndKeyIsInvalidWhenVerifyingThenReturns404AndInvalidUserDevice() throws JsonProcessingException {
        final UUID guid = invalidGuid();

        var response = catchThrowableOfType(
                () -> userDeviceActor.getValidUserDeviceKey(guid),
                RestClientResponseException.class
        );
        Assertions.assertThat(response.getRawStatusCode()).isEqualTo(HttpStatus.NOT_FOUND.value());
        var responseBody = response.getResponseBodyAsString();
        assertUserDeviceNotFound(guid, objectMapper.readValue(responseBody, UserDeviceViewModel.class));
    }

    @Test
    public void givenDeviceIdentifierIsUnknownWhenRevalidatingThenReturns404() throws JsonProcessingException {
        final UUID guid = unknownGuid();

        var response = catchThrowableOfType(
                () -> userDeviceActor.revalidateUserDeviceKey(guid),
                RestClientResponseException.class
        );
        Assertions.assertThat(response.getRawStatusCode()).isEqualTo(HttpStatus.NOT_FOUND.value());
        var responseBody = response.getResponseBodyAsString();
        assertUserDeviceNotFound(guid, objectMapper.readValue(responseBody, UserDeviceViewModel.class));
    }

    @Test
    public void givenDeviceIdentifierIsKnownAndKeyIsValidWhenRevalidatingThenReturnsOriginalKey() {
        final UUID guid = validGuid();

        final ResponseEntity<UserDeviceViewModel> originalDevice = userDeviceActor.getValidUserDeviceKey(guid);
        final ResponseEntity<UserDeviceViewModel> response = userDeviceActor.revalidateUserDeviceKey(guid);
        assertUserDeviceReturned(response.getBody(), is(equalTo(guid.toString())), is(equalTo(originalDevice.getBody().base64Key)));
    }

    @Test
    public void givenDeviceIdentifierIsKnownAndKeyIsInvalidWhenRevalidatingThenReturnsNewKey() {
        final UUID guid = validGuid();

        final ResponseEntity<UserDeviceViewModel> originalDevice = userDeviceActor.getValidUserDeviceKey(guid);
        userDeviceActor.invalidateUserDeviceKey(guid);
        final ResponseEntity<UserDeviceViewModel> response = userDeviceActor.revalidateUserDeviceKey(guid);
        assertUserDeviceReturned(response.getBody(), is(equalTo(guid.toString())), not(equalTo(originalDevice.getBody().base64Key)));
    }

    /*** NO SPECIFIC TEST FOR INVALIDATING A KEY AS THIS IS USED WITHIN THE OTHER TESTS TO OBTAIN INVALID KEYS ***/

    private void assertUserDeviceNotFound(UUID guid, UserDeviceViewModel response) { // TODO: Why do we return info that is what we sent?  Perhaps we store it - check client
        assertEquals("guid", guid.toString(), response.guid);
        assertFalse("validity", response.valid);
        assertNull("key", response.base64Key);
        assertNull("cipher", response.cipher);
    }

    private void assertUserDeviceReturned(final UserDeviceViewModel viewModel, Matcher<String> guidMatcher) {
        assertUserDeviceReturned(viewModel, guidMatcher, notNullValue(String.class));
    }

    private void assertUserDeviceReturned(final UserDeviceViewModel viewModel, Matcher<String> guidMatcher, final Matcher<String> base64KeyMatcher) {
        assertThat("guid", viewModel.guid, guidMatcher);
        assertThat("base64 key", viewModel.base64Key, base64KeyMatcher);
        assertTrue("validity", viewModel.valid);
        assertThat("key length >= 128 bits", Base64.getDecoder().decode(viewModel.base64Key).length, greaterThanOrEqualTo(128 / 8));
        assertEquals("cipher", "AES-GCM", viewModel.cipher);
    }

    private static Matcher<String> validUuid() {
        return new TypeSafeMatcher<>() {
            @Override
            protected boolean matchesSafely(String item) {
                try {
                    //noinspection ResultOfMethodCallIgnored
                    UUID.fromString(item);
                    return true;
                } catch (IllegalArgumentException e) {
                    return false;
                }
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("a valid uuid string");
            }
        };
    }

    private UUID validGuid() {
        // Act as a client device - if we don't have an identifier then create a new key, otherwise revalidate the one we have.
        if (userDeviceIdentifier == null) {
            final ResponseEntity<UserDeviceViewModel> newUserDeviceKey = userDeviceActor.createNewUserDeviceKey();
            return userDeviceIdentifier = UUID.fromString(newUserDeviceKey.getBody().guid);
        } else {
            userDeviceActor.revalidateUserDeviceKey(userDeviceIdentifier);
            return userDeviceIdentifier;
        }
    }

    private UUID invalidGuid() {
        // Act as a client device - if we don't have an identifier then create a new key, otherwise revalidate the one we have.
        if (userDeviceIdentifier == null) {
            final ResponseEntity<UserDeviceViewModel> newUserDeviceKey = userDeviceActor.createNewUserDeviceKey();
            userDeviceIdentifier = UUID.fromString(newUserDeviceKey.getBody().guid);
        }
        userDeviceActor.invalidateUserDeviceKey(userDeviceIdentifier);
        return userDeviceIdentifier;
    }

    private UUID unknownGuid() {
        return UUID.randomUUID();
    }
}
