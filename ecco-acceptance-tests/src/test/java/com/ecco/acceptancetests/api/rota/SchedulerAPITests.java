package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import org.junit.Ignore;
import org.junit.Test;


/**
 * Tests for the scheduler are done on the rota schedules - eg Rota...APITests.
 * However, this class is to help us remember this, and to document the setup
 */
@SuppressWarnings({"unused"})
public class SchedulerAPITests extends BaseJsonTest {

    @Test
    @Ignore("only for local setup for now")
    public void configSetupScheduler() {
        // update cfg_module set enabled=1 where name='rota';
        // ui - feature rota.scheduler
        // ui - rota permission
        // update cfg_settings set keyvalue=35 where keyname='care.scheduler.days';
        // insert into appointmenttypes
        // list def for care-checks (disable complete/not-complete) add new ones / care-locations
        // create visit schedule, withOUT direct task (its exclusive) and 'visit tasks' target schedule
        // if not sync'd on cal_eventstatus, then sync with https://demo.eccosolutions.co.uk/housing/api/rota/prePopulate
        // create a 'carer' in the system assigned specifically to the service, AND a hr job (admin -> staff, new job, link access)
        // login, 'move' a date, or use specific date: https://demo.eccosolutions.co.uk/housing/nav/r/care/2025-05-30
        // 'visit'
        // reports-definitions 'scheduler' report (TODO copy over)

        // scheduler menu item shows all rota, including care runs with some 'undefined' fields
        // assign a client to a worker directly, eg 'Cherry Tree House' on demo rota
        // goto the scheduler and find client on service
        // ** NB in the app, at least on demo sites, the uncompleted visits stack up for carers!
        // see

    }

    /*
     * visiting support data
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (1,0, 0x00,'alarm test - critical',100175,0, 0x00 ,'{"algorithm": {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-1", "lookupText": "yellow"},{"range": "2-", "lookupText": "red"}]}}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (2,0, 0x00,'fault/low battery - urgent',100175,0, 0x00 ,'{"algorithm": {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-5", "lookupText": "yellow"},{"range": "6-", "lookupText": "red"}]}}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (3,0, 0x00,'fire, flush & lint - urgent',100175,0, 0x00 ,'{"algorithm": {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-5", "lookupText": "yellow"},{"range": "6-", "lookupText": "red"}]}}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (6,0, 0x00,'visit/calls',100175,0, 0x00 ,'{"colourCssValue":"purple"}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (9,0, 0x00,'collection',100175,0, 0x00 ,'{"colourCssValue":"yellow"}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (11,0, 0x00,'health & safety',100175,0, 0x00 ,'{"colourCssValue":"purple"}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (12,0, 0x00,'alarm test - urgent',100175,0, 0x00 ,'{"algorithm": {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-5", "lookupText": "yellow"},{"range": "6-", "lookupText": "red"}]}}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (13,0, 0x00,'fault/low battery - critical',100175,0, 0x00 ,'{"algorithm": {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-1", "lookupText": "yellow"},{"range": "2-", "lookupText": "red"}]}}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (14,0, 0x00,'fire, flush & lint',100175,0, 0x00 ,'{"colourCssValue":"yellow"}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (15,0, 0x00,'heat and smoke test',100175,0, 0x00 ,'{"colourCssValue":"yellow"}');
     insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (16,0, 0x00,'additional equipment added',100175,0, 0x00 ,'{"colourCssValue":"yellow"}');
     -- insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (4,0, 0x00,'shared hours',-100,60, 0x00 ,'{"colourCssValue":"red"}');
     -- insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (10,0, 0x00,'availability',-100,0, 0x00 ,'NULL');
     */

    /*
     * /demo data
    insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (20,0, 0x00,'welfare check',100175,0, 0x00 ,'{"colourCssValue":"purple"}');
    insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (6,0, 0x00,'visit/calls',100175,0, 0x00 ,'{"colourCssValue":"purple"}');
    insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (21,0, 0x00,'key time',100175,0, 0x00 ,'{"algorithm": {"enabled": true, "algorithm": "", "rangeLookups": [{"range": "0-1", "lookupText": "yellow"},{"range": "2-", "lookupText": "red"}]}}');
     */
}
