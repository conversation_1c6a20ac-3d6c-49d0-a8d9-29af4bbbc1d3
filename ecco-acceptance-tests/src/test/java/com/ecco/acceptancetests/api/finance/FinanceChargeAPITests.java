package com.ecco.acceptancetests.api.finance;

import static org.assertj.core.api.Assertions.assertThat;

import com.ecco.acceptancetests.api.address.BaseAddressHistoryCommandAPITestSupport;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.rota.ContractViewModel;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.stream.Stream;
import java.util.Objects;


/**
 * A calculated-only implementation of service charges, as per DEV-2525.
 * A full implementation would involve scheduling invoice creation, which would involve
 * CRUD management based on address changes, allowing for invoice adjustments etc (see DEV-2526).
 */
@SuppressWarnings("DataFlowIssue")
public class FinanceChargeAPITests extends BaseAddressHistoryCommandAPITestSupport {

    private final UniqueDataService unique = UniqueDataService.instance;
    ContractViewModel svcChgContract;

    static Stream<LocalDate> dateParameters() {
        return Stream.of(
            LocalDate.now(),
            LocalDate.of(2025, 4, 1)
        );
    }

    // This test ensures the setup is working correctly
    @Test
    public void configSetupOnly() {
        /*
        FINANCE - service charges
        -------------------------
        service charges: referralView.charges
        table needs to not be mui - turn off feature 'mui'
        update cfg_module set enabled=true where name='buildings';

        charges report won't show (error in console) unless have contract
        select max(id) from servicerecipients;
        select * from hibernate_sequences where sequence_name='svc_recipient';
        -- update hibernate_sequences set next_val=? where sequence_name='svc_recipient';
        INSERT INTO servicerecipients (id, version, discriminator_orm, created, dataProtectionSignatureId, consentSignatureId, latestClientStatusId, currentTaskId, currentTaskIndex, nextDueSlaTaskId, nextSlaDueDate, serviceAllocationId, agreementSignatureId, agreement2SignatureId, agreement3SignatureId, latestClientStatusDateTime, agreement4SignatureId, agreement5SignatureId, agreement6SignatureId, agreement7SignatureId, agreement8SignatureId, agreement9SignatureId, agreement10SignatureId)
        VALUES (52, 0, 'cont', '2023-11-20 09:00:00', null, null, null, null, 0, null, null, -300, null, null, null, null, null, null, null, null, null, null, null);
        insert into fin_contracts (id, version, name, startInstant, endInstant, PONumbers, agreedCharge, contractTypeId, serviceRecipientId)
        values (1, 0, 'default', '2023-11-20', null, null, null, 203, 52);
        INSERT INTO fin_ratecards (id, version, name, startInstant, endInstant, advisoryTotalDuration, advisoryTotalCharge, matchingPartsOfWeek, matchingStartTime, matchingEndTime)
        VALUES (1, 0, 'weekly', '2023-11-20 00:00:00', null, null, null, null, null, null);
        -- select * from fin_unitofmeasurements where id=3;
        INSERT INTO fin_ratecardentries (id, version, rateCardId, matchingCategoryTypeId, matchingOutcomeId, matchingFactors, defaultEntry, chargeType, fixedCharge, unitMeasurementId, unitCharge, childRateCardEntryId, unitsToRepeatFor, disabled, units)
        VALUES (1, 0, 1, null, null, '{}', false, 'TEMPORAL', null, 3, 2.86, null, null, false, 1);
        INSERT INTO fin_ratecardentries (id, version, rateCardId, matchingCategoryTypeId, matchingOutcomeId, matchingFactors, defaultEntry, chargeType, fixedCharge, unitMeasurementId, unitCharge, childRateCardEntryId, unitsToRepeatFor, disabled, units)
        VALUES (2, 0, 1, null, null, '{}', false, 'TEMPORAL', null, 3, 20.00, 1, null, false, 7);
        INSERT INTO fin_contracts_ratecards (contractId, rateCardId) values (1, 1);

        also add buildings - because it uses building addresses
        */
    }

    @BeforeEach
    public void configSetup() {
        ensureCurrentUserHasGroup("finance");
        ensureFeatureToggleEnabled("referralView.charges");

        this.createBuildingAndAddresses("chg test");

        // enable 'charges' feature, and disable 'mui reports' (else table breaks)
        // insert into servicerecipients (id, version, discriminator_orm, created, serviceallocationid, currentTaskIndex) values (52, 0, 'cont', '2024-01-01 09:00:00', -300, 0);
        // insert into fin_contracts values (1, 0, 'default', '1970-01-01 00:00:00', null, null, null, 203, 52);
        // insert into fin_ratecards values (1, 0, 'weekly', '1970-01-01 00:00:00', null, null, null, null, null, null);
        // insert into fin_contracts_ratecards values (1, 1);
        // insert into fin_ratecardentries values (1, 0, 1, null,  null, '{}', false, 'TEMPORAL', null, 3, 2.86, null, null, false, 1);
        // insert into fin_ratecardentries values (2, 0, 1, null,  null, '{}', false, 'TEMPORAL', null, 3, 20.00, 1, null, false, 7);

        // Also see contract from Liquibase acctest context
        // NB this contract is not necessarily obeyed by the server - see findByContractTypeIdOrderByIdDesc
        //
        svcChgContract = contractActor.getContractById(1);
    }

    /**
     * No charges.
     * When we are changing addresses, then check there are no charges.
     * Its only when an address is associated with a building that we should charge.
     */
    @Test
    public void noChargesForNonBldgAddressChanges() {

        ReferralViewModel refWithAdr = createReferralWithAddress("address-noChg");

        // WHEN change the client address
        // use an existing address from buildings, but using 'changeAddress' means we're just using the addres - we're not moving in
        addressActor.changeAddress(buildings.get(0).address.getAddressId(), refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue());

        // CONTROLLER to load charges
        //var yesterday = LocalDate.now(ZoneId.of("UTC")).minusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC);
        //var future = yesterday.atZone(ZoneId.of("UTC")).plusWeeks(8).toInstant();
        //Range<Instant> dates = Range.openClosed(yesterday, future);
        var charges = financeChargeActor.getChargesByServiceRecipientId(refWithAdr.serviceRecipientId, null, null);
        assertThat(Objects.requireNonNull(charges.getBody()).length).isEqualTo(0);
    }

    /**
     * Basic charge.
     */
    @ParameterizedTest(name = "Basic charges test with date: {0}")
    @MethodSource("dateParameters")
    void basicChargesForBldgAddressChanges(LocalDate date) {

        // GIVEN a referral with an address
        ReferralViewModel refWithAdr = createReferralWithAddress("address-baseChg");

        /*
        // NB test failed after midnight, since FinanceChargeCalculationDefault uses convertFromUsersLocalDateTime
        // because it assumes the dates saved are local to the user London, which they are
        // so we just need to assume the same here
        var londonNow = EccoTimeUtils.getUsersLocalDateTimeNow().toLocalDate();
        addressActor.changeBuilding(buildings.get(0).buildingId, buildings.get(0).address.getAddressId(),
                refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue(), londonNow.minusDays(2));
        var charges = financeChargeActor.getChargesByServiceRecipientId(refWithAdr.serviceRecipientId);
        Assertions.assertThat(Objects.requireNonNull(charges.getBody()).length).isEqualTo(1);
        // test the charge is 2 days at 2.86 per day
        Assertions.assertThat(Objects.requireNonNull(charges.getBody()[0]).getNetAmount()).isEqualTo(new BigDecimal("5.72"));
        */

        // WHEN change the client address
        addressActor.changeBuilding(buildings.get(0).buildingId, buildings.get(0).address.getAddressId(),
                refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue(), date.minusDays(2));
        if (date.isBefore(LocalDate.now())) {
            // WHEN move out - building location null, but postal address remains at the building (for the sake of this test)
            addressActor.changeBuilding(null, buildings.get(0).address.getAddressId(),
                    refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue(), date);
        }

        var charges = financeChargeActor.getChargesByServiceRecipientId(refWithAdr.serviceRecipientId, null, null);
        assertThat(Objects.requireNonNull(charges.getBody()).length).isEqualTo(1);
        // test the charge is 2 days at 130/7 per day
        assertThat(Objects.requireNonNull(charges.getBody()[0]).getNetAmount()).isEqualTo(new BigDecimal("37.14"));
    }

    /**
     * Advanced charges.
     */
    @ParameterizedTest(name = "Advanced charges test with date: {0}")
    @MethodSource("dateParameters")
    void advancedChargesForBldgAddressChanges(LocalDate date) {

        // GIVEN a referral with an address
        ReferralViewModel refWithAdr = createReferralWithAddress("address-advChg");

        // WHEN change bldg[0] 2 months ago
        var bldg = buildings.get(0);
        addressActor.changeBuilding(bldg.buildingId, bldg.address.getAddressId(),
                refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue(), date.minusMonths(2));

        // WHEN change bldg[1] 1 month ago
        bldg = buildings.get(1);
        addressActor.changeBuilding(bldg.buildingId, bldg.address.getAddressId(),
                refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue(), date.minusMonths(1));

        var charges = financeChargeActor.getChargesByServiceRecipientId(refWithAdr.serviceRecipientId, null, date);
        // FIXME: We now have 2 lines at 130 and 140 flat rate which is wrong - put some month charges and child rate cards in!
        // 2 months ago (for 1 month, at 4 weeks at 130pcw) - do we actually need to test child rate cards that we already test in unit tests
        // 1 months ago (for 1 month, at 4 weeks at 140pcw)
        var chargeLines = Objects.requireNonNull(charges.getBody());
        assertThat(chargeLines.length).isEqualTo(2);
        // NB we can't be sure of the exact charge because we are charging by days -
        // 1 month to us is a calendar month, so it's between 80 for Feb and 88.58 for 31 days - non-month charge
        // BUT we don't have a calendar month charge ** CHARGE BY CALENDAR MONTH NEEDS ANOTHER TEST **
        assertThat(chargeLines).allMatch(
                c -> c.getNetAmount().compareTo(new BigDecimal("520")) >= 0 && c.getNetAmount().compareTo(new BigDecimal("640")) <= 0,
                "Expect charges to be between 520 and 640");
    }

    private ReferralViewModel createReferralWithAddress(String clientSecondName) {

        // GIVEN a referral with an address
        ReferralViewModel refWithAdr;
        {
            var ra = referralActor.constructMinimalReferralAndClientAggregate(null, "1970", clientSecondName, service, null);
            var avm = new AddressViewModel();
            String[] adr = {"noChg address0"};
            avm.setAddress(adr);
            avm.setPostcode("FI3 1ST");
            avm.setTown("Origin");
            ra.getClient().setAddress(avm);
            //addressActor.createAddress(avm).getBody().getId();
            //addressActor.changeAddress(Integer.parseInt(addressResult.getId()), bldg.serviceRecipientId);

            refWithAdr = referralActor.createMinimalReferralAndClient(ra);
        }
        return refWithAdr;
    }

}
