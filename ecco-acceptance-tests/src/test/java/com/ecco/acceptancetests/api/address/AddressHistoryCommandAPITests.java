package com.ecco.acceptancetests.api.address;

import com.ecco.webApi.contacts.address.AddressHistoryViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import org.assertj.core.api.Assertions;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.hamcrest.CoreMatchers.*;
import static org.junit.Assert.*;

public class AddressHistoryCommandAPITests extends BaseAddressHistoryCommandAPITestSupport {

    @BeforeEach
    public void create() {
        this.createBuildingAndAddresses("adr test");
    }

    @Test
    public void checkFirstEntryWith1970ThenDelete() {

        // GIVEN a referral with an address
        ReferralViewModel refWithAdr;
        {
            var ra = referralActor.constructMinimalReferralAndClientAggregate(null, "1970", "address", service, null);

            var avm = new AddressViewModel();
            String[] adr = {"first address"};
            avm.setAddress(adr);
            avm.setPostcode("FI3 1ST");
            avm.setTown("Origin");
            ra.getClient().setAddress(avm);
            //addressActor.createAddress(avm).getBody().getId();
            //addressActor.changeAddress(Integer.parseInt(addressResult.getId()), bldg.serviceRecipientId);

            refWithAdr = referralActor.createMinimalReferralAndClient(ra);
        }

        // WHEN change the client address
        int newAdrId;
        var avm = new AddressViewModel();
        {
            avm.setLine1(unique.appendId("second address"));
            avm.setPostcode("SE3 0ND");
            avm.setTown("Origin");
            newAdrId = Integer.parseInt(addressActor.createAddress(avm).getBody().getId());
            addressActor.changeAddress(newAdrId, refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue());
        }

        // THEN the first entry should be the original one, with validFrom 1970, validTo before the change
        AddressHistoryViewModel[] history;
        {
            // current entry is correct
            var clientNewAdr = clientActor.getClientById(refWithAdr.clientId).getBody();
            Assertions.assertThat(clientNewAdr.addressedLocationId).isEqualTo(newAdrId);

            // history is correct
            history = addressActor.getAddressHistoryByServiceRecipientIdOrderByValidFromDesc(refWithAdr.serviceRecipientId).getBody();
            MatcherAssert.assertThat(history.length, is(2));
            var second = history[0];
            var first = history[1];
            assertNotNull("first address did not record an addressId in the history", first.addressId);
            // could perhaps use 'matcherForSomeProperties'
            assertEquals(first.validFrom, LocalDate.EPOCH.atStartOfDay());
            assertEquals(second.addressId.intValue(), newAdrId);
            assertNotEquals(first.addressId.intValue(), newAdrId);

            // check the contactId for the first (special case) and latest (normal case)
            assertEquals(first.contactId, Integer.valueOf(refWithAdr.contactId.intValue()));
            assertEquals(second.contactId, Integer.valueOf(refWithAdr.contactId.intValue()));
        }
        // AND check servicerecipient address has been updated
        var sr = serviceRecipientActor.findSummaryCached(refWithAdr.serviceRecipientId).getBody();
        assertEquals("address not equals", sr.addressCommaSep, avm.toCommaSepString());

        // THEN delete each entry
        {
            addressActor.deleteAddress(history[0].id, refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue());
            addressActor.deleteAddress(history[1].id, refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue());
        }
    }

    @Test
    public void checkLatestAddressIsUnitAddress() {

        // GIVEN a referral with a building address
        ReferralViewModel refWithAdr;
        {
            var ra = referralActor.constructMinimalReferralAndClientAggregate(null, "unit", "address-bldg", service, null);
            var avm = new AddressViewModel();
            String[] adr = {"first address"};
            avm.setAddress(adr);
            avm.setPostcode("FI3 1ST");
            avm.setTown("Origin");
            ra.getClient().setAddress(avm);
            refWithAdr = referralActor.createMinimalReferralAndClient(ra);
        }

        // WHEN change to unit address
        {
            Integer newAdrId = buildings.get(2).address.getAddressId();
            assertNotNull(newAdrId);
            addressActor.changeAddress(newAdrId, refWithAdr.serviceRecipientId, refWithAdr.contactId.intValue());
        }

        // THEN check servicerecipient address has been updated
        var sr = serviceRecipientActor.findSummaryCached(refWithAdr.serviceRecipientId).getBody();
        assertEquals("address not equals", sr.addressCommaSep, buildings.get(2).address.toCommaSepString());
    }

}