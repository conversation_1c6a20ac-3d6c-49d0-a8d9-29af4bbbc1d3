package com.ecco.acceptancetests.api.evidence;

import com.ecco.dom.EvidenceAction;
import com.ecco.dto.AddedRemovedDto;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import org.joda.time.*;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;


public class SupportCommandAPITests extends SupportCommonCommandAPITests {

    public SupportCommandAPITests() {
        super();
    }

    // TODO refactor to use the underlying /snapshots
    @Test
    public void latestSmartSteps_happyPath() {
        // GIVEN a referral with work and 2 actions
        UUID instanceUuid1 = UUID.randomUUID();
        UUID instanceUuid2 = UUID.randomUUID();
        {
            // see also @Before createUniqueClientAndReferral

            // a comment and action exists
            CommentCommandViewModel ccvm = createCommentCommand(workUuid, "this is my first piece of work", Instant.now().minus(Days.days(10).toStandardDuration()), workDateUpTo50DaysInPast.minusDays(10));
            commandActor.executeCommand(ccvm);
            {
                GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                        defaultEvidenceTask, actionDefId, instanceUuid1, null);
                vm.goalNameChange = ChangeViewModel.create(null, "action 1 - goal 1");
                commandActor.executeCommand(vm);
            }
            {
                GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                        defaultEvidenceTask, actionDefId, instanceUuid2, null);
                vm.goalNameChange = ChangeViewModel.create(null, "action 1 - goal 2");
                commandActor.executeCommand(vm);
            }
        }

        // WHEN more work/actions are done, 5 days later
        UUID workUuid2 = UUID.randomUUID();
        {
            // a comment and action exists
            CommentCommandViewModel ccvm = createCommentCommand(workUuid2, "this is my second piece of work", Instant.now().minus(Days.days(5).toStandardDuration()), workDateUpTo50DaysInPast.minusDays(5));
            commandActor.executeCommand(ccvm);
            {
                GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel(workUuid2, getServiceRecipientId(), evidenceGroup,
                        defaultEvidenceTask, actionDefId, instanceUuid1, null);
                vm.goalNameChange = ChangeViewModel.create("action 1 - goal 1", "action 1 - goal 1 - updated");
                commandActor.executeCommand(vm);
            }
        }

        // THEN check the latest
        {
            EvidenceSupportWorkViewModel firstEvidence = readBackEvidence(workUuid);
            assertNotNull(firstEvidence);
            assertThat(firstEvidence.actions).hasSize(2);
            org.hamcrest.MatcherAssert.assertThat(firstEvidence.actions, hasItems(
                    hasProperty("goalName", equalTo("action 1 - goal 1")),
                    hasProperty("goalName", equalTo("action 1 - goal 2"))
            ));

            // check the latest for the first goal
            EvidenceSmartStepViewModel latestInstance1 = checkSnapshotContains(instanceUuid1);
            assertThat(latestInstance1.goalName).isEqualTo("action 1 - goal 1 - updated");
            EvidenceSmartStepViewModel latestInstance2 = checkSnapshotContains(instanceUuid2);
            assertThat(latestInstance2.goalName).isEqualTo("action 1 - goal 2");
        }
    }

    @Test
    public void actionStatusChangeReasonListName_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral
            ensureListDefinitionIds();

            // a comment and piece of work exists
            sendCommentCommand("this is my piece of work", workDateUpTo50DaysInPast);
        }

        UUID instanceUuid = UUID.randomUUID();

        // WHEN include a goal update (status reason change) on the same work
        GoalUpdateCommandViewModel vm;
        {
            // save some work with a statusChangeReason
            vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, instanceUuid, null);
            vm.statusChangeReason = ChangeViewModel.create(null, listDefId);
            commandActor.executeCommand(vm);
        }

        // THEN check the status change was made
        {
            EvidenceSupportWorkViewModel lastEvidence = readBackEvidence(vm.workUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.actions).hasSize(1);
            assertEquals(listDefId, lastEvidence.actions.get(0).statusChangeReasonId);
            assertThat(lastEvidence.actions.get(0).actionInstanceUuid).isEqualTo(instanceUuid);

            EvidenceSmartStepViewModel latestInstance = checkSnapshotContains(instanceUuid);
            assertThat(latestInstance.status).isEqualTo(EvidenceAction.isRelevant);

        }
    }

    @Test
    public void actionAssociated_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            sendCommentCommand("this is my associated piece of work", workDateUpTo50DaysInPast);
        }

        UUID instanceUuid = UUID.randomUUID();

        // WHEN
        GoalUpdateCommandViewModel vm;
        {
            // save some work with a 'blank' goal update
            vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, instanceUuid, null);
            vm.isRelevant = true;
            commandActor.executeCommand(vm);
            workUuid = vm.workUuid;
        }

        // THEN
        {
            EvidenceSupportWorkViewModel lastEvidence = readBackEvidence(vm.workUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.associatedActions).hasSize(1);
            assertThat(actionDefId.intValue()).isEqualTo(lastEvidence.associatedActions.get(0).intValue());
        }
    }

    @Test
    public void commentRiskManagementRequired_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral
        }

        // WHEN
        {
            CommentCommandViewModel ccvm = createCommentCommand("parents are amazing", workDateUpTo50DaysInPast);
            ccvm.riskManagementRequired = ChangeViewModel.create(null,  true);
            commandActor.executeCommand(ccvm);
        }

        // THEN
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(true, lastEvidence.riskManagementRequired);
        }
    }

    @Test
    public void commentClientStatusMeetingStatus_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral
            ensureListDefinitionIds();
        }

        // WHEN
        {
            CommentCommandViewModel ccvm = createCommentCommand("parents are dull", workDateUpTo50DaysInPast);
            ccvm.clientStatusId = ChangeViewModel.create(null, listDefId);
            ccvm.meetingStatusId = ChangeViewModel.create(null, listDefId);
            ccvm.locationId = ChangeViewModel.create(null, listDefId);
            commandActor.executeCommand(ccvm);
        }

        // THEN
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(listDefId, lastEvidence.clientStatusId);
            assertEquals(listDefId, lastEvidence.meetingStatusId);
            assertEquals(listDefId, lastEvidence.locationId);
        }
    }

    @Test
    public void attachment_happyPath() {
        // GIVEN
        long file2Id;
        {
            // see also @Before createUniqueClientAndReferral

            // an attachment exists
            if (fileId == null) {
                fileId = standardAttachment("needs");
            }
            file2Id = standardAttachment("needs");
        }

        List<EvidenceSupportWorkViewModel> workPrior = findWorkSummaryByServiceRecipientId();

        // WHEN
        UUID earlierAttachmentWorkUuid = UUID.randomUUID();
        {
            UUID firstWorkUuid = UUID.randomUUID();
            CommentCommandViewModel ccvm = createCommentCommand(firstWorkUuid, "work without attachment", null, workDateUpTo50DaysInPast);
            commandActor.executeCommand(ccvm);

            CommentCommandViewModel ccvmAttachment = createCommentCommand("work WITH attachments", workDateUpTo50DaysInPast);
            ccvmAttachment.attachmentIdsToAdd = new Long[] {fileId, file2Id};
            commandActor.executeCommand(ccvmAttachment);

            CommentCommandViewModel ccvmAttachment2 = createCommentCommand(earlierAttachmentWorkUuid, "work2 WITH another attachment", null, workDateUpTo50DaysInPast.plusDays(1));
            ccvmAttachment2.attachmentIdsToAdd = new Long[] {file2Id};
            commandActor.executeCommand(ccvmAttachment2);
        }

        // THEN CHECK work api
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(List.of(fileId, file2Id), lastEvidence.attachments.stream().map(a -> a.fileId).toList());

            List<EvidenceSupportWorkViewModel> work = findWorkSummaryByServiceRecipientId();
            assertEquals(workPrior.size() + 3, work.size());

            List<EvidenceSupportWorkViewModel> workWithAttachments = findWorkSummaryWithAttachmentsByServiceRecipientId();
            assertEquals(2, workWithAttachments.size());
        }

        // THEN CHECK attachments API
        {
            var attachments = supportEvidenceActor.findAttachmentsByServiceRecipientId(getServiceRecipientId()).getBody();
            assert attachments != null;
            // check 2 and NOT 3 - since one has 2 attachments
            assertEquals(2, attachments.size());
            // check newest first
            assertEquals(attachments.get(0).getWorkUuid(), earlierAttachmentWorkUuid);
        }
    }

    @Test
    public void flag_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral
        }

        // WHEN add a flag
        {
            // a comment and piece of work exists
            CommentCommandViewModel ccvm = createCommentCommand(workUuid, "this is my flag piece of work", null, workDateUpTo50DaysInPast);
            var f1 = ListDefinitionEntryViewModel.builder().listName("testlist_flag").name("flag 1").build();
            f1 = listDefActor.ensureAndReturnListDefinitionEntry(f1.getListName(), f1).iterator().next();
            ccvm.flagIds = AddedRemovedDto.added(Collections.singletonList(f1.getId()));
            commandActor.executeCommand(ccvm);
        }

        // THEN flag is returned
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(1, lastEvidence.flags.size());
        }
    }

    @Test
    public void hactRetrieval_happyPath() {

        // WHEN got work which is mapped to hact
        UUID instanceUuid = UUID.randomUUID();
        {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            sendCommentCommand("this is my HACT trigger work", workDateUpTo50DaysInPast);

            // save some work with a 'blank' goal update
            GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, instanceUuid, null);
            vm.statusChange = ChangeViewModel.changeNullTo(EvidenceAction.isRelevant);
            commandActor.executeCommand(vm);

            // we don't actually need to ensure the action exists on the service
            /*outcomes.stream()
                    .filter(input -> "economic wellbeing".equals(input.name)).findFirst().get()
                    .actionGroups.get(0).actions.stream()
                    .filter(a -> a.id.equals(82))
                    .findFirst().get().id;*/

            // save a second goal to test the inner join producing only one result (else client side hangs Chrome)
            vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, 113, UUID.randomUUID(), null);
            vm.statusChange = ChangeViewModel.changeNullTo(EvidenceAction.isRelevant);
            commandActor.executeCommand(vm);

            // save a third, nothing goal to test that the whole work item is returned (not just hact ones)
            // this may not be needed, but we'd expect work items to be the whole object
            vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, 114, UUID.randomUUID(), null);
            vm.statusChange = ChangeViewModel.changeNullTo(EvidenceAction.isRelevant);

            commandActor.executeCommand(vm);
        }

        // THEN can get back using hactOnly support work
        {
            List<EvidenceSupportWorkViewModel> work = supportEvidenceActor.findHactOnlySupportWorkSummaryByServiceRecipientId(getServiceRecipientId(), evidenceGroup).getBody().getContent();
            assertEquals(1, work.size());
            // the server only filters the work items that have hact, not the actions within it - so there is a non-hact action here (114)
            assertEquals(3, work.get(0).actions.size());
            org.hamcrest.MatcherAssert.assertThat(work.get(0).actions, hasItems(
                            hasProperty("actionId", equalTo(119L)),
                            hasProperty("actionId", equalTo(113L)),
                            hasProperty("actionId", equalTo(114L))
                    ));
        }
    }

    @Test
    public void statusChangeOnlySpiderGraph_happyPath() throws InterruptedException {

        // WHEN got work which is mapped to hact
        UUID instanceUuid = UUID.randomUUID();
        {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            sendCommentCommand("this is my statusChangeOnly work", workDateUpTo50DaysInPast.minusDays(1));

            // save some work with a 'blank' goal update
            GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, 114, instanceUuid, null);
            vm.statusChange = ChangeViewModel.changeNullTo(EvidenceAction.isRelevant);
            vm.goalPlanChange = ChangeViewModel.changeNullTo("my plan");
            commandActor.executeCommand(vm);

            // save a second goal for good measure
            GoalUpdateCommandViewModel vm2 = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, UUID.randomUUID(), null);
            vm2.statusChange = ChangeViewModel.changeNullTo(EvidenceAction.isRelevant);
            commandActor.executeCommand(vm2);
        }

        // FIXME: WE have an issue in GoalCommandHandler call to findLatestByServiceRecipientIdAndActionInstanceUuidAndCreatedLessOrEqualTo
        //  where we appear to be getting dirty reads.
        //  So that we can progress I'm putting a delay in here, but so that we don't forget, I'll ensure it's only
        //  until a certain date
        if (LocalDate.now().isBefore(new LocalDate(2025, 9, 10))) {
            Thread.sleep(500);
        } else {
            throw new IllegalStateException("Still haven't fixed the dirty read");
        }

        // THEN update a goal in a separate work item
        UUID updatedWorkUuid = UUID.randomUUID();
        {
            sendCommentCommand(updatedWorkUuid, "this is my statusChangeOnly work", workDateUpTo50DaysInPast.plusDays(1));
            GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel(updatedWorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, 114, instanceUuid, null);
            vm.goalPlanChange = ChangeViewModel.create("my plan", "my plan 2");
            commandActor.executeCommand(vm);
        }

        // THEN can get back support work changedOnly
        {
            List<EvidenceSupportWorkViewModel> work = supportEvidenceActor.findStatusChangeOnlySupportWorkSummaryByServiceRecipientId(getServiceRecipientId(), evidenceGroup).getBody().getContent();
            // we expect 1 work item because only the first work item has two statusChange's (two actionDefIds of isRelevant),
            // the later work item is an update - see 9bbad68e for the DISTINCT on the workitem
            assertEquals(1, work.size()); // FIXME: If this fails see fixme above
        }
    }

    @Test
    public void parentActionInstanceUuid_happyPath() {
        // GIVEN
        GoalUpdateCommandViewModel parentGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            // create an actionInstanceUuid with work date and created 2 days ago
            parentGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, UUID.randomUUID(), null);
            parentGoalVm.timestamp = Instant.now().minus(Duration.standardDays(2));
            parentGoalVm.targetDateChange = changeNullTo(new LocalDate(2018, 4, 7));
            commandActor.executeCommand(parentGoalVm);

            sendCommentCommand("parent + child entry", workDateUpTo50DaysInPast);
        }

        // WHEN add another smart step with the previous as the parent
        GoalUpdateCommandViewModel childGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            // create an actionInstanceUuid with work date and created 2 days ago
            childGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, UUID.randomUUID(), parentGoalVm.actionInstanceUuid);
            childGoalVm.timestamp = parentGoalVm.timestamp;
            childGoalVm.statusChange = ChangeViewModel.changeNullTo(1);
            childGoalVm.hierarchyChange = ChangeViewModel.changeNullTo((short) 1);
            String parentGoalIndex = "0";
            childGoalVm.positionChange = ChangeViewModel.changeNullTo(parentGoalIndex + "-0");
            commandActor.executeCommand(childGoalVm);
        }

        // THEN evidence has both, and the parentActionInstanceUuid populated
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);

            EvidenceSmartStepViewModel parentVm = lastEvidence.actions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(childGoalVm.parentActionInstanceUuid))
                    .findFirst()
                    .orElseThrow();
            assertNull(parentVm.parentActionInstanceUuid);

            EvidenceSmartStepViewModel childVm = lastEvidence.actions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(childGoalVm.actionInstanceUuid))
                    .findFirst()
                    .orElseThrow();
            assertEquals(parentGoalVm.actionInstanceUuid, childVm.parentActionInstanceUuid);
            assertEquals(1, childVm.hierarchy.intValue());
            assertEquals("0-0", childVm.position);
        }

        // WHEN update an existing snapshot
        UUID updateWorkUuid = UUID.randomUUID();
        {
            GoalUpdateCommandViewModel updateVm = new GoalUpdateCommandViewModel(updateWorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, childGoalVm.actionInstanceUuid, parentGoalVm.actionInstanceUuid);
            // we need to have a different 'created' date for the handler to find the previous snapshot (see commit)
            updateVm.timestamp = parentGoalVm.timestamp.plus(1000);
            updateVm.targetDateChange = changeNullTo(new LocalDate(2019,4,7));
            // NB this can execute very quickly, meaning the existing snapshot to the actionInstanceUuid is not found
            commandActor.executeCommand(updateVm);
        }

        // THEN expect the same parentActionInstanceUuid, hierarchy, position
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(updateWorkUuid);
            assertNotNull(lastEvidence);
            EvidenceSmartStepViewModel childVm = findWorkActionInstanceUuid(lastEvidence, childGoalVm.actionInstanceUuid);
            assertEquals(parentGoalVm.actionInstanceUuid, childVm.parentActionInstanceUuid);
            assertEquals(1, childVm.hierarchy.intValue());
            assertEquals("0-0", childVm.position);
        }
    }

    /**
     * Historical test to describe current behaviour.
     *
     * Until recently, processing a new entry assumes the latest snapshot was shown/observed when updating. This is because
     * the latest snapshot was shown using '>= all' based on ids. Also, the latest snapshot is assumed when saving
     * because the handlers take the item (eg actionInstanceUuid) and find the latest created (equivalent to latest id) and
     * then apply the changes to that snapshot and save.
     *
     * Always showing the latest brings thoughts around testing of older workDate or created dates.
     *  1) What happens if we enter a historical work date, and
     *  2) What happens if we save at a created timestamp that isn't the latest (eg offline - or multi-user scenario)
     *  3) What happens when people edit an existing work item
     *
     * 1) Historical work date [TESTED HERE]
     * If we choose a historical workDate, it saves as normal in the history, but the latest plan is shown from the latest item data.
     * PROBLEM: This means the front page will show what was just entered, even if you used a historical work date.
     * FIXED: This is now fixed because code now finds the latest snapshot using the latest by workDate, then created (not by id)
     * TODO UX: Maybe we should restrict 'took place on' to be > last work date, and force users to inert a new entry from the history page.
     * TODO Ensure that the snapshot is based at the right point in time
     *
     * 2) Historical created date [TESTED BELOW - historicalCreated_happyPath]
     * Processing a new entry currently assumes the latest snapshot was shown/observed when updating.
     * If we are offline and a command is syncd after a more recent command, it saves through the handler using the
     * item's snapshot at the time (eg action instance). This goes into the history with hardly any collisions since its based
     * on the snapshot at the time.
     * PROBLEM: Viewing the history will be accurate, but viewing the snapshot may miss information where a subsequent
     *          piece of work was done on the same item.
     * FIXED: NO
     * TODO When editing, ensure we are using the correct snapshot by providing workDate and created
     *
     * 3) Editing [tested elsewhere on comments only]
     * Editing currently is the comment area only - not related to historical items, so there is no issue currently.
     * Editing simply saves the comment with a newer details - the timestamp is not updated (see commentChange_cannotModifyFixedFields)
     */
    @Test
    public void historicalWorkDate_happyPath() {
        // GIVEN
        GoalUpdateCommandViewModel newGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            // create an actionInstanceUuid with work date and created 2 days ago
            newGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                            defaultEvidenceTask, actionDefId, UUID.randomUUID(), null);
            newGoalVm.timestamp = Instant.now().minus(Duration.standardDays(2));
            newGoalVm.targetDateChange = changeNullTo(new LocalDate(2015,4,7));
            commandActor.executeCommand(newGoalVm);

            sendCommentCommand("normal entry", workDateUpTo50DaysInPast);
        }

        // WHEN - creating entry using older work date (3 days behind) and newer created date (1 day newer)
        // (NB Using a newer created date would have failed if we had a snapshot test and the older snapshot code)
        GoalUpdateCommandViewModel olderVm;
        {
            // create a new work item in the past
            Instant newerCreated = Instant.now().minus(Duration.standardDays(1));
            UUID olderWorkUuid = UUID.randomUUID();
            CommentCommandViewModel ccvm = createCommentCommand(olderWorkUuid, "older work item", newerCreated, workDateUpTo50DaysInPast.minusDays(3));
            commandActor.executeCommand(ccvm);

            // The goal's 'from' details will be from the current work above, but we are inserting into the past
            // NB we stick with 'now' as the timestamp
            olderVm = new GoalUpdateCommandViewModel(olderWorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, newGoalVm.actionInstanceUuid, null);
            olderVm.timestamp = newerCreated;
            olderVm.targetDateChange = changeNullTo(new LocalDate(2018,4,7));

            commandActor.executeCommand(olderVm);
        }

        // THEN check the work has their respective data
        {
            // Check the later work (first saved) date still loads the later data
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals("normal entry", lastEvidence.comment);
            assertEquals(1, lastEvidence.actions.size());
            assertEquals(newGoalVm.actionInstanceUuid, lastEvidence.actions.get(0).actionInstanceUuid);
            // check the target date is as per the later workDate (not the latest created)
            assert lastEvidence.actions.get(0).targetDateTime != null;
            assertEquals(newGoalVm.targetDateChange.to, lastEvidence.actions.get(0).targetDateTime.toLocalDate());

            // Check the earlier work date (second saved) still loads the earlier data
            EvidenceSupportWorkViewModel earlierEvidence = findWorkByUuid(olderVm.workUuid);
            assertNotNull(earlierEvidence);
            assertEquals("older work item", earlierEvidence.comment);
            assertEquals(1, earlierEvidence.actions.size());
            assertEquals(newGoalVm.actionInstanceUuid, earlierEvidence.actions.get(0).actionInstanceUuid);
            // check the target date is as per the later workDate (not the latest created)
            assert earlierEvidence.actions.get(0).targetDateTime != null;
            assertEquals(olderVm.targetDateChange.to, earlierEvidence.actions.get(0).targetDateTime.toLocalDate());

            // Check the latest snapshot does indeed show the later work details
            DateTime snapshotWorkDate = workDateUpTo50DaysInPast.toDateTime(DateTimeZone.UTC);
            Instant snapshot = Instant.now();
            SupportSmartStepsSnapshotViewModel latestSnapshot = supportEvidenceActor.findTimestampSupportSnapshot(getServiceRecipientId(),
                    evidenceGroup, snapshotWorkDate, snapshot).getBody();
            assert latestSnapshot != null;
            assertEquals(1, latestSnapshot.latestActions.size());
            assert latestSnapshot.latestActions.get(0).targetDateTime != null;
            assertEquals(newGoalVm.targetDateChange.to, latestSnapshot.latestActions.get(0).targetDateTime.toLocalDate());
        }
    }

    @Test
    public void historicalCreated_happyPath() {
        // GIVEN
        GoalUpdateCommandViewModel newGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            // FIRST WORK ITEM (3 days ago in work and created)
            newGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, UUID.randomUUID(), null);
            newGoalVm.timestamp = Instant.now().minus(Duration.standardDays(2));
            newGoalVm.targetDateChange = changeNullTo(new LocalDate(2015,4,7));
            commandActor.executeCommand(newGoalVm);

            sendCommentCommand("normal entry", workDateUpTo50DaysInPast.minusDays(3));


            // THIRD WORK ITEM (1 day ago in work and created)
            GoalUpdateCommandViewModel newestVm;
            Instant newerCreated = Instant.now().minus(Duration.standardDays(1));
            UUID olderWorkUuid = UUID.randomUUID();
            CommentCommandViewModel ccvm = createCommentCommand(olderWorkUuid, "older work item", newerCreated, workDateUpTo50DaysInPast.minusDays(1));
            commandActor.executeCommand(ccvm);

            newestVm = new GoalUpdateCommandViewModel(olderWorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, newGoalVm.actionInstanceUuid, newGoalVm.parentActionInstanceUuid);
            newestVm.timestamp = newerCreated;
            newestVm.targetDateChange = changeNullTo(new LocalDate(2018,4,7));

            commandActor.executeCommand(newestVm);

        }

        // WHEN SECOND WORK ITEM (2 days behind in work and created)
        // This mimics when we have offline work that then gets sync'd when we already have a later work item online.
        GoalUpdateCommandViewModel middleVm;
        {
            Instant middleCreated = Instant.now().minus(Duration.standardDays(2));
            UUID middleWorkUuid = UUID.randomUUID();
            CommentCommandViewModel ccvm = createCommentCommand(middleWorkUuid, "older work item", middleCreated, workDateUpTo50DaysInPast.minusDays(2));
            commandActor.executeCommand(ccvm);

            middleVm = new GoalUpdateCommandViewModel(middleWorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, newGoalVm.actionInstanceUuid, newGoalVm.parentActionInstanceUuid);
            middleVm.timestamp = middleCreated;
            middleVm.targetDateChange = changeNullTo(new LocalDate(2016,4,7));
            middleVm.goalPlanChange = ChangeViewModel.changeNullTo("plan added");

            commandActor.executeCommand(middleVm);
        }

        // THEN test the current behaviour which is that the SECOND save does not propogate to later data the same action
        {
            // Check the latest snapshot does indeed show the later work details
            DateTime snapshotWorkDate = workDateUpTo50DaysInPast.toDateTime(DateTimeZone.UTC);
            Instant snapshot = snapshotWorkDate.toInstant();
            SupportSmartStepsSnapshotViewModel latestSnapshot = supportEvidenceActor.findTimestampSupportSnapshot(
                        getServiceRecipientId(),evidenceGroup, snapshotWorkDate, snapshot).getBody();
            // expected as null, BUT should be middleVm.goalPlanChange.to
            assertNull(latestSnapshot.latestActions.get(0).goalPlan);
        }
    }

    @Disabled("we have no 'createUser' web api yet")
    public void commentChange_doesntModifyAuthor() {
        CommentCommandViewModel vm = sendCommentCommand("this is my piece of work", workDateUpTo50DaysInPast);

        // TODO create webApi for creating user
        logout();
        String author = "Lina";
        //loginAs(username, password);

        // upate the comment from a different login
        //noinspection ConstantConditions
        CommentCommandViewModel ccvm = createCommentCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                EvidenceTask.NEEDS_ASSESSMENT, "NO - this is my piece of work", workDateUpTo50DaysInPast, vm.minsSpent.to);
        commandActor.executeCommand(ccvm);

        // TODO verify the original author is the same
        EvidenceSupportWorkViewModel workSummary = findWorkByUuid(workUuid);
        assertEquals(author, workSummary.authorDisplayName);
    }

    @Test
    public void commentChange_happyPath() {
        CommentCommandViewModel firstVm = sendCommentCommand("this is my first piece of work", workDateUpTo50DaysInPast);

        String correctedText = "this is my first CORRECTED piece of work";
        LocalDateTime correctedDate = workDateUpTo50DaysInPast.minusDays(1).withMillisOfSecond(0);
        //noinspection ConstantConditions
        Integer correctedMinsSpent = firstVm.minsSpent.to + 5;

        CommentCommandViewModel ccvm = createCommentCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                EvidenceTask.NEEDS_ASSESSMENT, correctedText, correctedDate, correctedMinsSpent);
        commandActor.executeCommand(ccvm);

        EvidenceSupportWorkViewModel workSummary = findWorkByUuid(workUuid);
        assertEquals(correctedText, workSummary.comment);
        assertEquals(correctedDate, workSummary.workDate);
        assertEquals(correctedMinsSpent, workSummary.minsSpent);
    }

    @Test
    public void commentChange_allowNullComment() {
        CommentCommandViewModel ccvm = createCommentCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                EvidenceTask.NEEDS_ASSESSMENT, null, new LocalDateTime(2015,8,3,0,0), null);
        commandActor.executeCommand(ccvm);
    }

    @Test
    public void commentChange_cannotModifyFixedFields() {

        String txt = "this is the first piece of work";
        CommentCommandViewModel firstVm = sendCommentCommand(txt, workDateUpTo50DaysInPast);
        EvidenceSupportWorkViewModel workSummaryFirst = findWorkByUuid(workUuid);
        // extract to immutable fields, so not comparing the reloaded object in session
        LocalDateTime created = workSummaryFirst.createdDate;
        String taskName = workSummaryFirst.taskName;
        // TODO String evidenceGroupId = workSummaryFirst.;

        // update some stuff
        //noinspection ConstantConditions
        CommentCommandViewModel ccvm = createCommentCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                EvidenceTask.NEEDS_ASSESSMENT, txt, workDateUpTo50DaysInPast, firstVm.minsSpent.to);
        commandActor.executeCommand(ccvm);

        // verify fixed fields not changed
        EvidenceSupportWorkViewModel workSummary = findWorkByUuid(workUuid);
        assertEquals(created, workSummary.createdDate);
        assertEquals(taskName, workSummary.taskName);
        // TODO assertEquals(evidenceGroupId, workSummary.sourceTaskGroup);
    }

    @Test
    public void commentChange_cannotModifyReferralId() {
        String txt = "this is the first piece of work";
        CommentCommandViewModel vm = sendCommentCommand(txt, workDateUpTo50DaysInPast);

        // supply a wrong serviceRecipientId for the existing workUuid so should throw an error
        //noinspection ConstantConditions
        CommentCommandViewModel ccvm = createCommentCommandViewModel(workUuid, -99, evidenceGroup,
                EvidenceTask.NEEDS_ASSESSMENT, txt, workDateUpTo50DaysInPast, vm.minsSpent.to);
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(ccvm));
    }

    @Test
    public void commandOrder_saveWorkItemThenCommentShouldAppendToWorkItem() {
        BaseGoalUpdateCommandViewModel riskUpdateCmd = sendDefaultGoalUpdateCommand();

        CommentCommandViewModel ccvm = sendCommentCommand("look around... isn't nature amazing", workDateUpTo50DaysInPast);

        BaseWorkViewModel lastEvidence = getAndVerifyUpdateGotApplied(riskUpdateCmd);
        verifyThatEvidenceMatchesCommentCommand(ccvm, lastEvidence);
    }

    @Test
    public void commandOrder_goalUpdateCommandShouldCreateWorkWithNoComment() {
        BaseGoalUpdateCommandViewModel updateCmd = sendDefaultGoalUpdateCommand();

        BaseWorkViewModel lastEvidence = getAndVerifyUpdateGotApplied(updateCmd);
        verifyNoCommentAndDefaultWorkDate(lastEvidence, updateCmd.timestamp);
    }

    @Test
    public void commandOrder_commentThenActionCommandShouldAppendToWorkAndPreserveComment() {
        CommentCommandViewModel commentCmd = sendCommentCommand("children are the future", workDateUpTo50DaysInPast);

        BaseGoalUpdateCommandViewModel goalUpdateCommand = sendDefaultGoalUpdateCommand();

        BaseWorkViewModel lastEvidence = getAndVerifyUpdateGotApplied(goalUpdateCommand);
        verifyThatEvidenceMatchesCommentCommand(commentCmd, lastEvidence);
    }

    /**
     * We currently allow the ChangeViewModel.to to simply overwrite the existing field
     * and whilst this is the case our commands can come in any order, and the workDate etc can be
     * updated when the work/comment command comes through. However, if we enforce the 'from' then
     * any subsequent work/comment command will fail since information has been defaulted to create
     * the initial work item.
     * Either this can be resolved client-side by setting the 'from' as the created date, or we handle
     * server-side by allowing an update where the 'from' is null, but the workdate = created date.
     * Regardless, we have this test to highlight the problem should any enforcing come into play.
     */

    @Test
    public void commandOrder_associatedThenComment() {
        // GIVEN
        GoalUpdateCommandViewModel vm;
        UUID instanceUuid = UUID.randomUUID();
        {
            // see also @Before createUniqueClientAndReferral

            // save some work with a 'blank' goal update
            vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, instanceUuid, null);
            vm.isRelevant = true;
            commandActor.executeCommand(vm);
            workUuid = vm.workUuid;
        }

        // WHEN
        {
            // a comment and piece of work exists
            sendCommentCommand("this is my associated piece of work2", workDateUpTo50DaysInPast);
        }

        // THEN
        {
            EvidenceSupportWorkViewModel lastEvidence = readBackEvidence(vm.workUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.associatedActions).hasSize(1);
            assertThat(actionDefId).isEqualTo(lastEvidence.associatedActions.get(0).intValue());
        }
    }

    @Test
    public void commandOrder_actionThenCommentShouldUpdateWork() {
        sendDefaultGoalUpdateCommand();

        CommentCommandViewModel commentCmd = sendCommentCommand("blah blah", workDateUpTo50DaysInPast);

        verifyThatEvidenceMatchesCommentCommand(commentCmd, findWorkByUuid(workUuid));
    }

}
