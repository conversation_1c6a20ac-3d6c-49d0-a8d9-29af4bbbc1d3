package com.ecco.acceptancetests.api.rota;

import static org.hamcrest.CoreMatchers.endsWith;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

import java.net.URI;
import java.util.List;
import java.util.Random;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.actors.CalendarActor;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jayway.jsonpath.JsonPath;

public class AvailabilityAPITests extends BaseJsonTest {

    private final String sysadminCal = "e37aede8-9f45-4bef-b6a5-d7ad27ca7841";
    private final DateTime weekBack = new DateTime().minusDays(7);
    private final DateTime weekForward = new DateTime().plusDays(7);

    @Test
    public void availRequestForSysAdminShouldReturnSomething() {
        assertThat(apiBaseUrl, endsWith("/api/"));

        ResponseEntity<String> response = calendarActor.getAvailability(sysadminCal, weekBack, weekForward);

        assertThat(response.getStatusCode(), is(HttpStatus.OK));
//        System.out.println(response.getBody());

//        System.out.println("links:" + JsonPath.read(response.getBody(), "$.links"));
//        System.out.println("dtStart:" + JsonPath.read(response.getBody(), "$.dtStart"));
//        System.out.println("dtEnd:" + JsonPath.read(response.getBody(), "$.dtEnd"));
//        System.out.println("available:" + JsonPath.read(response.getBody(), "$.available"));
    }

    @Test
    public void updatingAvailabilityShouldReturnLocationOfUpdatedRecord() {
        ResponseEntity<String> response = calendarActor.getAvailability(sysadminCal, weekBack, weekForward);

        assertThat(response.getStatusCode(), is(HttpStatus.OK));

        final List<Object> list = JsonPath.read(response.getBody(), "$.links[?(@.rel == 'collection')].href");
        final HttpHeaders postHeaders = new HttpHeaders();
        postHeaders.setContentType(MediaType.APPLICATION_JSON);

        // Create a random new available period
        JsonObject parsedAvailability = new JsonParser().parse(response.getBody()).getAsJsonObject();
        // FIXME: I think all this should be LocalDateTime.parse() etc, but we're still passing some millis in
        DateTime dtStart = ISODateTimeFormat.dateTime().parseDateTime(parsedAvailability.get("dtStart").getAsString());

        DateTime availableStart = dtStart.plusHours(new Random().nextInt(240));
        DateTime availableEnd = availableStart.plusHours(new Random().nextInt(8) + 1);

        JsonObject newAvailable = new JsonObject();
        newAvailable.addProperty("dtStart", availableStart.toDate().getTime());
        newAvailable.addProperty("dtEnd", availableEnd.toDate().getTime());
        final JsonArray availables = new JsonArray();
        availables.add(newAvailable);
        parsedAvailability.add("available", availables);

        final HttpEntity<String> postRequest = new HttpEntity<>(parsedAvailability.toString(), postHeaders);
        final URI createdLocation = restTemplate.postForLocation(list.get(0).toString(), postRequest);

        // This location should be the same as where we started.
        assertEquals("Created location", URI.create(apiBaseUrl + "calendar/availability/" + sysadminCal
                + '/' + CalendarActor.YYYYMMDD.print(weekBack) + '-' + CalendarActor.YYYYMMDD.print(weekForward)), createdLocation);

        // So fetching it again should include our updated availability.
        response = restTemplate.getForEntity(createdLocation, String.class);
        assertEquals("One available period", 1, ((List<Object>) JsonPath.read(response.getBody(), "$.available[*]")).size());
        assertEquals("Check available start", availableStart.withZone(DateTimeZone.UTC).toString(),
                JsonPath.read(response.getBody(), "$.available[0].dtStart"));
        assertEquals("Check available end", availableEnd.withZone(DateTimeZone.UTC).toString(),
                JsonPath.read(response.getBody(), "$.available[0].dtEnd"));
    }

}
