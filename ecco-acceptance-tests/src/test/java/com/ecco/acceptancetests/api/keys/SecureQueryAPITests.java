package com.ecco.acceptancetests.api.keys;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.WebApiSettings;
import com.ecco.infrastructure.rest.ThrowOnlyOn400And5xxErrorHandler;
import com.ecco.webApi.viewModels.UserDeviceViewModel;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URI;
import java.util.Base64;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.springframework.hateoas.IanaLinkRelations.SELF;

public class SecureQueryAPITests extends BaseJsonTest {
    @BeforeEach
    public void changeRestTemplateErrorHandler() {
        restTemplate.setErrorHandler(new ThrowOnlyOn400And5xxErrorHandler());
    }

    @Test
    public void givenValidUserDeviceAndQueryRequiringAuthenticationWhenQuerySubmittedThenExecutesOK() throws IOException, InvalidCipherTextException {
        final UserDeviceViewModel userDevice = userDeviceActor.createNewUserDeviceKey().getBody();
        logout(); // Ensure we're not pre-authenticated for the next step.

        final UUID userDeviceIdentifier = UUID.fromString(userDevice.guid);
        // Fetch the same user device using the secure query API
        final ResponseEntity<UserDeviceViewModel> response = securePayloadActor.executeSecureJsonQuery(UserDeviceViewModel.class, userDeviceIdentifier, Base64.getDecoder().decode(userDevice.base64Key), URI.create("/api/keys/userDevices/valid/" + userDeviceIdentifier.toString() + "/"));
        assertEquals(HttpStatus.OK, response.getStatusCode());
        // Also check the contents.
        assertEquals(userDevice.base64Key, response.getBody().base64Key);
        assertEquals(userDevice.cipher, response.getBody().cipher);
        assertEquals(userDevice.guid, response.getBody().guid);
        assertEquals(userDevice.valid, response.getBody().valid);
        assertEquals(WebApiSettings.APPLICATION_URL + "/api/keys/userDevices/valid/" + userDeviceIdentifier.toString() + "/",
                response.getBody().getRequiredLink(SELF).getHref());
    }

}
