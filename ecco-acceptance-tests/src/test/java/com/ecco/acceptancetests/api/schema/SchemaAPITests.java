package com.ecco.acceptancetests.api.schema;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * @since 20/01/2016
 */
public class SchemaAPITests extends BaseJsonTest {

    @Test
    public void getReferralListSchema() {
        ResponseEntity<JsonSchema> response = schemaActor.getSchema("referrals");
        Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
    }
}
