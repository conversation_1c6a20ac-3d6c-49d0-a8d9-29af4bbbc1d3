package com.ecco.acceptancetests.api.hr;

import com.ecco.dto.ChangeViewModel;
import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.acceptancetests.api.referral.TaskClientDetailAbstractCommandAPITests;
import com.ecco.webApi.contacts.WorkerViewModel;
import com.ecco.webApi.taskFlow.StaffTaskStaffDetailCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.workers.WorkerJobDetailCommandViewModel;
import org.springframework.http.ResponseEntity;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpClientErrorException.BadRequest;

import static org.junit.Assert.*;

import java.time.LocalDate;

/**
 * An isolated test, in that it does not form part of DefaultReferralStepsWebApiSupport because
 * we do not yet have a testing-flow with this in it.
 */
public class StaffCommandAPITests extends BaseJsonTest {

    WorkerViewModel vm;
    TaskClientDetailAbstractCommandAPITests workerDetail = new TaskClientDetailAbstractCommandAPITests();

    @Test
    public void canEditStaff() {

        // GIVEN a job and a worker
        ensureStaff();

        // WHEN update the worker
        {
            StaffTaskStaffDetailCommandViewModel cmd = new StaffTaskStaffDetailCommandViewModel(vm.jobs.get(0).getServiceRecipient().serviceRecipientId, null);
            this.workerDetail.populateSuccess(cmd, vm);
            commandActor.executeCommand(cmd);
        }

        // THEN we get the updated details back
        {
            WorkerViewModel wvmUpdated = workerActor.getWorker(vm.workerId).getBody();
            assert wvmUpdated != null;
            this.workerDetail.checkSuccess(wvmUpdated);
        }
    }

    @Test
    public void failToEditStaff() {

        // GIVEN a referral and a client
        ensureStaff();

        // WHEN update the worker with dodgy data
        {
            StaffTaskStaffDetailCommandViewModel cmd = new StaffTaskStaffDetailCommandViewModel(vm.jobs.get(0).getServiceRecipient().serviceRecipientId, null);
            this.workerDetail.populateFail(cmd, vm);
            assertThrows(BadRequest.class, () -> commandActor.executeCommand(cmd));
        }
    }

    @Test
    public void canEditJob() {

        // GIVEN a job and a worker
        ensureStaff();

        // WHEN update the job - choose not today because that is the default in createWorker
        var startDate = LocalDate.now().minusYears(1);
        {
            WorkerJobDetailCommandViewModel cmd = new WorkerJobDetailCommandViewModel(vm.jobs.get(0).getServiceRecipient().serviceRecipientId, null);
            cmd.setCode(ChangeViewModel.changeNullTo("j-code"));
            cmd.setStartDate(ChangeViewModel.changeNullTo(startDate));
            cmd.setContractedWeeklyHours(ChangeViewModel.changeNullTo(15));

            commandActor.executeCommand(cmd);
        }

        // THEN we get the updated details back
        {
            WorkerViewModel wvmUpdated = workerActor.getWorker(vm.workerId).getBody();
            assert wvmUpdated != null;
            assertEquals(wvmUpdated.getJobs().get(0).getStartDate(), startDate);
        }
    }

    private void ensureStaff() {
        if (this.vm == null) {
            String firstNameUnique = UniqueDataService.instance.clientFirstNameFor("EditingOnly");
            String lastNameUnique = UniqueDataService.instance.clientLastNameFor("StaffCmd");

            ResponseEntity<Result> result = workerActor.createWorker(firstNameUnique, lastNameUnique);
            var workerId = Long.parseLong(result.getBody().getId());
            workerActor.createWorkerJob(workerId, LocalDate.now());
            this.vm = workerActor.getWorker(workerId).getBody();
        }
    }
}
