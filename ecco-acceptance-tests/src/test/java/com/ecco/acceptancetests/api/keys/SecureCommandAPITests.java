package com.ecco.acceptancetests.api.keys;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.infrastructure.rest.ThrowOnlyOn400And5xxErrorHandler;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.UserDeviceViewModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.net.URI;
import java.util.Base64;
import java.util.UUID;

public class SecureCommandAPITests extends BaseJsonTest {
    @BeforeEach
    public void changeRestTemplateErrorHandler() {
        restTemplate.setErrorHandler(new ThrowOnlyOn400And5xxErrorHandler());
    }

    @Test
    public void givenValidUserDeviceAndCommandRequiringAuthenticationWhenCommandSubmittedThenExecutesOK() throws JsonProcessingException, InvalidCipherTextException {
        final UserDeviceViewModel userDevice = userDeviceActor.createNewUserDeviceKey().getBody();
        logout(); // Ensure we're not pre-authenticated for the next step.

        final UUID userDeviceIdentifier = UUID.fromString(userDevice.guid);
        final ResponseEntity<Result> response = securePayloadActor.executeSecureJsonCommand(userDeviceIdentifier, Base64.getDecoder().decode(userDevice.base64Key),
                HttpMethod.GET, URI.create("/api/keys/userDevices/valid/" + userDeviceIdentifier.toString() + "/"), null);
        Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
    }
}
