package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.acceptancetests.ui.pages.Role;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.infrastructure.time.Clock;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.rota.webApi.dto.*;
import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralSummaryViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.listsConfig.AppointmentTypeViewModel;
import com.ecco.webApi.rota.ServiceRecipientAppointmentScheduleCommandDto;
import com.ecco.webApi.rota.ServiceRecipientAppointmentScheduleDirectTaskCommandDto;
import com.ecco.webApi.taskFlow.ReferralTaskExitCommandViewModel;
import kotlin.Pair;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import org.jspecify.annotations.Nullable;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static com.ecco.data.client.steps.ReferralStepDefinitions.EXIT;
import static com.ecco.infrastructure.time.JodaToJDKAdapters.*;
import static com.ecco.dto.AddedRemovedDto.added;
import static com.ecco.dto.AddedRemovedDto.addedRemoved;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static java.util.Collections.singletonList;
import static java.util.Objects.requireNonNull;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;

/**
 * Also see RotaReferralAPITests and ClientInvoicingTests
 */
@SuppressWarnings({"ConstantConditions", "deprecation"})
public class RotaScheduleAPITests extends BaseJsonTest {

    private final Clock clock = Clock.DEFAULT;
    protected final org.joda.time.DateTime now = clock.now();
    protected final java.time.ZonedDateTime nowJdk = clock.nowJdk();
    protected final org.joda.time.LocalDate today = now.toLocalDate();
    protected final ZonedDateTime nextMon3pm = nowJdk.with(TemporalAdjusters.next(DayOfWeek.MONDAY)).withHour(15).withMinute(0).withSecond(0).withNano(0);


    @Test
    public void schedules_dayOfWeekAfterStart() {
        loginAsSysadmin();

        var svm = GIVEN_ReferralSummary("FirstName", "createDayAfterStart");

        // GIVEN referral agreement - today for 30 days
        agreementActor.createAppointmentType(DEMO_ALL, svm.firstName, 60);
        agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusDays(30), null);

        // GIVEN referral schedule - now (in 2 hours) every week for 2 months
        // but with tomorrow's dayOfWeek, it will start then - see EntryConverter.adjustStartToDayOfWeek
        DayOfWeek tomorrow = LocalDateTime.now().plusDays(1).getDayOfWeek();
        DaysOfWeek dow = DaysOfWeek.fromCalendarDayISO(tomorrow);
        rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId,
                nowJdk.toLocalDateTime().plusHours(2), dow, clock.nowJdk().toLocalDate().plusMonths(2), 0, null);

        // THEN test schedules today should not exist - we have set the day for tomorrow
        Rota rotaToday = rotaActor.getWholeOrgRotaOnDate(now);
        assertThat("Expect appointment returned", rotaToday.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(0));

        // THEN test schedules tomorrow
        Rota rotaTomorrow = rotaActor.getWholeOrgRotaOnDate(now.plusDays(1));
        assertThat("Expect appointment returned", rotaTomorrow.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));

        logout();
    }

    @Test
    public void schedules_dayOfWeekBeforeStartAdds6Days() {
        loginAsSysadmin();

        var svm = GIVEN_ReferralSummary("FirstName", "createDayBeforeStart");

        // GIVEN referral agreement - today for 30 days
        agreementActor.createAppointmentType(DEMO_ALL, svm.firstName, 60);
        agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusDays(30), null);

        // GIVEN referral schedule - now (in 2 hours) every week for 2 months on yesterday's dayOfWeek
        DayOfWeek yesterday = LocalDateTime.now().minusDays(1).getDayOfWeek();
        DaysOfWeek dow = DaysOfWeek.fromCalendarDayISO(yesterday);
        rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId, nowJdk.toLocalDateTime().plusHours(2), dow, clock.nowJdk().toLocalDate().plusMonths(2), 0, null);

        // THEN test schedules today should not exist - we have set the day for 6 days time
        Rota rotaToday = rotaActor.getWholeOrgRotaOnDate(now);
        assertThat("Expect appointment returned", rotaToday.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(0));

        // THEN test schedules in 6 days time
        Rota rotaTomorrow = rotaActor.getWholeOrgRotaOnDate(now.plusDays(6));
        assertThat("Expect appointment returned", rotaTomorrow.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));

        logout();
    }

    @Test
    public void schedules_doubleUp() {
        // create schedules at LocalDateTime.now().minusDays(1).withHour(14).withMinute(15).withSecond(0).withNano(0);
        var aptToday = DateTime.now().withHourOfDay(14).withMinuteOfHour(15).withSecondOfMinute(0).withMillisOfSecond(0);
        var schedules = createScheduleWithAdditionalStaff("FirstName", "doubleUp", LocalDate.now().plusDays(2));

        // assert that the recurrences have "managedByUri": "entity://AppointmentSchedule/<scheduleId>" (and not /null)
        // so the created schedules
        schedules.component2().forEach(s -> {
            var rota = rotaActor.getRotaPartialOnDate(false, true, "workers:all", "referrals:all:" + s.getServiceRecipientId(), aptToday);
            var managedBy = rota.getDemandAppointments().get(0).getManagedByUri();
            assertNotEquals(managedBy.toString(), "entity://AppointmentSchedule/null");
        });
    }

    // FAILED on actions/runs/2806842659 6th Aug 2022 without any real changes
    // FAILED on actions/runs/4332858644 4th Mar 2023 (9:08pm GMT) 22m45s without any real changes
    @Test
    public void schedules_createTruncateExtend() {
        loginAsSysadmin();

        // GIVEN user
        var userName = unique.userNameFor("worker-truncExt");
        var pwd = unique.passwordFor("worker-truncExt");
        userManagementSteps.createIndividualWithUser(userName, pwd, userName + "-first", userName + "-last",Role.staff);

        // GIVEN worker
        rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, null);

        var svm = GIVEN_ReferralSummary("FirstName", "truncateExtend");

        // GIVEN agreement for today + 5 months
        agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusMonths(5), null);

        // GIVEN schedule - repeating every day (at 9pm for 10 mins) every week for the whole agreement
        // without an hour, the build can run at 9.51pm causing 'now + 2 hours' to cause issues on the matcher below
        var allDays = DaysOfWeek.fromBits(127);

        rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId, nowJdk.toLocalDate().atTime(LocalTime.of(21, 0)), allDays, null, 0, null);

        // GIVEN confirm schedules at 2 weeks time
        var rota = rotaActor.getWholeOrgRotaOnDate(now.plusWeeks(2));
        // matching over midnight causes 2 apts not 1
            //2021-12-12T21:51:35.4019901Z Expected: an iterable with size <1>
            //        2021-12-12T21:51:35.4020577Z      but: iterable size was <2>
            //2021-12-12T21:51:35.4026479Z 	at com.ecco.acceptancetests.api.rota.RotaScheduleAPITests.schedules_createTruncateExtend(AgreementScheduleAPITests.java:137) ~[test-classes/:?]
        assertThat("Expect appointment returned", rota.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));

        {
            // WHEN truncate schedule by setting endDate to startDate
            var sched = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody().get(0);
            var endDateUpdated = localDateToJDk(sched.getStart());
            updateSchedule("update", svm.serviceRecipientId, sched, null, endDateUpdated, null, null, null);

            // THEN we should have 1 schedule with an endDate
            var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
            assertThat("Expected one schedule returned", schedulesUpdated, iterableWithSize(1));
            assertEquals("end date doesn't match", sched.getStart(), schedulesUpdated.get(0).getEnd());

            // AND we should not have any demand tomorrow
            // FAILED on actions/runs/4393611874 11th Mar 2023 with same error
            // FAILED on actions/runs/4332858644 4th Mar 2023 without any real changes
            //  at this point, with relevant logs of:
            /*
            ...
            2023-03-04T21:30:17.5374667Z [INFO] [talledLocalContainer] 2023-03-04 21:30:17,533 INFO [com.ecco.rota.service.RotaDelegator] sysadmin        - Handling workers:all, referrals:all with ReferralWorkerRotaHandler
            2023-03-04T21:30:17.5537317Z [INFO] [talledLocalContainer] 2023-03-04 21:30:17,553 INFO [httpLog] sysadmin        - GET /ecco-war/api/rota/agreements/101206/appointments/ null http 964830E08CC064237031612689D56FDD sysadmin 127.0.0.1 Apache-HttpClient/4.5.13 (Java/17.0.6)
            2023-03-04T21:30:17.5601829Z [INFO] [talledLocalContainer] 2023-03-04 21:30:17,559 INFO [httpLog] sysadmin        - GET /ecco-war/api/rota/workers:all/view serviceRecipientFilter=referrals:all&startDate=2023-03-05 http 964830E08CC064237031612689D56FDD sysadmin 127.0.0.1 Apache-HttpClient/4.5.13 (Java/17.0.6)
            2023-03-04T21:30:17.5617781Z [INFO] [talledLocalContainer] 2023-03-04 21:30:17,561 INFO [com.ecco.rota.service.RotaDelegator] sysadmin        - Handling workers:all, referrals:all with ReferralWorkerRotaHandler
            2023-03-04T21:30:17.8680375Z [INFO] [talledLocalContainer] 2023-03-04 21:30:17,864 ERROR [com.ecco.webApi.controllers.EccoWebApiControllerAdvice] sysadmin        - No entry with handle 8c13ac2d-9343-47bb-b572-a1202948ae9c exists.
            at findRecurrenceExceptions(CosmoCalendarRecurringService.java:449
            from BaseRotaHandler.addDemandWithStatuses(BaseRotaHandler.java:59)
            from getRota(RotaController.java:120)
            from getWholeOrgRotaOnDate(RotaActor.java:51)
            from RotaScheduleAPITests.schedules_createTruncateExtend(RotaScheduleAPITests.java:158)
             */
            Rota rotaUpdated = rotaActor.getWholeOrgRotaOnDate(now.plusDays(1)); // blows up with /ecco-war/api/rota/workers:all/view serviceRecipientFilter=referrals:all&startDate=2023-03-26
            assertThat("Expect appointment returned", rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(0));
        }
        {
            // WHEN extend schedule by setting endDate to startDate plus 3 months
            var sched = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody().get(0);
            var endIn3Months = localDateToJDk(sched.getStart().plusMonths(3));
            updateSchedule("update", svm.serviceRecipientId, sched, null, endIn3Months, null, null, null);

            // THEN we should have 1 schedule with an endDate
            var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
            assertThat("Expected one schedule returned", schedulesUpdated, iterableWithSize(1));
            assertEquals("end date doesn't match", sched.getStart().plusMonths(3), schedulesUpdated.get(0).getEnd());

            {   // AND we should have  demand in 3 months
                var rotaUpdated = rotaActor.getWholeOrgRotaOnDate(now.plusMonths(3));
                assertThat(rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));
            }

            {   // AND we should not have any demand the day after
                var rotaUpdated = rotaActor.getWholeOrgRotaOnDate(now.plusMonths(3).plusDays(1));
                assertThat(rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(0));
            }
        }

        logout();
    }


    @Test
    public void schedules_createUpdateEndDate() {
        loginAsSysadmin();

        // GIVEN one agreement with 2 schedules (one with a parentId)
        LocalDate end = LocalDate.now().plusMonths(2);
        var svm_schedulesUpdated = createScheduleWithAdditionalStaff("Additional", "updateWithAdditionalStaff", end);
        ReferralSummaryViewModel svm = referralActor.getReferralSummaryByServiceRecipientId(svm_schedulesUpdated.component1().serviceRecipientId).getBody();

        // WHEN edit a recurring agreement (reduce additionalStaff to 0)
        var schedules = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
        RecurringDemandScheduleDto ds = schedules.stream().filter(s -> s.getParentScheduleId() == null).findFirst().orElseThrow();
        org.joda.time.LocalDate updatedEndJoda = ds.getEnd().plusDays(5);
        LocalDate updatedEndDate = localDateToJDk(updatedEndJoda);
        updateSchedule("update", svm.serviceRecipientId, ds, null, updatedEndDate, null, null, null);

        // THEN we should have 2 schedules updated (one with parentId)
        var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now.plusDays(6)).getBody();
        assertThat("Expected two schedules returned", schedulesUpdated, iterableWithSize(2));
        long matchesEnd = schedulesUpdated.stream().filter(s -> updatedEndJoda.equals(s.getEnd())).count();
        assertEquals("Expected two schedules with new end date", 2, matchesEnd);

        logout();
    }

    @Test
    public void schedules_createAllocateSingleThenSplit() {
        loginAsSysadmin();

        LocalDateTime startSchedule = nowJdk.plusDays(1).withHour(15).withMinute(0).withSecond(0).withNano(0).toLocalDateTime();
        LocalDateTime allocationDate = startSchedule.plusWeeks(1);
        LocalDateTime splitDate = startSchedule.plusDays(1);

        final String workerFirstName = "Bobby";
        final String workerLastName = unique.lastNameFor("userRota1");
        String userName = unique.userNameFor("userRota1");
        String clientLastName = "createAssignThenSplit1";

        splitScheduleTest(startSchedule, allocationDate, splitDate, workerFirstName, workerLastName, userName, clientLastName, splitDate.plusWeeks(2).toLocalDate());
        logout();
    }

    /**
     * This test captures when a schedule end is updated (eg to null) but the handle no longer exists
     * in the series table because two entries replaced it in an allocate.
     */
    @Test
    public void schedules_createPartAllocateRecurringThenUpdateEnd() {
        loginAsSysadmin();

        createPartAllocateRecurringThenUpdateEnd("userPartAllocEnd");

        logout();
    }

    // see also RotaReferralAPITests#adHocScheduleWithAllocate
    @Test
    public void scheduleWithAllocate() {
        loginAsSysadmin();

        // GIVEN user
        var userName = unique.userNameFor("worker-schedAlloc");
        var pwd = unique.passwordFor("worker-schedAlloc");
        userManagementSteps.createIndividualWithUser(userName, pwd, userName + "-first", userName + "-last",Role.staff);
        // GIVEN worker
        var worker = rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, null);

        // GIVEN REFERRAL
        var svm = GIVEN_ReferralSummary("FirstName", "schedAlloc");

        // GIVEN agreement for today + 5 months
        agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusMonths(5), null);

        // GIVEN schedule - repeating every day (at 9pm for 10 mins) every week for the whole agreement
        // without an hour, the build can run at 9.51pm causing 'now + 2 hours' to cause issues on the matcher below
        var allDays = DaysOfWeek.fromBits(127);

        // GIVEN allocation
        rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId, nowJdk.toLocalDate().atTime(LocalTime.of(21, 0)), allDays, null, 0, worker.getServiceRecipientId());

        // GIVEN confirm schedules at 2 weeks time
        var rota = rotaActor.getWholeOrgRotaOnDate(now.plusWeeks(2));

        // no demand expected since - its allocated
        List<RotaAppointmentViewModel> pendingAppointments = rota.findDemandByServiceRecipientName(svm.clientDisplayName);
        assertThat("Expect no appointments returned", pendingAppointments, iterableWithSize(0));
        // allocated expected
        List<RotaAppointmentViewModel> allocatedAppointments = rota.findResourceByName(worker.getName()).findAppointmentsByServiceRecipientName(svm.clientDisplayName);
        assertThat("Expect appointment returned", allocatedAppointments, iterableWithSize(1));

        var apt = rota.findResourceByName(worker.getName()).findAppointmentsByServiceRecipientName(svm.clientDisplayName).get(0);
        // check we find allocated appt and it matches the setup
        log.info("Setup of schedTest [worker; client; adHocAgreementId; adHocScheduleId; adHocEventRef]: {}; {}; {}, {}, {}",
                worker.getName(), svm.clientDisplayName, apt.getAgreementId(), apt.getScheduleId(), apt.getRef());

        logout();
    }

    private ReferralSummaryViewModel createPartAllocateRecurringThenUpdateEnd(String shortTestName) {
        LocalDateTime startSchedule = nextMon3pm.toLocalDateTime();

        String workerFirstName = "Pat";
        String userName = unique.userNameFor(shortTestName);
        String clientLastName = "client-" + shortTestName;

        // GIVEN referral schedule, worker etc
        ReferralSummaryViewModel svm;
        var workerLastName = unique.lastNameFor(shortTestName);
        final String workerName = workerFirstName + ' ' + workerLastName;
        {
            // agreement from today for 30 days
            var daysToAllocate = DaysOfWeek.fromCalendarDaysISO(List.of(1,2,3,4,5,6,7));
            svm = createUserAndWorkerWithAvailabilityAndReferralWithAgreement("Pat", workerLastName, userName, clientLastName);
            rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId, startSchedule, daysToAllocate, clock.nowJdk().toLocalDate().plusMonths(2), 0, null);
        }

        // GIVEN PART ALLOCATE - to get entries in the series table
        {
            var daysToAllocate = DaysOfWeek.fromCalendarDaysISO(List.of(1,2,3));
            rotaSteps.checkCanAssignResourceToRecurringAppointmentAtTime("workers:all", "referrals:all", svm.displayName, workerName,
                    startSchedule, daysToAllocate, null);
        }

        // WHEN update schedule end to be null
        {
            // WHEN update the schedule end date to be null
            var schedules = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, dateTimeToJoda(nextMon3pm)).getBody();
            RecurringDemandScheduleDto ds = schedules.stream().filter(s -> s.getParentScheduleId() == null).findFirst().orElseThrow();
            updateSchedule("update", svm.serviceRecipientId, ds, null, null, null, null, null);
        }

        // THEN we have no error, and still have some entries
        var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, dateTimeToJoda(nextMon3pm)).getBody();
        assertThat("Expected one schedule returned", schedulesUpdated, iterableWithSize(1));
        var ds = schedulesUpdated.stream().filter(s -> s.getPreviousScheduleId() == null).toList();
        assertThat("Expected one schedule with previousScheduleId null returned", ds.size(), is(1));
        assertThat("Expected one schedule with end date null", ds.stream().filter(s -> s.getEnd() == null).count(), is(1L));
        RecurringDemandScheduleDto dsUpdated = ds.stream().filter(s -> s.getEnd() == null).findFirst().orElseThrow();
        assertNull("Expected end date to be null", dsUpdated.getEnd());

        // THEN we should have demand as normal
        {
            var rotaUpdated = rotaActor.getWholeOrgRotaOnDate(startSchedule.plusDays(3)); // gets us to Thurs, first unalloc/demand
            assertThat(rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));
            assertNotEquals(rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName).get(0).getManagedByUri().toString(), "entity://AppointmentSchedule/null");
        }
        return svm;
    }

    /**
     * Test the triggering of getSeriesLowestHighest against a null end date in updateRecurringEntryBounds.
     * It requires entries to exist with no end date, and then set an end dates.
     */
    @Test
    public void schedules_givenWithoutEndThenWithEnd() {
        loginAsSysadmin();

        // GIVEN a schedule with some series entries
        // this creates a schedule ending 'nowJdk.toLocalDate().plusMonths(2)', assigns MTuW, update end to blank
        var svm = this.createPartAllocateRecurringThenUpdateEnd("withoutThenEnd");

        // WHEN update its bounds to anything (triggering the getSeriesLowestHighest on null dates)
        {
            var schedule = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, dateTimeToJoda(nowJdk.with(TemporalAdjusters.next(DayOfWeek.MONDAY)))).getBody().get(0);
            updateSchedule("update", svm.serviceRecipientId, schedule, null, nowJdk.plusWeeks(2).toLocalDate(), null, null, null);
        }

        // THEN
        // passes if there is no failure/NPE

        logout();
    }

    /**
     * Test the scenario of 'if (entry.getStart().toLocalDate().isAfter(endDate)) {' in the CalendarRecurringSeriesDecorator.
     * We need to test when a schedule has series entries with an end date, then end dates are cleared
     * and then end dates are set again.
     * There will be a series with a future schedule because removing the end dates created one 'forever'
     * and yet by setting an end date again, the future series needs deleting.
     */
    @Test
    public void schedules_givenWithoutEndThenWithEndBeforeFutureSchedule() {
        loginAsSysadmin();

        // GIVEN a schedule with some series entries
        // this creates a schedule starting MONDAY and ending 'nowJdk.toLocalDate().plusMonths(2)', assigns MTuW, update end to blank
        var svm = this.createPartAllocateRecurringThenUpdateEnd("futureSched");

        // WHEN update its bounds to anything (triggering the getSeriesLowestHighest on null dates)
        var end = nextMon3pm.plusDays(1).toLocalDate();
        {
            var schedule = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, dateTimeToJoda(nowJdk.with(TemporalAdjusters.next(DayOfWeek.MONDAY)))).getBody().get(0);
            updateSchedule("update", svm.serviceRecipientId, schedule, null, end, null, null, null);
        }

        // THEN check the demand ended
        {
            var checkEnded = end.with(TemporalAdjusters.next(DayOfWeek.MONDAY)).atStartOfDay();
            var rotaUpdated = rotaActor.getWholeOrgRotaOnDate(checkEnded);
            assertThat(rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(0));
        }

        // TODO Check rota loads

        logout();
    }

    /**
     * Test the scenario of 'recurringEntriesInRange' in the CalendarRecurringSeriesDecorator
     * where the endBeforeStart <= 0 needs to be < 0.
     * This caused some entries to be skipped from processing - those where start=end date.
     * So we create some series entries, and get one entry to be for one day.
     */
    @Test
    public void schedules_singleEntryMissed() {
        loginAsSysadmin();

        // GIVEN a schedule with some series entries
        // this creates a schedule from Mon every day ending 'nowJdk.toLocalDate().plusMonths(2)', assigns MTuW, update end to blank
        var shortTestName = "singleEntry";
        String workerNameLast = unique.lastNameFor(shortTestName);
        final String workerName = "Pat " + workerNameLast;
        var svm = this.createPartAllocateRecurringThenUpdateEnd(shortTestName);

        // GIVEN part allocate all but one day, to get one entry spanning 1 day
        {
            var daysToAllocate = DaysOfWeek.fromCalendarDaysISO(List.of(5,6,7)); // Fr,Sa,Su (leave Th)
            var fri = nextMon3pm.plusDays(4).toLocalDateTime();
            rotaSteps.checkCanAssignResourceToRecurringAppointmentAtTime("workers:all", "referrals:all", svm.displayName, workerName,
                    fri, daysToAllocate, null);
        }

        // WHEN part allocate over/on the split day
        var thurs = nextMon3pm.plusDays(3).toLocalDateTime();
        {
            var daysToAllocate = DaysOfWeek.fromCalendarDaysISO(List.of(1,2,3,4)); // Mo,Tu,We,Th (include 'hanging' Th)
            rotaSteps.checkCanAssignResourceToRecurringAppointmentAtTime("workers:all", "referrals:all", svm.displayName, workerName,
                    thurs, daysToAllocate, null);
        }

        // TEST there isn't an entry for the Thurs on its own that is TENTATIVE
        // as this entry on its own then overlaps with the one that is allocated
        // since it wasn't picked up and processed correctly
        {
            var rotaUpdated = rotaActor.getWholeOrgRotaOnDate(thurs.toLocalDate().atStartOfDay());
            assertThat(rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(0));
        }


        // WHEN we close the referral
        var startSchedule = nextMon3pm.toLocalDateTime();
        var closeDate = startSchedule.plusDays(10).toLocalDate();
        var tasks = workflowActor.getTasksByTaskName(svm.serviceRecipientId);
        var cmd = new ReferralTaskExitCommandViewModel(svm.serviceRecipientId, tasks.get(EXIT).taskHandle);
        cmd.exitedDateChange = changeNullTo(JodaToJDKAdapters.localDateToJoda(closeDate));
        referralActor.executeCommand(cmd);

        // THEN expect the schedule end date to change to the close date
        {
            var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, dateTimeToJoda(nextMon3pm)).getBody();
            var ds = schedulesUpdated.stream().filter(s -> s.getPreviousScheduleId() == null).collect(Collectors.toList());
            assertThat("Expected one schedule with previousScheduleId null returned", ds.size(), is(1));
            assertThat("Expected one schedule with end date null", ds.stream().filter(s -> s.getEnd() != null).count(), is(1L));
            RecurringDemandScheduleDto dsUpdated = ds.stream().filter(s -> s.getEnd() != null).findFirst().orElseThrow();
            assertThat("Expected schedule end date to match close date", dsUpdated.getEnd().toString(), equalTo(closeDate.toString()));
        }
        logout();
    }

    @Test
    public void schedules_createAllocateSingleThenSplitOnAllocation() {
        loginAsSysadmin();

        LocalDateTime startSchedule = nowJdk.plusDays(1).withHour(15).withMinute(0).withSecond(0).withNano(0).toLocalDateTime();
        LocalDateTime allocationDate = startSchedule.plusWeeks(1);
        LocalDateTime splitDate = allocationDate;

        final String workerFirstName = unique.firstNameFor("userRota2");
        final String workerLastName = unique.lastNameFor("userRota2");
        String userName = unique.userNameFor("userRota2");
        String clientLastName = "createAssignThenSplit2";

        splitScheduleTest(startSchedule, allocationDate, splitDate, workerFirstName, workerLastName, userName, clientLastName, null);
        logout();
    }

    private void splitScheduleTest(LocalDateTime startSchedule, LocalDateTime allocationDate, LocalDateTime splitDate,
                                   String workerFirstName, String workerLastName, String userName, String clientLastName,
                                   LocalDate endDate) {
        splitScheduleTest(startSchedule, allocationDate, splitDate, workerFirstName, workerLastName, userName,
                clientLastName, endDate, null);
    }

    private void splitScheduleTest(LocalDateTime startSchedule, LocalDateTime allocationDate, LocalDateTime splitDate,
                                   String workerFirstName, String workerLastName, String userName, String clientLastName,
                                   LocalDate endDate,
                                   @Nullable List<ServiceRecipientAppointmentScheduleDirectTaskCommandDto> taskChanges) {

        // GIVEN a worker, referral, and schedule with allocated worker (a week tomorrow)
        ReferralSummaryViewModel svm;
        var splitDateJoda = localDateTimeToJoda(splitDate).toLocalDate();
        int newDayIso = splitDateJoda.getDayOfWeek();
        {
            final String workerName = workerFirstName + ' ' + workerLastName;

            // agreement from today for 30 days
            svm = createUserAndWorkerWithAvailabilityAndReferralWithAgreement(workerFirstName, workerLastName, userName, clientLastName);

            // GIVEN schedule - repeating tomorrow at 3pm for 10 mins every week for 2 months
            rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId, startSchedule, null, clock.nowJdk().toLocalDate().plusMonths(2),0, taskChanges, null);

            // GIVEN allocate a worker a week tomorrow at 3pm
            rotaSteps.checkCanAssignResourceToSingleAppointmentAtTime("workers:all", "referrals:all", svm.clientDisplayName, workerName, allocationDate);

            // GIVEN allocated apt in 1 week tomorrow at 3pm
            Rota rotaInOneWeek = rotaActor.getWholeOrgRotaOnDate(startSchedule.plusWeeks(1));
            assertThat("Expect appointment returned", rotaInOneWeek.findResourceByName(workerName).findAppointmentsByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));

            // GIVEN schedule exists at 2 weeks tomorrow at 3pm
            Rota rotaInTwoWeeks = rotaActor.getWholeOrgRotaOnDate(startSchedule.plusWeeks(2));
            assertThat("Expect appointment returned", rotaInTwoWeeks.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));
        }

        // WHEN split this week (and change to day after)
        {
            // WHEN split the day after the schedule start - which will cause the confirmed appointment above to be a problem
            var schedules = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
            RecurringDemandScheduleDto ds = schedules.get(0);
            var startScheduleJoda = localDateTimeToJoda(startSchedule).toDateTime();
            int removeDayIso = startScheduleJoda.getDayOfWeek();
            int removeDayNonIso = removeDayIso + 1 > 7 ? 1 : removeDayIso + 1;
            int newDayNonIso = newDayIso + 1 > 7 ? 1 : newDayIso + 1;
            // splits day after schedule (which is day after tomorrow) and sets that day
            updateSchedule("split", svm.serviceRecipientId, ds, splitDate, endDate, newDayNonIso, removeDayNonIso, null);
        }

        // THEN expect no error on the allocation in a week
        {
            // THEN expect to see two schedules for the referral - one ended, and one created with the same details
            var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
            assertThat("Expected two schedules returned", schedulesUpdated, iterableWithSize(2));
            // NB previousSchedule is the pre-split schedule, whereas the parentSchedule involves the children clones for multiple workers
            // so previousSchedule is 1 or more because the original is still in place, just this is split from it (and it may be > 1 if its a split of a split)
            assertThat("Expected one schedule with previousScheduleId null returned", schedulesUpdated.stream().filter(s -> s.getPreviousScheduleId() == null).count(), is(1L));
            // one ends the day before the split - as each day represents all hours of that day, inclusive
            Predicate<RecurringDemandScheduleDto> predicate = pojo -> pojo.getEnd().equals(splitDateJoda.minusDays(1));
            long matches = schedulesUpdated.stream().filter(predicate).count();
            assertEquals("Expected one to have endDate set to day before start (TODO: We must check ad-hoc doesn't do a split?)", 1, matches);

            // AND we should have only one demand beyond the split, on the new day
            Rota rotaUpdated = rotaActor.getWholeOrgRotaOnDate(splitDateJoda.plusWeeks(2).toDateTimeAtStartOfDay());
            List<RotaAppointmentViewModel> entries = rotaUpdated.findDemandByServiceRecipientName(svm.clientDisplayName);
            assertThat("Expect appointment returned", entries, iterableWithSize(1));
            int scheduleDayOfWeekIso = entries.get(0).getStart().toLocalDate().getDayOfWeek().getValue();
            assertEquals("Expect schedule day changed", newDayIso, scheduleDayOfWeekIso);
        }

        // THEN expect the schedule to have maintained its tasks
        {
            if (taskChanges != null && taskChanges.size() > 0) {
                var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
                // one ends the day before the split - as each day represents all hours of that day, inclusive
                Predicate<RecurringDemandScheduleDto> predicate = pojo -> pojo.getStart().equals(splitDateJoda);
                var scheduleUpdated = schedulesUpdated.stream().filter(predicate).collect(Collectors.toList()).get(0);

                var tasks = rotaActor.getDirectTaskInstanceIdsForSchedule(scheduleUpdated.getScheduleId());
                assertThat("Expect direct tasks linked", tasks, iterableWithSize(1));

                // THEN smart steps exist with task details
                // NB a far future snapshot date is needed as applicableFrom/start is the work date,
                // and this will likely be in the near future and could hide the latest on the schedule
                var futureDate = DateTime.now().plusYears(99);
                var snapshot = supportEvidenceActor.findTimestampSupportSnapshot(svm.serviceRecipientId,
                        EvidenceGroup.NEEDS, futureDate, futureDate.toInstant()).getBody();
                var match = snapshot.latestActions.stream()
                        .filter(a -> a.actionInstanceUuid.toString().equals(tasks.get(0).getTaskInstanceId())).collect(Collectors.toList());
                assertThat("Expect smartsteps returned", match, iterableWithSize(1));
            }
        }
    }

    @Test
    @Disabled("works locally")
    public void schedules_splitWithAdditionalStaff() {
        loginAsSysadmin();

        // GIVEN one agreement with 2 schedules (one with a parentId)
        LocalDate end = LocalDate.now().plusMonths(2);
        var svm_schedulesUpdated = createScheduleWithAdditionalStaff("Additional", "splitWithAdditionalStaff", end);
        ReferralSummaryViewModel svm = referralActor.getReferralSummaryByServiceRecipientId(svm_schedulesUpdated.component1().serviceRecipientId).getBody();

        // WHEN edit a recurring agreement (reduce additionalStaff to 0)
        var schedules = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
        RecurringDemandScheduleDto ds = schedules.get(0);
        org.joda.time.LocalDate splitDateJoda = ds.getStart().plusDays(5);
        LocalDateTime splitDate = localDateToJDk(splitDateJoda).atStartOfDay();
        updateSchedule("split", svm.serviceRecipientId, ds, splitDate, null, null, null, null);

        // THEN we should have 4 schedules (two with parentId)
        var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now.plusDays(6)).getBody();
        assertThat("Expected four schedules returned", schedulesUpdated, iterableWithSize(4));
        long matchesLive = schedulesUpdated.stream().filter(s -> splitDateJoda.isAfter(s.getEnd())).count();
        assertEquals("Expected two schedules live", 2, matchesLive);
        long matchesLiveWithParentId = schedulesUpdated.stream().filter(s -> splitDateJoda.isAfter(s.getEnd()) && s.getParentScheduleId() != null).count();
        assertEquals("Expected one to be live with parentId", 1, matchesLiveWithParentId);

        logout();
    }

    @Test
    @Disabled("works locally")
    public void schedules_splitWithLessAdditionalStaff() {
        loginAsSysadmin();

        // GIVEN one agreement with 2 schedules (one with a parentId)
        LocalDate end = LocalDate.now().plusMonths(2);
        var svm_schedulesUpdated = createScheduleWithAdditionalStaff("Additional", "splitWithLessAdditionalStaff", end);
        ReferralSummaryViewModel svm = referralActor.getReferralSummaryByServiceRecipientId(svm_schedulesUpdated.component1().serviceRecipientId).getBody();

        // WHEN edit a recurring agreement (reduce additionalStaff to 0)
        var schedules = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
        RecurringDemandScheduleDto ds = schedules.get(0);
        org.joda.time.LocalDate splitDateJoda = ds.getStart().plusDays(5);
        LocalDateTime splitDate = localDateToJDk(splitDateJoda).atStartOfDay();
        updateSchedule("split", svm.serviceRecipientId, ds, splitDate, null, null, null, 0);

        // THEN we should have 3 schedules (one with parentId)
        var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now.plusDays(6)).getBody();
        // 1 new one (with no additionalStaff), the other two stopped
        assertThat("Expected three schedules returned", schedulesUpdated, iterableWithSize(3));
        long matchesLive = schedulesUpdated.stream().filter(s -> splitDateJoda.isBefore(s.getEnd())).count();
        assertEquals("Expected one schedule live", 1, matchesLive);
        long matches = schedulesUpdated.stream().filter(s -> splitDateJoda.isAfter(s.getEnd()) && s.getParentScheduleId() != null).count();
        assertEquals("Expected one to be not live with parentId", 1, matches);

        logout();
    }

    @Test
    public void schedules_splitWithMoreAdditionalStaff() {
        loginAsSysadmin();

        // GIVEN one agreement with 2 schedules (one additionalStaff with a parentId)
        var svm_schedulesUpdated = createScheduleWithAdditionalStaff("Additional", "splitWithMoreAdditionalStaff", null);
        ReferralSummaryViewModel svm = referralActor.getReferralSummaryByServiceRecipientId(svm_schedulesUpdated.component1().serviceRecipientId).getBody();

        // WHEN edit a recurring agreement (increase additionalStaff to 2)
        var schedules = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
        RecurringDemandScheduleDto ds = schedules.get(0);
        org.joda.time.LocalDate splitDateJoda = ds.getStart().plusDays(5);
        LocalDateTime splitDate = localDateToJDk(splitDateJoda).atStartOfDay();
        updateSchedule("split", svm.serviceRecipientId, ds, splitDate, null, null, null, 2);

        // THEN we should have 5 schedules - 2 originals split = 4, 1 new
        var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now.plusDays(6)).getBody();
        assertThat("Expected five schedules returned", schedulesUpdated, iterableWithSize(5));
        long matchesLive = schedulesUpdated.stream().filter(s -> s.getEnd() == null || splitDateJoda.isBefore(s.getEnd())).count();
        assertEquals("Expected three schedule live (2 additionalStaff)", 3, matchesLive);
        long matches = schedulesUpdated.stream().filter(s -> s.getEnd() != null && splitDateJoda.isAfter(s.getEnd()) && s.getParentScheduleId() != null).count();
        assertEquals("Expected two to be not live, of which one with parentId", 1, matches);

        logout();
    }

    @Test
    public void schedules_createAllocateSingleWithTasksDirect() {
        loginAsSysadmin();

        var svm = GIVEN_ReferralSummary("FirstName", "createAllocateTasks");

        // GIVEN referral agreement
        agreementActor.createAppointmentType(DEMO_ALL, svm.firstName, 60);
        agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusDays(30), null);

        // GIVEN referral schedule - now (in 2 hours) every week for 2 months
        // WITH tasksDirect
        DayOfWeek today = LocalDateTime.now().getDayOfWeek();
        DaysOfWeek dow = DaysOfWeek.fromCalendarDayISO(today);
        var task1 = new ServiceRecipientAppointmentScheduleDirectTaskCommandDto();
        task1.operation = BaseCommandViewModel.OPERATION_ADD;
        task1.taskDefId = 1704; // "bins put out and were emptied"
        task1.taskInstanceId = UUID.randomUUID().toString();
        var result = rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId,
                nowJdk.toLocalDateTime().plusHours(2), dow, clock.nowJdk().toLocalDate().plusMonths(2), 0, Collections.singletonList(task1), null);

        // THEN test schedules exist with tasks
        var sched = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody().get(0);
        var tasks = rotaActor.getDirectTaskInstanceIdsForSchedule(sched.getScheduleId());
        assertThat("Expect direct tasks linked", tasks, iterableWithSize(1));

        // THEN smart steps exist with task details
        // NB a far future snapshot date is needed as applicableFrom/start is the work date,
        // and this will likely be in the near future and could hide the latest on the schedule
        var futureDate = DateTime.now().plusYears(99);
        var snapshot = supportEvidenceActor.findTimestampSupportSnapshot(svm.serviceRecipientId,
                EvidenceGroup.NEEDS, futureDate, futureDate.toInstant()).getBody();
        var match = snapshot.latestActions.stream()
                .filter(a -> a.actionInstanceUuid.toString().equals(tasks.get(0).getTaskInstanceId())).collect(Collectors.toList());
        assertThat("Expect smartsteps returned", match, iterableWithSize(1));

        // do an update with no tasks changes, and expect same tasks back (with no error)
        // WHEN truncate schedule by setting endDate to startDate
        var endDateUpdated = localDateToJDk(sched.getStart());
        updateSchedule("update", svm.serviceRecipientId, sched, null, endDateUpdated, null, null, null);

        // THEN test schedules exist with tasks
        var tasksUpdated = rotaActor.getDirectTaskInstanceIdsForSchedule(sched.getScheduleId());
        assertThat("Expect direct tasks linked", tasksUpdated, iterableWithSize(1));

        logout();
    }

    @Test
    public void tasksDirectWithSplit() {
        loginAsSysadmin();

        var svm = GIVEN_ReferralSummary("FirstName", "createAllocateSplitTasks");

        LocalDateTime startSchedule = nowJdk.plusDays(1).withHour(15).withMinute(0).withSecond(0).withNano(0).toLocalDateTime();
        LocalDateTime allocationDate = startSchedule.plusWeeks(1);
        LocalDateTime splitDate = startSchedule.plusDays(1);

        final String workerFirstName = unique.firstNameFor("userRotaTasks");
        final String workerLastName = unique.lastNameFor("userRotaTasks");
        String userName = unique.userNameFor("userRotaTasks");

        // create and split a schedule, with direct tasks
        var task1 = new ServiceRecipientAppointmentScheduleDirectTaskCommandDto();
        task1.operation = BaseCommandViewModel.OPERATION_ADD;
        task1.taskDefId = 1704; // "bins put out and were emptied"
        task1.taskInstanceId = UUID.randomUUID().toString();
        splitScheduleTest(startSchedule, allocationDate, splitDate, workerFirstName, workerLastName, userName, svm.getLastName(), null, Collections.singletonList(task1));

        logout();
    }

    private Pair<ServiceRecipientSummary, List<RecurringDemandScheduleDto>> createScheduleWithAdditionalStaff(String firstName,
                                                                                                              String lastName,
                                                                                                              LocalDate endDate) {
        var svm = GIVEN_ReferralSummary(firstName, lastName);

        // GIVEN agreement
        final AgreementResource clientAgreement;
        {
            final String agreementAppointmentType = agreementActor.createAppointmentType(DEMO_ALL, unique.nameFor("aptType"), 60);
            agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusDays(30), null);
            clientAgreement = agreementActor.getActiveAgreementsOnDate(svm.serviceRecipientId, now).get(0);
        }

        // WHEN create a new schedule for 2 workers
        LocalDateTime start = LocalDateTime.now().minusDays(1).withHour(14).withMinute(15).withSecond(0).withNano(0);
        ServiceRecipientAppointmentScheduleCommandDto cmd = createBaseSchedule(clientAgreement, start, endDate);
        cmd.additionalStaff = 1;
        commandActor.executeCommand(cmd);

        // THEN we should have 2 schedules
        var schedulesUpdated = agreementActor.getAppointmentSchedulesFromAgreement(svm.serviceRecipientId, now).getBody();
        assertThat("Expected two schedules returned", schedulesUpdated, iterableWithSize(2));

        Predicate<RecurringDemandScheduleDto> predicate = pojo -> pojo.getParentScheduleId() != null;
        long matches = schedulesUpdated.stream().filter(predicate).count();
        assertEquals("Expected one to have parentId", 1, matches);

        return new Pair<>(svm, schedulesUpdated);
    }

    @Nullable
    private ReferralSummaryViewModel GIVEN_ReferralSummary(String firstName, String lastName) {
        String firstNameUnique = unique.clientFirstNameFor(firstName);
        String lastNameUnique = unique.clientLastNameFor(lastName);
        ReferralViewModel rvm = referralActor.createMinimalReferralAndClient(null, firstNameUnique, lastNameUnique, DEMO_ALL);
        return referralActor.getReferralSummaryById(rvm.referralId).getBody();
    }

    /**
     * @param addDay NON-ISO day number (SUN = 1)
     */
    private void updateSchedule(String operation, int serviceRecipientId, RecurringDemandScheduleDto ds, LocalDateTime start,
                                @Nullable LocalDate end, @Nullable Integer addDay, @Nullable Integer removeDay,
                                @Nullable Integer additionalStaff) {
        ServiceRecipientAppointmentScheduleCommandDto cmd = new ServiceRecipientAppointmentScheduleCommandDto(operation, serviceRecipientId);
        cmd.eventRef = ds.getEventRef();
        if (operation.equals("split")) {
            cmd.applicableFromDate = start.toLocalDate();
        }
        else if (start != null) {
            cmd.startDate = ChangeViewModel.create(localDateToJDk(ds.getStart()), start.toLocalDate());
        }

        if (start != null) {
            cmd.time = ChangeViewModel.create(JodaToJDKAdapters.localTimeToJDk(ds.getTime()), start.toLocalTime());
        }
        cmd.endDate = ChangeViewModel.create(localDateToJDk(ds.getEnd()), end);
        if (addDay != null) {
            cmd.days = added(singletonList(addDay));
        }
        if (removeDay != null) {
            cmd.days = addedRemoved(new ArrayList<>(requireNonNull(ds.getCalendarDays())), null, singletonList(removeDay));
        }
        cmd.additionalStaff = additionalStaff;

        commandActor.executeCommand(cmd);
    }

    private ReferralSummaryViewModel createUserAndWorkerWithAvailabilityAndReferralWithAgreement(String workerFirstName, String workerLastName, String userName, String clientLastName) {
        // GIVEN user
        String pwd = unique.passwordFor(userName);
        userManagementSteps.createIndividualWithUser(userName, pwd, userName + "-first", userName + "-last", Role.staff);

        // GIVEN worker - availability doesn't matter, its only a ui thing
        rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, workerFirstName, workerLastName, null);

        var svm = GIVEN_ReferralSummary("FirstName", clientLastName);

        // GIVEN agreement
        agreementActor.createAppointmentType(DEMO_ALL, userName, 60);
        agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusDays(30), null);

        return svm;
    }

    public ServiceRecipientAppointmentScheduleCommandDto createBaseSchedule(AgreementResource agreement, LocalDateTime start, LocalDate end) {
        ServiceRecipientAppointmentScheduleCommandDto cmd = new ServiceRecipientAppointmentScheduleCommandDto(BaseCommandViewModel.OPERATION_ADD, agreement.getServiceRecipientId());
        cmd.eventRef = null; // confirm its an 'add' operation
        cmd.agreementId = agreement.getAgreementId().intValue();
        //cmd.workerId = optional
        AppointmentTypeViewModel apptType = agreementActor.getAppointmentTypes().get(0);
        cmd.appointmentTypeId = ChangeViewModel.changeNullTo(apptType.id);
        cmd.startDate = changeNullTo(start.toLocalDate());
        cmd.time = changeNullTo(start.toLocalTime());
        cmd.durationMins = changeNullTo(10);

        // repeating part
        cmd.endDate = end == null ? null : changeNullTo(end);
        cmd.withISODayOfWeek(DayOfWeek.from(nowJdk));

        return cmd;
    }

}
