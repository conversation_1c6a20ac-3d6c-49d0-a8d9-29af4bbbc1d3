package com.ecco.acceptancetests.api.reports;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.time.Clock;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.CommentCommandViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.evidence.EvidenceSupportWorkViewModel;
import com.ecco.webApi.viewModels.DateTimeUpdateCommand;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeMatcher;
import org.joda.time.*;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static com.ecco.dom.EvidenceGroup.NEEDS;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThat;

/**
 *  Test reports - currently around timezone edges (see PredicateSupport for this list we copied):
 *   1: presented local user date put against utc system datetime:
 *      eg 2015-08-03 against 2015-08-03 00:43 UTC, query needs to be 2015-08-02 23:00 (in BST) to capture all UTC records
 *   2: presented local user date+time put against utc system datetime:
 *      eg 2015-08-03 00:00 against 2015-08-03 00:43 UTC, query needs to be 2015-08-02 23:00 (in BST) to capture all UTC records
 *   3: presented local user date put against utc user datetime (but should be date only):
 *      eg 2015-08-03 against 2015-08-03 00:43 UTC, do no translation since both are incorrect so work fine as-is
 *   4: presented local user date+time put against utc user datetime:
 *      eg 2015-08-03 00:00 against 2015-08-03 00:43 UTC, do no translation since both are incorrect so work fine as-is
*/

// TODO - We've covered tests around a user date and a system date - we should cover all the fields used in ReferralStatusCommonPredicates

@SuppressWarnings({"unused", "UnnecessaryLocalVariable"})
public class ReportDatesAPITests extends BaseJsonTest {

    private Clock clock = Clock.DEFAULT;
    DateTime now = clock.now();

    private static Matcher<ReferralViewModel> matchReferralId(final long referralId) {
        return matchReferralId(equalTo(referralId));
    }

    private static Matcher<ReferralViewModel> matchReferralId(final Matcher<Long> delegate) {
        return new TypeSafeMatcher<>() {
            @Override
            protected boolean matchesSafely(ReferralViewModel item) {
                return delegate.matches(item.getReferralId());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("a view model with referralId ").appendDescriptionOf(delegate);
            }
        };
    }

    private static final class ExtractReferralId implements Function<ReferralViewModel, Long> {
        @Override
        public Long apply(ReferralViewModel input) {
            return input.getReferralId();
        }
    }

    private static final class ExtractWorkId implements Function<EvidenceSupportWorkViewModel, UUID> {
        @Override
        public UUID apply(EvidenceSupportWorkViewModel input) {
            return input.id;
        }
    }

    @Test
    public void canUpdateSystemDate() {
        LocalDateTime created = new LocalDateTime(2014,2,14,0,23);

        long referralId = this.createReferral("2014-02-11").referralId;
        this.resetCreatedDateTime(referralId, created);

        ReferralViewModel referral = referralActor.getReferralById(referralId).getBody();
        assertThat(referral.created, is(created.toDateTime(DateTimeZone.UTC)));
    }

    /**
     * Tests around a system date - 'created'
     * Scenario 1: user date (local) against system date (utc)
     * Scenario 2: is also covered since the comparison is against a datetime field, so a time has to be provided, in
     * this case its (midnight) - see ReportController and PredicateSupport
     */
    @Test
    public void userDateAgainstSystemDateTime() {
        // when requesting dates NOT in BST, we test for report dates:
        //     >= 13th Feb 2014 00:00 UTC
        //     <  14th Feb 2014 00:00 UTC
        testsAroundCreated("2014-02-13", false);

        // when requesting dates in BST, we test for report dates:
        //     >= 02th Aug 2014 23:00 UTC
        //     <  03th Aug 2014 23:00 UTC
        testsAroundCreated("2014-08-03", true);
    }

    /**
     * Tests around a user date - 'workdate'
     * Scenario 3: user date (local) against user date (utc)
     * Scenario 4: is also covered since the comparison is against a datetime field, so a time has to be provided, in
     * this case its (midnight) - see ReportController and PredicateSupport
     */
    @Test
    public void userDateAgainstUserDateTime() {
        // when requesting dates NOT in BST, we test for report dates:
        //     >= 13th Feb 2014 00:00 UTC
        //     <  14th Feb 2014 00:00 UTC
        testsAroundWorkDate("2014-02-13", false);
        // when requesting dates in BST, we test for report dates:
        //     >= 03th Aug 2014 00:00 UTC
        //     <  04th Aug 2014 00:00 UTC
        // BECAUSE the user provied the date to save and to query - so they will match
        // in whatever tz
        testsAroundWorkDate("2014-08-03", true);
    }

    private void testsAroundCreated(String from, boolean isWithinBST) {
        String to = from; // yyyy-MM-dd
        String status = "created";
        int preExisting = this.runReport(from, to, status).length;
        Set<Long> referralIdsExpected = new HashSet<>();

        // system created referral on baseDate 00:23 UTC
        LocalDate baseDate = ISODateTimeFormat.date().parseLocalDate(from);
        long referralId = this.createReferral("2014-02-11").referralId;
        this.resetCreatedDateTime(referralId, baseDate.toLocalDateTime(
                new LocalTime(DateTimeZone.UTC).withHourOfDay(0).withMinuteOfHour(10)));
        referralIdsExpected.add(referralId);

        // create another referral within the previous hour, on the previous day
        long referralId2 = this.createReferral("2014-02-10").referralId;
        this.resetCreatedDateTime(referralId2, baseDate.minusDays(1).toLocalDateTime(
                new LocalTime(DateTimeZone.UTC)).withHourOfDay(23).withMinuteOfHour(52));
        if (isWithinBST) {
            referralIdsExpected.add(referralId2);
        }

        // create another referral within the previous hour again, on the previous day
        long referralId3 = this.createReferral("2014-02-10").referralId;
        this.resetCreatedDateTime(referralId3, baseDate.minusDays(1).toLocalDateTime(
                new LocalTime(DateTimeZone.UTC)).withHourOfDay(22).withMinuteOfHour(52));

        // create another referral at the end of the same day
        long referralId4 = this.createReferral("2014-02-09").referralId;
        this.resetCreatedDateTime(referralId4, baseDate.toLocalDateTime(
                new LocalTime(DateTimeZone.UTC)).withHourOfDay(23).withMinuteOfHour(52));
        if (!isWithinBST) {
            referralIdsExpected.add(referralId4);
        }

        // create another referral just in the next day
        long referralId5 = this.createReferral("2014-02-08").referralId;
        this.resetCreatedDateTime(referralId5, baseDate.plusDays(1).toLocalDateTime(
                new LocalTime(DateTimeZone.UTC)).withHourOfDay(0).withMinuteOfHour(12));

        // report includes referrals
        ReferralViewModel[] results = this.runReport(from, to, status);
        assertThat(results.length - preExisting, is(referralIdsExpected.size()));
        List<Long> referralIdsArr = Lists.transform(Arrays.asList(results), new ExtractReferralId());
        org.junit.Assert.assertTrue(referralIdsArr.containsAll(referralIdsExpected));
        /*
        // hamcrest seems to insist on the whole array, so we go with guava as above
        assertThat(referralIdsArr, containsInAnyOrder(referralId));
        assertThat("referral view model results", Arrays.asList(results), containsInAnyOrder(
                matchReferralId(referralId)));
                //referralViewModelWithReferralId(referralId2)));
        assertThat(Arrays.asList(results), contains(
                hasProperty("referralId", is(referralId))));//
                //hasProperty("referralId", is(referralId2))));
         */
    }

    private void testsAroundWorkDate(String from, boolean isWithinBST) {
        String to = from; // yyyy-MM-dd
        int preExisting = this.runReportSupport(from, to).length;
        Set<UUID> workIdsExpected = new HashSet<>();

        // create referral
        int serviceRecipientId = this.createReferral("2014-02-11").serviceRecipientId;

        // user saves work date at baseDate 00:23 UTC
        // the work date is saved as if it is UTC - see SupportCommandCommandHandler and EvidenceBuilder.withWorkDate
        LocalDateTime baseDateTime = ISODateTimeFormat.date().parseLocalDate(from).toLocalDateTime(LocalTime.MIDNIGHT);
        LocalDateTime firstWorkDateTime = baseDateTime.withHourOfDay(0).withMinuteOfHour(23);
        UUID work1 = this.createWork(serviceRecipientId, firstWorkDateTime);
        workIdsExpected.add(work1);
        // check work on referral
        List<EvidenceSupportWorkViewModel> supportWorkRefs =
                supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(serviceRecipientId, NEEDS).getBody().getContent();
        assertThat(supportWorkRefs.size(), is(1));
        assertThat(supportWorkRefs.get(0).workDate, is(firstWorkDateTime));

        // user saves work date within the previous hour, on the previous day
        // this is a user given date, and read back as given in legacy support, and a local date time in new support
        UUID work2 = this.createWork(serviceRecipientId, baseDateTime.minusDays(1).withHourOfDay(23).withMinuteOfHour(52));
        // we never expect this back - since the user provides the work and query date

        // user saves work date within the previous hour again, on the previous day
        UUID work3 = this.createWork(serviceRecipientId, baseDateTime.minusDays(1).withHourOfDay(22).withMinuteOfHour(52));
        // we never expect this back - since the user provides the work and query date

        // user saves work date at the end of the same day
        UUID work4 = this.createWork(serviceRecipientId, baseDateTime.withHourOfDay(23).withMinuteOfHour(52));
        workIdsExpected.add(work4);

        // user saves work date just in the next day
        UUID work5 = this.createWork(serviceRecipientId, baseDateTime.plusDays(1).withHourOfDay(0).withMinuteOfHour(12));
        // we never expect this back - since the user provides the work and query date

        // check work on report
        EvidenceSupportWorkViewModel[] results = this.runReportSupport(from, to);
        assertThat(results.length - preExisting, is(workIdsExpected.size()));
        List<UUID> referralIdsArr = Lists.transform(Arrays.asList(results), new ExtractWorkId());
        org.junit.Assert.assertTrue(referralIdsArr.containsAll(workIdsExpected));
    }

    private ReferralViewModel createReferral(String received) {
        // referral saved with receivedDate assuming its UTC (as per current referral form save operation)
        long clientId = clientActor.createClient("Billy", "Turner");
        DateTime receivedDate = new LocalDate(2014, 2, 13).toDateTimeAtStartOfDay(DateTimeZone.UTC);
        ReferralViewModel rvm = new ReferralViewModel();
        rvm.setClientId(clientId);
        rvm.setReceivedDate(receivedDate.toLocalDate());
        rvm.importServiceName = ServiceOptions.DEMO_ALL.getServiceName();
        long referralId = referralActor.createReferralFromCommand(rvm);

        rvm = referralActor.getReferralById(referralId).getBody();
        return rvm;
    }

    private void resetCreatedDateTime(long referralId, LocalDateTime newCreatedDate) {
        DateTimeUpdateCommand cmd = new DateTimeUpdateCommand();
        // the command assumes the localdate is in UTC
        cmd.newValue = newCreatedDate;
        updateAuditTimestampCommandActor.dateTimeUpdateReferral(referralId, "created", cmd);
    }

    private ReferralViewModel[] runReport(String from, String to, String status) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setFrom(from);
        dto.setTo(to);
        dto.setReferralStatus(status);
        ResponseEntity<ReferralViewModel[]> responseReport = reportActor.getReportReferrals(dto, 0);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertNotNull(responseReport.getBody());
        return responseReport.getBody();
    }

    private UUID createWork(int serviceRecipientId, LocalDateTime workDate) {
        // create work
        UUID workUuid = UUID.randomUUID();
        CommentCommandViewModel ccvm = new CommentCommandViewModel(workUuid, serviceRecipientId,
                NEEDS, EvidenceTask.NEEDS_ASSESSMENT);
        ccvm.workDate = ChangeViewModel.create(null, workDate);
        commandActor.executeCommand(ccvm);
        return workUuid;
    }

    private EvidenceSupportWorkViewModel[] runReportSupport(String from, String to) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setFrom(from);
        dto.setTo(to);
        dto.setReferralStatus(null);
        ResponseEntity<EvidenceSupportWorkViewModel[]> responseReport = reportActor.getReportSupport(dto, 0);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertNotNull(responseReport.getBody());
        return responseReport.getBody();
    }

}
