package com.ecco.acceptancetests.api.building;

import com.ecco.acceptancetests.api.BaseJsonTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Tests for the buildings are done on the rota, or standard referral files (since it's the same functionality)
 * However, this class is to help us remember this, and to document the setup
 */
@SuppressWarnings({"unused"})
public class BuildingAPITests extends BaseJsonTest {

    @Test
    @Disabled("only for local setup for now")
    public void configSetup() {
        // update cfg_module set enabled=1 where name='building';
        // ui - staff permission

        // SECURITY / NIGHT STAFF
        /*
        my/team file qr code to url
            https://test.eccosolutions.co.uk/test12/nav/ myteam /
        full file qr code to url
            https://test.eccosolutions.co.uk/test12/nav/r/main/sr2/206759/emergencyDetails
            https://test.eccosolutions.co.uk/test12/nav/r/main/sr2/206759/tasks/compliance

        NB the my/team renders cards in ReferralActionsCard - from MyReferralsList -> ReferralsListControl -> ReferralSummaryControl


        // CONFIG
        // ======
        /*
        service config for -100 'building-default'
        referralView (allowNext for all steps...)
        needsAttachment
        endFlow

        create+add new 'compliance' evidence support page
            (after ‘start’ - just because)
            titleRaw compliance
            tookPlaceOn? timer
            showOnQuickLog
            commentWidth wide
            showMenus overview
            clientStatusListName clientStatus-list
            showVisualStyle tabular-new (else pops up lots)
            showOutcomes none
            actAs assessment
            showCommentComponents attachments,minutesSpent
            outcomesById ? - tabular-new still shows ‘internal’ ‘external’ smart step
            accessAudit y
        security user ‘security’ only ticked, adding services (aaa?)
        */
    }

}
