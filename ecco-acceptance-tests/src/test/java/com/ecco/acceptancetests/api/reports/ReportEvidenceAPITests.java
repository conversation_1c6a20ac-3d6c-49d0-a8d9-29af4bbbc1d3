package com.ecco.acceptancetests.api.reports;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.time.Clock;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.CommentCommandViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.evidence.EvidenceSupportWorkViewModel;
import org.joda.time.*;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;
import java.util.stream.Collectors;

import static com.ecco.dom.EvidenceGroup.NEEDS;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThat;

/**
 *  Test reports - currently around timezone edges (see PredicateSupport for this list we copied):
*/

public class ReportEvidenceAPITests extends BaseJsonTest {

    private Clock clock = Clock.DEFAULT;
    private final LocalDateTime someTimeNear = clock.nowWithoutMillies().toLocalDateTime().minusDays(5).withHourOfDay(10).withMinuteOfHour(15);
    private final LocalDateTime someTimeAgo = clock.nowWithoutMillies().toLocalDateTime().minusDays(50).withHourOfDay(10).withMinuteOfHour(15);

    @Test
    public void getLatestWorkPerTask() {
        ReferralViewModel rvm = this.createReferral(someTimeAgo);

        // PRE EXISTING 'NEEDS' WORK
        String from = someTimeAgo.toString("yyyy-MM-dd");
        String to = someTimeNear.toString("yyyy-MM-dd");
        int preExisting = this.runReportSupport(from, to).length;

        // save work under 'needs assessment'
        UUID work1 = this.createWork(rvm.serviceRecipientId, someTimeAgo, EvidenceTask.NEEDS_ASSESSMENT, "first");
        // save work under 'needs reduction'
        UUID work2 = this.createWork(rvm.serviceRecipientId, someTimeAgo.plusDays(1), EvidenceTask.NEEDS_REDUCTION, "first");
        // save work under 'needs reduction', a newer version
        UUID work3 = this.createWork(rvm.serviceRecipientId, someTimeAgo.plusDays(2), EvidenceTask.NEEDS_REDUCTION, "second");

        // check work on referral - see SupportEvidenceController#findByServiceRecipientIdRequest, order by workDate desc, created desc
        List<EvidenceSupportWorkViewModel> supportWorkRefs =
                supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(rvm.serviceRecipientId, NEEDS).getBody().getContent();
        assertThat("3 expected", supportWorkRefs.size(), is(3));
        assertThat("first work expected", supportWorkRefs.get(supportWorkRefs.size()-1).workDate, is(someTimeAgo));

        Set<UUID> workIdsExpected = new HashSet<>();
        workIdsExpected.add(work1);
        workIdsExpected.add(work3);

        // see ReportController#supportWorkByCriteria
        EvidenceSupportWorkViewModel[] results = this.runReportSupport(from, to);
        assertThat(results.length - preExisting, is(workIdsExpected.size()));
        List<UUID> referralIdsArr = Arrays.stream(results).map(r -> r.id).collect(Collectors.toList());
        org.junit.Assert.assertTrue(referralIdsArr.containsAll(workIdsExpected));
    }

    private ReferralViewModel createReferral(LocalDateTime received) {
        // referral saved with receivedDate assuming its UTC (as per current referral form save operation)
        long clientId = clientActor.createClient("ReportEvidenceAPITest", received.toString("yyyy-mm-dd"));
        ReferralViewModel rvm = new ReferralViewModel();
        rvm.setClientId(clientId);
        rvm.setReceivedDate(received.toLocalDate());
        rvm.importServiceName = ServiceOptions.DEMO_ALL.getServiceName();
        long referralId = referralActor.createReferralFromCommand(rvm);

        rvm = referralActor.getReferralById(referralId).getBody();
        return rvm;
    }

    private UUID createWork(int serviceRecipientId, LocalDateTime workDate, EvidenceTask task, String comment) {
        // create work
        UUID workUuid = UUID.randomUUID();
        CommentCommandViewModel ccvm = new CommentCommandViewModel(workUuid, serviceRecipientId,
                NEEDS, task);
        ccvm.workDate = ChangeViewModel.changeNullTo(workDate);
        ccvm.comment = ChangeViewModel.changeNullTo(comment);
        commandActor.executeCommand(ccvm);
        return workUuid;
    }

    private EvidenceSupportWorkViewModel[] runReportSupport(String from, String to) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setFrom(from);
        dto.setTo(to);
        dto.setReferralStatus(null);
        dto.setSupportEvidenceGroup(NEEDS.getName());
        ResponseEntity<EvidenceSupportWorkViewModel[]> responseReport = reportActor.getReportLatestSupportWork(dto);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertNotNull(responseReport.getBody());
        return responseReport.getBody();
    }

}
