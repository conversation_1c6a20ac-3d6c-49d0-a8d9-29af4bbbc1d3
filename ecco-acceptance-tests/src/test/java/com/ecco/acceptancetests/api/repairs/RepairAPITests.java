package com.ecco.acceptancetests.api.repairs;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.test.support.UniqueDataService;
import org.junit.Test;

public class RepairAPITests extends BaseJsonTest {

    private final UniqueDataService unique = UniqueDataService.instance;

    @Test
    public void configSetupOnly() {
        /*
        REPAIRS
        --------
        list - permission ‘repairs’
        /<instance>/p/r/repair?buildingId=3000
        category list name - repair-category
        priority list name - repair-priority
        config -600: referralView / referralDetails / decideFinal / close / endFlow
        referralDetails formDefinitions - 1,2,3 for key(first step),form(main step),full(both for view in file)
        */
    }

}
