package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.infrastructure.time.Clock;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import kotlin.Pair;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Care run shifts is currently not active.
 * This started life as RotaCareRunAPITests, so possibly check there for further work.
 */
public class RotaCareRunShiftAPITests extends BaseJsonTest {

    private final Clock clock = Clock.DEFAULT;
    protected final org.joda.time.DateTime now = clock.now();
    protected final java.time.ZonedDateTime nowJdk = clock.nowJdk();

    @Test
    @Disabled("disabled the handler for this test")
    public void careRunWithShiftsEndToEndTest() {
        loginAsSysadmin();


        // *************
        // CREATE DEMAND

        // CREATE BUILDING DEMAND - these are shifts, for runs to be allocated to
        String aptType = unique.nameFor("typeBld1");
        final FixedContainerViewModel buildingShift = rotaSteps.createBuilding( aptType);
        rotaSteps.createBuildingAgreement(buildingShift);
        rotaSteps.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(buildingShift, LocalDateTime.now());

        // CREATE BUILDING-RUN *DEMAND* - these are like shifts, for workers to be allocated to
        String aptTypeRun = unique.nameFor("typeBldRun1");
        final FixedContainerViewModel buildingRun = rotaSteps.createBuilding( aptTypeRun);
        rotaSteps.createBuildingAgreement(buildingRun);
        rotaSteps.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(buildingRun, LocalDateTime.now());

        // CREATE REFERRAL DEMAND - these are individual referral appointments, to be allocated to runs
        final Pair<Long, String> referralPair = rotaSteps.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement("aptType1", LocalDate.now().atTime(22, 0), null);


        // *************
        // CREATE RESOURCE

        // CREATE WORKER RESOURCE - these are workers, to be allocated to runs (they have their primaryLocation set to appear on the run rota)
        String userName = unique.userNameFor("rcs-user1");
        String password = unique.passwordFor(userName);
        userActor.createIndividualWithUser(userName, password, unique.firstNameFor(userName), unique.lastNameFor(userName), null);
        final String workerName = rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, buildingRun.buildingId).getName();

        // CREATE BUILDING-RUN *AS RESOURCE* - these are runs, to be allocated to shifts
        // TODO consider the ResourcesRotaHandler approach (resources:all are assumed to be always available, so perhaps doesn't fit as well?)
        rotaSteps.createBuildingAvailabilityFromToday(buildingRun.buildingId);


        // *************
        // ASSIGN RESOURCES TO DEMAND

        // ASSIGN WORKER RESOURCE TO CARE RUN DEMAND to represent the worker being put on a run
        rotaSteps.checkCanAssignResourceToFirstAppointmentToday("workers:all", "buildings:"+buildingRun.buildingId, ""+buildingRun.name, workerName);

        // TODO workers could be assigned to care run demand only, and the individual appointment to the care run - but that isn't tested here
        // TODO here we assume that assigning the worker to a run propagates the worker on to the appointment directly
        // ASSIGN WORKER RESOURCE TO REFERRAL DEMAND to represent the worker being put on an individual's appointment
        rotaSteps.checkCanAssignResourceToFirstAppointmentToday("workers:all", "referrals:all", referralPair.getSecond(), workerName);

        // ALLOCATE CARE RUN *AS RESOURCE* TO SHIFT DEMAND - to represent the run being put on a shift
        // TODO implement more than one shift
        // TODO consider the building rota which includes the clients who belong to the building
        rotaSteps.checkCanAssignResourceToFirstAppointmentToday("careruns:"+buildingRun.buildingId, "buildings:"+buildingShift.buildingId, buildingShift.name, buildingRun.name);

        logout();
    }

}
