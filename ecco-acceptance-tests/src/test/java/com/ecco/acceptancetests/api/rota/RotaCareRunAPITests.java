package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.webApi.calendar.ServiceRecipientRotaDecorator;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.infrastructure.time.Clock;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralSummaryViewModel;
import com.ecco.webApi.rota.AppointmentActionCommandDto;
import com.ecco.webApi.rota.AppointmentRecurringActionCommandDto;
import com.ecco.webApi.rota.ServiceRecipientAppointmentScheduleCommandDto;
import com.ecco.webApi.taskFlow.ReferralTaskClientResidenceCommandDto;
import com.ecco.webApi.viewModels.Result;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Test;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.stream.Collectors;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static java.util.Collections.singletonList;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;

/**
 * Rota endToEnd test for appointments to workers. Also see RotaReferralAPITests.
 * <ol>
 *     <li>create care run appointments (= demand), to be allocated to workers availability (= resource) on the live rota</li>
 *     <li>create referral appointments (= demand), to be allocated to care runs availability (= resource) on the run builder</li>
 *     <li>create workers availability (= resource)</li>
 *     <li>create care run availability (= resource), for the referral appointments (= demand) on the run builder</li>
 *     <li>allocate care run appointments (= demand) to workers (= resource) on the live rota</li>
 *     <li>allocate workers (= resource) to a referral appointments (= demand) - NB this need to be happen, but we have to manually currently</li>
 * </ol>
 */
@SuppressWarnings({"ConstantConditions", "deprecation"})
public class RotaCareRunAPITests extends BaseJsonTest {

    private final Clock clock = Clock.DEFAULT;
    protected final org.joda.time.DateTime now = clock.now();
    protected final java.time.ZonedDateTime nowJdk = clock.nowJdk();

    private final LocalDateTime startMon2pm;
    private final LocalDateTime startTues4pm;
    private final DateTime mon2pm, tues4pm;
    {
        startMon2pm = nowJdk.toLocalDateTime().with(TemporalAdjusters.next(DayOfWeek.MONDAY)).withHour(14).withMinute(0).withSecond(0).withNano(0);
        startTues4pm = startMon2pm.plusDays(1).plusHours(2);
        mon2pm = JodaToJDKAdapters.localDateTimeToJoda(startMon2pm).toDateTime();
        tues4pm = JodaToJDKAdapters.localDateTimeToJoda(startTues4pm).toDateTime();
    }

    @Test
    public void careRunEndToEndTest() {
        loginAsSysadmin();


        // *************
        // GIVEN DEMAND

        // CREATE BUILDING-RUN *DEMAND* - these are care runs appointments, for workers to be allocated to
        var aptTypeRun = unique.nameFor("typeBldRun1");
        final var building = rotaSteps.createBuilding(unique.nameFor("typeParentBld"));
        // care runs need to be a 'carerun' resource and have a parent - see getWherePredicateForDemandSchedulesOfCareRunsInBuilding
        final var buildingRun = rotaSteps.createCareRun(aptTypeRun, building);

        var aptTime = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0).plusHours(5);

        rotaSteps.createBuildingAgreement(buildingRun);
        // the client-side creates the availability like this, although not ad-hoc
        rotaSteps.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(buildingRun, aptTime);

        // CREATE REFERRAL DEMAND - these are individual referral appointments, to be allocated to care run availability resource
        final var referralId_fullName = rotaSteps.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement("aptType1", aptTime, null);
        var rvm = referralActor.getReferralById(referralId_fullName.component1()).getBody();

        // referrals need to be on a building - see getWherePredicateForDemandSchedulesOfClientsInBuildingAndBuilding
        var residence = new ReferralTaskClientResidenceCommandDto(rvm.serviceRecipientId, "residence");
        residence.residence = ChangeViewModel.changeNullTo(building.buildingId);
        residence.startDate = ChangeViewModel.changeNullTo(now.toLocalDate().minusDays(5));
        commandActor.executeCommand(residence);

        // *************
        // GIVEN RESOURCE

        // CREATE WORKER RESOURCE - these are workers, to be allocated to runs (they have their primaryLocation set to appear on the run rota)
        var workerUserName = unique.userNameFor("rc-user1");
        var password = unique.passwordFor(workerUserName);
        var workerIndividual = userActor.createIndividualWithUser(workerUserName, password, unique.firstNameFor(workerUserName), unique.lastNameFor(workerUserName), null);
        final var workerName = rotaSteps.createWorkerAndJobWithAvailabilityForToday(workerUserName, building.buildingId).getName();

        // CREATE BUILDING-RUN *AS RESOURCE* - these are care runs, for referral appointments to be allocated to
        // TODO consider the ResourcesRotaHandler approach (resources:all are assumed to be always available, so perhaps doesn't fit as well?)
        // NB the client side doesn't use availability for a building, it shows the schedule as if availability - see BuildingCareRunRotaHandler
        //rotaSteps.createBuildingAvailabilityForToday(buildingRun.buildingId);


        // *************
        // THEN CAN ASSIGN RESOURCES TO DEMAND

        // [on run builder] ASSIGN CARE RUN RESOURCE TO REFERRAL DEMAND to represent the referral appointment being put on a care run availability
        rotaSteps.checkCanAssignResourceToSingleAppointmentAtTime("careruns:all", "buildings:" + building.buildingId,
                referralId_fullName.component2(), buildingRun.name, aptTime);

        // [on live rota] ASSIGN WORKER RESOURCE TO CARE RUN DEMAND to represent the worker being put on a care run
        rotaSteps.checkCanAssignResourceToSingleAppointmentAtTime("workers:all", "buildings:" + building.buildingId,
                buildingRun.name, workerName, aptTime);

        // *************
        // VERIFY WORKER'S CALENDAR

        // NB this approach means the worker isn't assigned directly to the run appointments (apts/runs are independent allocations to workers)
        // if needed directly, we could test assigning using the existing API from the ad-hoc apt created at 10pm today to the worker available
        // which would assign the worker resource to the referral demand to represent the worker being put on an individual's appointment
        // however, the referral already has a status of CONFIRMED (although to the carerun, not the worker) which means RotaHandlerSupport.addDemandRecurrenceIfAppropriate deems not a demand
        //rotaSteps.checkCanAssignResourceToFirstAppointment("workers:all", "referrals:all", referralId_fullName.getSecond(), workerName);

        // check the worker can see the appointment from their calendar, including the care run shift breakdown
        var workerEventsInRange = calendarActor.getEntriesByTime(
                JodaToJDKAdapters.localDateToJoda(aptTime.toLocalDate()),
                JodaToJDKAdapters.localDateToJoda(aptTime.toLocalDate()).plusDays(1),
                null,
                singletonList(workerIndividual.calendarId)
        ).getBody();
        assertThat("event should be retrieved", workerEventsInRange.length, is(1));
        assertEquals("OWNER - run is owner", workerEventsInRange[0].getOwnerCalendarId(), buildingRun.calendarId);
        // and with the run owner event, there will be a shift breakdown
        assertTrue("event should have 'shift breakdown' link", workerEventsInRange[0].hasLink(ServiceRecipientRotaDecorator.REL_RUN_BREAKDOWN));

        // NB the worker doesn't actually own any events - they are put onto the client or run
        // the worker is put onto the run event as an attendee, so we ask for the workers events and get the run event
        // the run event has links which include a 'shift breakdown' link which can get the events inside the run
        var workerBreakdownLink = workerEventsInRange[0].getLink(ServiceRecipientRotaDecorator.REL_RUN_BREAKDOWN).orElseThrow();
        var eventsInBreakdown = calendarActor.getEntriesByLink(workerBreakdownLink);
        // the breakdown are carerun events, which will be the availability (owned by the run) and where the run is put onto clients
        // so we have 2 events, the availability event owned by the run, and the client which owns that event
        assertThat("breakdown events should be retrieved", eventsInBreakdown.getBody().length, is(2));
        var runAvailabilityInBreakdown = Arrays.stream(eventsInBreakdown.getBody()).filter(e -> e.getOwnerCalendarId().equals(buildingRun.calendarId)).collect(Collectors.toList());
        var clientsInBreakdown = Arrays.stream(eventsInBreakdown.getBody()).filter(e -> e.hasLink(ServiceRecipientRotaDecorator.REL_ROTAVISIT)).collect(Collectors.toList());

        assertThat("breakdown event should include client", clientsInBreakdown.size(), is(1));

        assertThat("OWNER - run is owner of availability event", runAvailabilityInBreakdown.size(), is(1));
        assertFalse("OWNER - client is owner of client event(s)", clientsInBreakdown.stream().anyMatch(e -> e.getOwnerCalendarId().equals(buildingRun.calendarId)));

        var clientAttendee = clientsInBreakdown.get(0).getAttendees().stream().filter(a -> a.name.equals(rvm.clientDisplayName)).collect(Collectors.toList());
        assertEquals("breakdown event client name should match", clientAttendee.get(0).name, rvm.clientDisplayName);

        logout();
    }

    // create referral appointments (= demand), to be allocated to care runs availability (= resource) on the run builder
    @Test
    public void careRunAllocateFutureSchedulesTest() {
        loginAsSysadmin();

        // *************
        // GIVEN Demand for visits to a referral client `svm` residing at `building`

        var svm = createDefaultReferral("Repeat", "Allocation");
        var building = rotaSteps.createBuilding(String.format("typeParentBld-%s", svm.firstName));
        createDefaultDemand(svm, building);

        // *************
        // GIVEN RESOURCE of a care run `careRun` available all day today

        final FixedContainerViewModel careRun;
        {
            // *************
            // GIVEN RESOURCE of a care run `careRun` available all day today
            careRun = rotaSteps.createCareRun(unique.nameFor("typeBldRun2"), building);
            createDefaultCareRun(svm, building, careRun);
        }

        // *************
        // WHEN assign `svm` appointment (single) to care run `careRun`
        // WED week

        // NB normal single allocations take the appointment recurrence from the parent's TENTATIVE status and ensures its concrete and sets CONFIRMED
        // then it puts the carerun's calendarId as an attendee and status ACCEPTED
        // [on run builder] ASSIGN CARE RUN RESOURCE TO REFERRAL DEMAND - SINGLE for next Wed on the schedule (to represent the referral appointment being put on a care run)
        allocateDefaultAppointment(svm, building, careRun);

        // *************
        // AND WHEN `svm` appointment (recurring) to care run `careRun`
        // MON for all schedule (since not specified specific days), therefore includes MON,WED,FRI

        // NB recurring allocations apply single allocations a number of times

        final Rota rotaMon2pmCarerun = getCareRunsForBuilding(building, mon2pm);
        {
            // [on run builder] ASSIGN CARE RUN RESOURCE TO REFERRAL DEMAND - REPEATING for the whole schedule (the cmd also caters for a part-schedule if needed)
            // this sets the carerun as an attendee on the referral schedule recurringEntry (as sort of meta-data - which does nothing, except attendees also appear on recurrences)
            var apt = rotaMon2pmCarerun.findDemandByServiceRecipientName(svm.clientDisplayName).get(0);
            var partSchedule = new ServiceRecipientAppointmentScheduleCommandDto("partSchedule", apt.getServiceRecipientId());
            partSchedule.applicableFromDate = startMon2pm.toLocalDate();
            partSchedule.endDate = ChangeViewModel.changeNullTo(startMon2pm.toLocalDate().plusDays(7)); // So that we don't get the rest happening async before the next allocate
            var allocateToSchedule = new AppointmentRecurringActionCommandDto(AppointmentRecurringActionCommandDto.OPERATION_ALLOCATE,
                    apt.getRef(), apt.getServiceRecipientId(), "careruns:all", "buildings:" + building.buildingId,
                    partSchedule);
            allocateToSchedule.allocateResourceId = careRun.buildingId;
            var result = commandActor.executeCommand(allocateToSchedule).getBody();
            assertThat(result.getMessage(), containsString("appointments allocated up to"));
            assertNotNull("command not found", serviceRecipientActor.findAllocateRecurringCommand(allocateToSchedule.uuid.toString()).getBody());
        }

        // *************
        // AND WHEN `svm` appointment (recurring) to care run `careRun`
        // TUES for PART schedule (days TUES, not THURS)

        // NB recurring allocations apply single allocations a number of times

        final Rota rotaTues4pmCarerun = getCareRunsForBuilding(building, tues4pm);
        {
            // [on run builder] ASSIGN CARE RUN RESOURCE TO REFERRAL DEMAND - REPEATING for the whole schedule (the cmd also caters for a part-schedule if needed)
            // this sets the carerun as an attendee on the referral schedule recurringEntry (as sort of meta-data - which does nothing, except attendees also appear on recurrences)
            var apt = rotaTues4pmCarerun.findDemandByServiceRecipientName(svm.clientDisplayName).get(0);
            var partSchedule = new ServiceRecipientAppointmentScheduleCommandDto("partSchedule", apt.getServiceRecipientId());
            partSchedule.applicableFromDate = startTues4pm.toLocalDate();
            partSchedule.withISODayOfWeek(DayOfWeek.from(startTues4pm));
            partSchedule.endDate = ChangeViewModel.changeNullTo(startTues4pm.toLocalDate().plusDays(14));
            var allocateToSchedule = new AppointmentRecurringActionCommandDto(AppointmentRecurringActionCommandDto.OPERATION_ALLOCATE,
                    apt.getRef(), apt.getServiceRecipientId(), "careruns:all", "buildings:" + building.buildingId, partSchedule);
            allocateToSchedule.allocateResourceId = careRun.buildingId;
            var result = commandActor.executeCommand(allocateToSchedule).getBody();
            assertThat(result.getMessage(), containsString("appointments allocated"));

            assertNotNull("command not found", serviceRecipientActor.findAllocateRecurringCommand(allocateToSchedule.uuid.toString()).getBody());
        }

        // *************
        // THEN the rota on MON has no unallocated appointments for svcRec on any rota (for MON, WED, FRI part alloc)
        // AND there is an allocated recurrence to `careRun` on the care runs rota
        checkRotasHaveApptAssigned(mon2pm, svm, building, careRun);

        // *************
        // AND THEN the rota on TUES has no unallocated appointments for svcRec on any rota (for TUES-only part alloc)
        // AND there is an allocated recurrence to `careRun` on the care runs rota
        checkRotasHaveApptAssigned(tues4pm, svm, building, careRun);

        // *************
        // AND THEN the rota on THURS contains an unassigned appointment for svm (we didn't alloc THURS)
        var thurs4pm = tues4pm.plusDays(2);
        checkRotasHaveApptUnassigned(thurs4pm, svm, building);

        // AND
        // WHEN I deallocate from a given date
        // I can close off on previous date (last day of service)
        {
            var rotaThursCarerun = getCareRunsForBuilding(building, tues4pm);

/*
        RotaResourceViewModel worker = rota.findResourceByName(careRun.name);
        assertNotNull("Expected worker with name '" + careRun.name, worker);

        List<RotaAppointmentViewModel> appointments = worker.findAppointmentsByServiceRecipientName(svm.displayName);
        assertThat("Expect appointment returned", appointments, iterableWithSize(1));

        var deallocateCommand = new AppointmentRecurringActionCommandDto(AppointmentRecurringActionCommandDto.OPERATION_DEALLOCATE,
                apt.getRef(), apt.getServiceRecipientId(), "careruns:all", "buildings:" + building.buildingId, partSchedule);
        deallocateCommand.deallocateResourceId = careRun.buildingId;
        commandActor.executeCommand(deallocateCommand);
        assertNotNull("command not found", serviceRecipientActor.findAllocateRecurringCommand(deallocateCommand.uuid.toString()).getBody());
*/
        }

        logout();
    }

    private void allocateDefaultAppointment(ReferralSummaryViewModel svm, FixedContainerViewModel building, FixedContainerViewModel careRun) {
        var weekWed = mon2pm.plusWeeks(1).plusDays(2);
        var rotaWeekWed1 = getCareRunsForBuilding(building, weekWed);
        var aptWeekWed = rotaWeekWed1.findDemandByServiceRecipientName(svm.clientDisplayName).get(0);
        var allocateToCareRun = new AppointmentActionCommandDto(AppointmentActionCommandDto.OPERATION_ALLOCATE,
                aptWeekWed.getRef(), aptWeekWed.getServiceRecipientId(), "careruns:all", "buildings:" + building.buildingId);
        allocateToCareRun.allocateResourceId = careRun.buildingId;
        commandActor.executeCommand(allocateToCareRun);
        assertNotNull("command not found", serviceRecipientActor.findAllocateRecurringCommand(allocateToCareRun.uuid.toString()).getBody());

        // CHECK [on run builder] a referral appointment is for Monday
        var rotaWeekWed2 = getCareRunsForBuilding(building, weekWed);
        assertRotaHasOneApptAssignedToRun(rotaWeekWed2, careRun, svm);
    }

    private void createDefaultCareRun(ReferralSummaryViewModel svm, FixedContainerViewModel building, FixedContainerViewModel careRun) {
        // GIVEN care run with availability - care runs need to be a 'carerun' resource type and have a parent - see getWherePredicateForDemandSchedulesOfCareRunsInBuilding
        rotaSteps.createBuildingAvailabilityFromToday(careRun.buildingId);

        // CHECK [on run builder] a referral appointment is for Monday
        var rotaMon2pmCarerun = getCareRunsForBuilding(building, mon2pm);
        assertRotaHasOneUnassignedFor(rotaMon2pmCarerun, svm);

        // CHECK [on run builder] a referral appointment is for Tues
        var rotaTues4pmCarerun = getCareRunsForBuilding(building, tues4pm);
        assertRotaHasOneUnassignedFor(rotaTues4pmCarerun, svm);
    }

    private FixedContainerViewModel createDefaultDemand(ReferralSummaryViewModel svm, FixedContainerViewModel building) {
        return createDefaultDemand(svm, building, startMon2pm.toLocalDate(), startMon2pm.toLocalTime(), startTues4pm.toLocalTime());
    }

    private FixedContainerViewModel createDefaultDemand(ReferralSummaryViewModel svm, FixedContainerViewModel building, java.time.LocalDate startMon, LocalTime monTime, LocalTime tuesTime) {
        var startMonTime = startMon.atTime(monTime);
        var startTuesTime = startMon.plusDays(1).atTime(tuesTime);

        // GIVEN referral agreement - today for 30 days
        agreementActor.createAppointmentType(DEMO_ALL, svm.firstName, 60);
        var today = now.toLocalDate();
        var result = agreementActor.createAgreement(svm.serviceRecipientId, today, today.plusDays(10), null);
        // which we extended, and then truncated
        var agreementId = extractAgreementId(result);
        agreementActor.editAgreementEnd(svm.serviceRecipientId, agreementId,
                ChangeViewModel.create(today.plusDays(10), today.plusDays(40)));
        agreementActor.editAgreementEnd(svm.serviceRecipientId, agreementId,
                ChangeViewModel.create(today.plusDays(40), today.plusDays(30)));

        // THEN end date should be plus 30 days
        var agreement = agreementActor.getFirstAgreement(svm.serviceRecipientId, now);
        assertThat(agreement.getEnd(), equalTo(today.plusDays(30)));

        // GIVEN referral appointment schedule - on Mon,Wed,Fri in 2 hours time every week (intervalFrequency default 1) for 2 months from next Monday
        var days = DaysOfWeek.fromCalendarDaysISO(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY);
        rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId, startMonTime, days, clock.nowJdk().toLocalDate().plusMonths(2), 0, null);

        // GIVEN referral appointment schedule - on Tues,Thurs in 4 hours time every week (intervalFrequency default 1) for 2 months from next Monday
        var daysOther = DaysOfWeek.fromCalendarDaysISO(DayOfWeek.TUESDAY, DayOfWeek.THURSDAY);
        rotaSteps.checkCanCreateScheduleOnFirstAgreement(svm.clientDisplayName, svm.serviceRecipientId, startTuesTime, daysOther, clock.nowJdk().toLocalDate().plusMonths(2), 0, null);

        // CHECK [normal rota] a referral appointment is for Monday
        var rotaMon2pmStaff = getStaffRotaForWholeOrg(JodaToJDKAdapters.localDateTimeToJoda(startMonTime).toDateTime());
        assertThat("Expect Mon appointment - demand shown on client/worker rota", rotaMon2pmStaff.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));

        // CHECK [normal rota] a referral appointment is for Tuesday
        var rotaTues4pmStaff = getStaffRotaForWholeOrg(JodaToJDKAdapters.localDateTimeToJoda(startTuesTime).toDateTime());
        assertThat("Expect Tues appointment - demand shows on client/worker rota", rotaTues4pmStaff.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));

        // referrals need to be on a building - see getWherePredicateForDemandSchedulesOfClientsInBuildingAndBuilding
        var residence = new ReferralTaskClientResidenceCommandDto(svm.serviceRecipientId, "residence");
        residence.residence = ChangeViewModel.changeNullTo(building.buildingId);
        residence.startDate = ChangeViewModel.changeNullTo(today.minusDays(5));
        commandActor.executeCommand(residence);

        return building;
    }

    public static long extractAgreementId(Result result) {
        var parts = result.getLink("edit").getHref().split("/");
        return Long.parseLong(parts[parts.length - 2]);
    }

    private ReferralSummaryViewModel createDefaultReferral(String firstName, String lastName) {
        var firstNameUnique = unique.clientFirstNameFor(firstName);
        var lastNameUnique = unique.clientLastNameFor(lastName);

        // GIVEN referral
        var rvm = referralActor.createMinimalReferralAndClient(null, firstNameUnique, lastNameUnique, DEMO_ALL);
        return referralActor.getReferralSummaryById(rvm.referralId).getBody();
    }

    private void checkRotasHaveApptUnassigned(DateTime instantWithinRotaDay, ReferralSummaryViewModel svm, FixedContainerViewModel building) {
        var careRunsRota = getCareRunsForBuilding(building, instantWithinRotaDay);
        assertRotaHasOneUnassignedFor(careRunsRota, svm);

        var staffBldgRota = getStaffRotaForBuilding(building, instantWithinRotaDay);
        assertRotaHasOneUnassignedFor(staffBldgRota, svm);

        var wholeOrgRota = getStaffRotaForWholeOrg(instantWithinRotaDay);
        assertRotaHasOneUnassignedFor(wholeOrgRota, svm);
    }

    private void checkRotasHaveApptAssigned(DateTime instantWithinRotaDay, ReferralSummaryViewModel svm, FixedContainerViewModel building, FixedContainerViewModel careRun) {
        var careRunsRota = getCareRunsForBuilding(building, instantWithinRotaDay);
        assertRotaHasNoUnassignedApptsFor(careRunsRota, svm);
        assertRotaHasOneApptAssignedToRun(careRunsRota, careRun, svm);

        var staffBldgRota = getStaffRotaForBuilding(building, instantWithinRotaDay);
        assertRotaHasNoUnassignedApptsFor(staffBldgRota, svm);

        var wholeOrgRota = getStaffRotaForWholeOrg(instantWithinRotaDay);
        assertRotaHasNoUnassignedApptsFor(wholeOrgRota, svm);
    }

    private void assertRotaHasOneApptAssignedToRun(Rota rota, FixedContainerViewModel run, ReferralSummaryViewModel svm) {
        assertThat("appointment IS allocated (in resource) on run builder",
                rota.findResourceByName(run.name).findAppointmentsByServiceRecipientName(svm.clientDisplayName),
                iterableWithSize(1));
    }

    private void assertRotaHasOneUnassignedFor(Rota rota, ReferralSummaryViewModel svm) {
        assertThat("rota has one unallocated appointment for service recipient (is in demand)",
                rota.findDemandByServiceRecipientName(svm.clientDisplayName), iterableWithSize(1));
    }

    private void assertRotaHasNoUnassignedApptsFor(Rota rota, ReferralSummaryViewModel svm) {
        assertThat("rota does NOT contain appointments for service recipient (it's now 'in' the care run)",
                rota.findDemandByServiceRecipientName(svm.clientDisplayName), emptyIterable());
    }

    private Rota getStaffRotaForWholeOrg(DateTime instantWithinDay) {
        return rotaActor.getWholeOrgRotaOnDate(instantWithinDay);
    }

    private Rota getStaffRotaForBuilding(FixedContainerViewModel building, DateTime instantWithinDay) {
        return rotaActor.getRotaOnDate("workers:all", "buildings:" + building.buildingId, instantWithinDay);
    }

    private Rota getCareRunsForBuilding(FixedContainerViewModel building, DateTime instantWithinDay) {
        return rotaActor.getRotaOnDate("careruns:all", "buildings:" + building.buildingId, instantWithinDay);
    }


}
