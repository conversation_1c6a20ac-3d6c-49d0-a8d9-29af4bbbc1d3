package com.ecco.acceptancetests.ui.pages.supportplan;

import com.ecco.acceptancetests.ui.pages.BasePageObject;
import org.openqa.selenium.WebDriver;

import java.util.Date;

import static org.hamcrest.Matchers.containsString;

public class EvidenceBasePage extends BasePageObject {
    private static final String BUTTON_TEXT_SAVE = "save";
    private static final String commentFieldName = "comment";

    public EvidenceBasePage(String pageURL, WebDriver webDriver) {
        super(pageURL, webDriver);
    }

    public void setComment(String comment) {
        setFieldSoonByName(commentFieldName, comment);
    }

    public void setWorkDate(Date date) {
        setFocusById("workDate");
        setDateByElementIdIso("workDate", date);
        hideDateAndTimePicker(); // because focus will pop up the date picker
    }

    public boolean isReviewInProgress() {
        return this instanceof ReviewPage;
    }

    public void save() {
        clickButtonByText(BUTTON_TEXT_SAVE);
        if (isReviewInProgress()) {
            return;
        }
        try {
            waitForSavedAlert(BasePageObject.DEFAULT_SAVE_TIMEOUT);
        } catch (AssertionError e) {
            // HACKY sometimes we don't have a saved alert and instead return straight to the task list
            verifyCurrentPage(containsString("/secure/referralFlow.html"));
        }
    }
}
