package com.ecco.acceptancetests.ui.performance;

import com.ecco.acceptancetests.ui.deployment.DeploymentValidationTests;

public class StandardUserSession implements Runnable {

    @Override
    public void run() {

        DeploymentValidationTests tests = new DeploymentValidationTests();
        tests.beforeTest();
        try {
            tests.adminUserCanSeeHomeLoginAndCreateNewUser();
        } catch (Exception e) {

            throw new RuntimeException(e);
        }


    }
}
