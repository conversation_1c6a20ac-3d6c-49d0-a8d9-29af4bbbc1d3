package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.expectations.OfflineExpectation;


import com.ecco.acceptancetests.givens.OfflineGiven;
import com.ecco.acceptancetests.steps.OfflineContext;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.acceptancetests.ui.pages.WelcomePage;
import com.ecco.acceptancetests.ui.pages.offline.ManageOfflinePage;
import com.ecco.acceptancetests.ui.pages.offline.ReferralsListPage;

import java.util.List;

import org.jspecify.annotations.NonNull;
import org.openqa.selenium.WebDriver;


public class OfflineStepsWebDriver extends WebDriverUI implements OfflineContext {

    private WelcomePage welcomePage;

    private boolean isOnline = false;


    public OfflineStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
        this.welcomePage = new WelcomePage(webDriver);
    }

    private OfflineStepsWebDriver(WebDriver webDriver, boolean isOnline) {
        this(webDriver);
        this.isOnline = isOnline;
    }

    @NonNull
    @Override
    public OfflineStepsWebDriver forOnline() {
        return new OfflineStepsWebDriver(webDriver, true);
    }


    @NonNull
    @Override
    public OfflineGiven given(@NonNull BaseSeleniumTest testFramework) {
        return new OfflineGiven(this, testFramework);
    }

    @NonNull
    @Override
    public OfflineExpectation expect() {
        return new OfflineExpectation(this);
    }


    @NonNull
    @Override
    public OfflineContext loginOffline() {
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        ManageOfflinePage offlinePage = welcomePage.gotoOffline();
        offlinePage.clickFinish();
        // TODO:  offlinePage.clickLogin();
        UIFactory.getInstance().getLoginUI().loginAsSysadmin();
        return this;
    }


    @NonNull
    @Override
    public OfflineContext syncReferrals() {
        welcomePage.menu();
        ManageOfflinePage offlinePage = welcomePage.gotoOffline();
        offlinePage.clickSyncButton();
        return this;
    }


    @NonNull
    @Override
    public List<String> viewReferrals() {
        welcomePage.navigateAsEntryPoint();
        welcomePage.menu();

        ReferralsListPage referralsPage;
        if (!isOnline) {
            ManageOfflinePage offlinePage = welcomePage.gotoOffline();
            referralsPage = offlinePage.gotoReferrals();
        }
        else {
            referralsPage = welcomePage.gotoMyReferrals();
        }
        List<String> clientNames = referralsPage.getClientNames();
        return clientNames;
    }
}
