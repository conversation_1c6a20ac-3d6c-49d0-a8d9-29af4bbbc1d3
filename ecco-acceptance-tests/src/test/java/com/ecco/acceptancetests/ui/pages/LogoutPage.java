package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class LogoutPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/logoutSuccess";

    public LogoutPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public void logout() {
        getPageHeader().logout();
        waitForPageLoaded();
        verifyIsCurrentPage();
    }

    @Override
    public EccoBasePage defaultAction() {
        logout();
        return this;
    }

}
