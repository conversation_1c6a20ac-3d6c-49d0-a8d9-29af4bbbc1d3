package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.TestUtils;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.ServiceOptions;
import com.ecco.data.client.model.ReferralAggregate;
import com.ecco.rota.webApi.dto.RecurringDemandScheduleDto;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.evidence.ReferralViewModel;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.IfProfileValue;

import java.util.List;

import static com.ecco.data.client.ServiceOptions.ACCOMMODATION;
import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@SuppressWarnings("deprecation")
public class ReferralTests extends BaseSeleniumTest {

    // WIP: This will probably be injected per thread by the factory
    private final UniqueDataService unique = UniqueDataService.instance;

    private static final String TEST_NI_NUMBER = "*********";
    private static final DateTimeFormatter ISODATEFORMAT = ISODateTimeFormat.date();

    private static final Integer GENDER = 115;

    @Test
    public void canCreateLinearReferral() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        ReferralOptions options;

        // create new referral using split create/process methods
        options = new ReferralOptions().requiresAccommodation(true);
        LocalDate dob = LocalDate.parse("1980-01-14");
        ReferralViewModel rvm1 = referralSteps.createReferral(true, false, false, null, options, ACCOMMODATION,
                "FirstName", "LastName", dob, TEST_NI_NUMBER, "female", "English", "Missing");
        String referralName1 = rvm1.getClientFirstName() + " " + rvm1.getClientLastName();
        referralSteps.processTaskDefinitions(options, ACCOMMODATION);

        // create new referral using 'ensure' so picks an existing one up if there
        ReferralViewModel rvm = ensureReferralView("lookupThisCode");
        assertEquals("referral code", "lookupThisCode", rvm.getReferralCode());

        // create a new referral to floating support
        // actually - were testing accommodation and demo all for now...
        //options = new ReferralOptions();
        //String referralName2 = referralSteps.processReferral(options, Service.FLOATING_SUPPORT, "Bobby", "FLOATING",
        // null, "referral comment 2");

        //userUI.check().canSeeText("your action has been accepted");
        List<String> referrals = referralSteps.listReferrals();
        TestUtils.listContains(referrals, referralName1);

        // Check post-conditions on the referrals
        rvm1 = referralActor.getReferralById(rvm1.referralId).getBody();
        assertNotNull(rvm1);
        // Need to do this for user calendar. Interviews are not added to client calendar
        // assertTrue(rvm1.calendarEvents.stream().anyMatch( e -> e.title.contains("Interview")));

        // TODO: check user calendar - we can get calendarId from session data, but we actually want an actor for
        // the user's home page with calendar events, I suspect

        loginSteps.logout();
        logoutApi();
    }

    @Test
    public void canCreateActivitiReferral() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        ReferralOptions options = new ReferralOptions()
                .requiresProjects(false)
                .requiresDataProtection(true)
                .requiresEmergencyDetails(true)
                .requiresGeneralQuestionnaireOutcomeStar(true);
        String referralName = referralSteps.processReferral(options, DEMO_ALL, "FirstName", "ALL", "*********",
                "this is a new referral comment 1").getSecond();

        //userUI.check().canSeeText("your action has been accepted");
        List<String> referrals = referralSteps.listReferrals();
        TestUtils.listContains(referrals, referralName);

        // Check post-conditions on the referrals - to do this we'd have to split create and process as for Accomodation
//        ReferralViewModel rvm = referralActor.getReferralById(rvm.referralId).getBody();

        loginSteps.logout();
        logoutApi();
    }

    @Disabled("This needs migrating to newer report styles now that old reports have been hidden")
    @Test
    public void sysadminUserCanCreateNewReferralAndViewReports() {
        loginAsSysadminApi();
        loginSteps.loginAsSysadmin();

        LocalDate dte = new LocalDate(2014, 2, 13);
        ReferralViewModel referral = referralActor.createReferralAsStarted("Joe", "Blingo", dte, ACCOMMODATION);

        assertNotNull("TEST ERROR: creating a referral did not return a referral id", referral.referralId);

        logoutApi();

        loginSteps.navigateToWelcome(); // until ECCO-627 we get an error with just clicking back when choosing a service (its going back to a post)
        loginSteps.logout();
    }

    @Test
    @Disabled("no longer implemented - reinstate upon request")
    public void sysadminUserCanViewReferralOverviewPrint() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        LocalDate dte = new LocalDate(2014, 2, 13);
        ReferralViewModel r = referralActor.createReferralAsStarted("Joe", "Print", dte, ACCOMMODATION);
        referralSteps.printReferralOverview(r.clientFirstName, r.clientLastName);

        logoutApi();
    }

    @Test
    @IfProfileValue(name="test.category", values={"all","buggy-tests"}) // This fails on Bamboo in WebDriver XPath
    public void givenCalendarAccessedFromReferralWhenEventCreatedThenClientCanBeIncluded() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        LocalDate dte = new LocalDate(2014, 12, 1);
        ReferralViewModel r = referralActor.createReferralAsStarted("Alec", "Callender", dte, ACCOMMODATION);
        referralSteps.navigateToAppointments(r.clientFirstName, r.clientLastName);
        calendarSteps.addCalendarEventForTomorrowWithAttendees("September Song", r.getClientDisplayName());

        logoutApi();
    }

    @Test
    public void sysadminUserCanCreateNewReferralRenameAndFind() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        // create a referral or re-use it if its there
        ReferralViewModel rvm = ensureReferralView("searchTest");
        assertEquals("referral code", "searchTest", rvm.getReferralCode());

        // update the client's LAST name
        String newLastName = "Z"+rvm.getClientLastName();
        updateTextCommandActor.updateProperty("clients", rvm.getClientId(), "contact.lastName", newLastName,
                rvm.getClientLastName());

        // find the referral by LAST name
        referralSteps.findClientThenReferral(null, null, newLastName, rvm.getReferralCode());
        logoutApi();
    }

    /**
     * pre-create Sally Xavier, then create Sally Xav
     * it could be that 'Sally Xav' is shorthand for Sally Xavier
     * or it could be that Sally Xav could be the real name and so a new client
     * - this test is where the clients are the same
     */
    @Test
    @IfProfileValue(name="test.category", values={"all","possible bug"}) // Fails 'has existing client' on .createReferral
    public void createNewReferralsWithSameClient_nameSubset() {
        loginAsSysadminApi();

        String firstName= "Sally";
        String lastName = "Xavier";
        // uniqify first name only - in case we are repeating acceptance-tests on a local dev db
        firstName = firstName.concat("-" + System.currentTimeMillis());
        String lastNameSubset = "Xav";

        // create a PRE-EXISTING referral
        // web-api creation of referral
        ReferralAggregate ra = new ReferralAggregate();
        ra.client.setFirstName(firstName);
        ra.client.setLastName(lastName);
        ra.client.setGenderId(GENDER);
        // we need to use the default date from the 'createReferral' process, otherwise the dob are different
        LocalDate origDob = new LocalDate(1977, 5, 4); // original date assumed
        ra.client.birthDate = origDob;
        LocalDate dte = new LocalDate(2014, 2, 13);
        ra.referral.setImportServiceName(ACCOMMODATION.getServiceName());
        ra.referral.setDecisionDate(dte.toLocalDateTime(LocalTime.MIDNIGHT));
        ra.referral.setReceivedDate(dte);
        ra.referral.setReceivingServiceDate(dte);
        referralActor.submitReferralAggregateImport(ra);

        // PROCESS NEW REFERRAL
        loginSteps.loginAsSysadmin();
        ReferralOptions options = new ReferralOptions().requiresAccommodation(true);
        // test without unique so we use the same firstName lastName - so we can test with a different dob
        referralSteps.createReferral(false, true, true, firstName + " " + lastName, options, ACCOMMODATION,
                firstName, lastNameSubset, origDob, null, "female", null, null);

        logoutApi();
    }

    /**
     * pre-create Sally Jones, then create Sally Jo
     * it could be that 'Sally Jo' is shorthand for Sally Jones
     * or it could be that Sally Jo could be the real name and so a new client
     * - this test is where the clients are different
     */
    @Test
    public void createNewReferralsWithDifferentClient_nameSubset() {
        loginAsSysadminApi();

        String firstName= "Sally";
        String lastName = "Jones";
        // uniqify first name only - in case we are repeating acceptance-tests on a local dev db
        firstName = firstName.concat("-" + System.currentTimeMillis());
        String lastNameSubset = "Jo";

        // create a PRE-EXISTING referral
        // web-api creation of referral
        ReferralAggregate ra = new ReferralAggregate();
        ra.client.setFirstName(firstName);
        ra.client.setLastName(lastName);
        ra.client.setGenderId(GENDER);
        // we need to use the default date from the 'createReferral' process, otherwise the dob are different
        LocalDate origDob = new LocalDate(1977, 5, 4); // original date assumed
        ra.client.birthDate = origDob;
        LocalDate dte = new LocalDate(2014, 2, 13);
        ra.referral.setDecisionDate(dte.toLocalDateTime(LocalTime.MIDNIGHT));
        ra.referral.setImportServiceName(ACCOMMODATION.getServiceName());
        ra.referral.setDecisionDate(dte.toLocalDateTime(LocalTime.MIDNIGHT));
        ra.referral.setReceivedDate(dte);
        ra.referral.setReceivingServiceDate(dte);
        referralActor.submitReferralAggregateImport(ra);

        // PROCESS NEW REFERRAL
        loginSteps.loginAsSysadmin();
        ReferralOptions options = new ReferralOptions().requiresAccommodation(true);
        // test without unique so we use the same firstName lastName - so we can test with a different dob
        referralSteps.createReferral(false, true, false, firstName + " " + lastNameSubset, options, ACCOMMODATION,
                firstName, lastNameSubset, origDob, null, "female", null, null);

        logoutApi();
    }

    /**
     * pre-create Dup Licate and then create the same name but with a different dob - should be separate clients
     */
    @Test
    public void createNewReferralsWithDifferentClient_dob() {
        loginAsSysadminApi();

        // uniqify - in case we are repeating acceptance-tests on a local dev db
        String firstName= "Dup-" + System.currentTimeMillis();
        String lastName = "Licate-" + System.currentTimeMillis();

        // create a PRE-EXISTING referral
        // web-api creation of referral
        ReferralAggregate ra = new ReferralAggregate();
        ra.client.setFirstName(firstName);
        ra.client.setLastName(lastName);
        ra.client.setBirthDate(new LocalDate(1995, 11, 7));
        ra.client.setGenderId(GENDER);

        LocalDate dte = new LocalDate(2014, 2, 13);
        ra.referral.setImportServiceName(ACCOMMODATION.getServiceName());
        ra.referral.setDecisionDate(dte.toLocalDateTime(LocalTime.MIDNIGHT));
        ra.referral.setReceivedDate(dte);
        ra.referral.setReceivingServiceDate(dte);

        referralActor.submitReferralAggregateImport(ra);

        // PROCESS NEW REFERRAL - just a change of dob by a day
        loginSteps.loginAsSysadmin();
        ReferralOptions options = new ReferralOptions().requiresAccommodation(true);
        LocalDate dob = new LocalDate(1995, 11, 8);
        // test without unique so we use the same firstName lastName - so we can test with a different dob
        referralSteps.createReferral(false, true, false, null,
                options, ACCOMMODATION, firstName, lastName, dob, TEST_NI_NUMBER, "male", "English", "Missing");

        logoutApi();
    }

    @Test
    @IfProfileValue(name="test.category", values={"all","wip"}) // TODO: 10 April 2018 commit removed referralFundingDef.jsp and enhanceFundingForm.ts
    public void closingReferralTruncatesAppointmentSchedules() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        // Reference data
        final ServiceOptions service = DEMO_ALL;
        final LocalDate startDate = new LocalDate(2014, 8, 22);

        // Create a new referral
        ReferralAggregate ra = new ReferralAggregate();
        ra.client.setLastName("Gascoigne");
        ra.client.setFirstName(unique.clientFirstNameFor("Paul"));
        ra.client.setBirthDate(new LocalDate(1967, 5, 27));
        ra.client.setGenderId(GENDER);
        ra.referral.setImportServiceName(service.getServiceName());
        ra.referral.setDecisionDate(startDate.toLocalDateTime(LocalTime.MIDNIGHT));
        ra.referral.setReceivedDate(startDate);
        ra.referral.setReceivingServiceDate(startDate);
        referralActor.submitReferralAggregateImport(ra);

        // Create an appointment type
        final String appointmentType = agreementActor.createAppointmentType(service, unique.idFor("Gazza"), 15);

        // Create an appointment schedule for the referral
        referralSteps.findClientThenReferral(ra.client.code, ra.client.firstName, ra.client.lastName,
                ra.referral.referralCode);
        ReferralOptions options = ReferralOptions.DEMO_ALL.requiresDeliveredBy(true).requiresSource(true)
                .requiresFunding(true).requiresSupportStaffNotes(true).withServiceAgreement(appointmentType, null);
        referralSteps.processTaskDefinitions(options, service);

        // Find the agreement for our client
        ResponseEntity<RecurringDemandScheduleDto[]> schedules = agreementActor.getAppointmentSchedulesFromAgreementForClient(ra.getReferral().serviceRecipientId, new DateTime());
        LocalDate scheduleEndDate = schedules.getBody()[0].getEnd();
        LocalDate newEndDate = scheduleEndDate.minusDays(2);

        // Now close off the referral early and check the appointment schedule.
        referralSteps.exited(ra.client.firstName, ra.client.lastName, newEndDate); // TODO: CAUSES FAIL here as haven't completed enough to enable close off

        // Re-fetch the schedule and check.
        schedules = agreementActor.getAppointmentSchedulesFromAgreementForClient(ra.referral.serviceRecipientId,
                new DateTime());
        scheduleEndDate = schedules.getBody()[0].getEnd();

        assertEquals("Expect end date to have changed to match referral end date", newEndDate, scheduleEndDate);

        loginSteps.logout();
        logoutApi();
    }

    // return or create a referral
    // NB requires restClient.login
    private ReferralViewModel ensureReferralView(String code) {

        // check for existance
        ResponseEntity<ReferralViewModel> response = referralActor.getReferralByCode(code);
        ReferralViewModel rvm = response.getBody();
        if (rvm != null && response.getStatusCode().is2xxSuccessful()) {
            // move to the referralView, so the ui is consistent with creating
            referralSteps.findClientThenReferral(rvm.getClientId().toString(), rvm.getClientFirstName(),
                    rvm.getClientLastName(), rvm.getReferralCode());
            return rvm;
        }

        // create if not - and stay on a referralView page
        ReferralOptions options = new ReferralOptions().requiresAccommodation(true);
        LocalDate dob = LocalDate.now();
        rvm = referralSteps.createReferral(true, false, false, null,
                options, ACCOMMODATION, "FirstName", "LastName", dob,
                TEST_NI_NUMBER, "male", "English", "Missing");

        // update the referral's code to identify it in further tests
        // use web-api as its not available on the ui (only clients code is available when the 'messages' table has an lastClientID entry)
        // oldValue is by default the same as the id
        // NB this will leave the referralView above out of date (version number incremented), but recent changes should mean that the referral is loaded when clicked
        updateTextCommandActor.updateProperty("referrals", rvm.getReferralId(), "code", code,
                rvm.getReferralCode());

        // re-load because the ui doesn't have access to all the properties of the ReferralViewModel but the web-api does
        // and since we expect it in the return above, simply get it again
        response = referralActor.getReferralByCode(code);
        rvm = response.getBody();
        return rvm;
    }

    /*
     *  Oana: TODO: I suggest the following for this test
     public void sysadminUserCanCreateNewReferralSeeItAndViewReports(){
        userUI.login(Constants.SYSADMIN_USERNAME, Constants.SYSADMIN_PASSWORD);
        createReferral(all the details needed for a new referral: usingAccomodation, options, firstName,...); implement this class in ReferralUI or any
            other class that deals with referrals. Create there one method that can be called from any other class
            just having different parameters
        viewReport(typeOfReport); move the implementation of this class to a more relevant place;
            to Report.java or any other class that deals only with reports. Depending on the typeOfReport
            parameter, choose what to click on.
        assertTrue(whatever is relevant for this test); Probably a relevant info to check would be to check
            that the referral that was just created appears in the report.

     }

     This way the test remains clear to read and understand / maintain. I don;t see why the viewReports method
     is implemented here, and not in a class that deals specifically with reports (of course, considering
     the fact that this method will be used more than in this test only)

     this test class should probably contain methods for verifying all types of reports

     */

}
