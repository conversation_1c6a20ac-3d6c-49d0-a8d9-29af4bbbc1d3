package com.ecco.acceptancetests.ui.steps.webdriver;

import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeMatcher;
import org.hamcrest.core.IsEqual;
import org.openqa.selenium.WebElement;

public class WebElementMatchers {
    public static class WebElementTextMatcher extends TypeSafeMatcher<WebElement> {
        private final Matcher<String> expected;

        public WebElementTextMatcher(Matcher<String> expected) {
            this.expected = expected;
        }

        @Override
        protected boolean matchesSafely(WebElement item) {
            return expected.matches(item.getText());
        }

        @Override
        public void describeTo(Description description) {
            description.appendText("element with text ").appendDescriptionOf(expected);
        }
    }

    public static Matcher<? super WebElement> elementWithText(Matcher<String> expected) {
        return new WebElementTextMatcher(expected);
    }

    public static Matcher<? super WebElement> elementWithText(String expected) {
        return new WebElementTextMatcher(new IsEqual<>(expected));
    }
}
