package com.ecco.acceptancetests.ui.smoke

import com.ecco.acceptancetests.expectations.OfflineExpectation
import com.ecco.acceptancetests.givens.OfflineGiven
import com.ecco.acceptancetests.steps.OfflineContext
import com.ecco.acceptancetests.ui.BaseSeleniumTest
import com.ecco.data.client.ReferralOptions
import com.ecco.data.client.ServiceOptions
import com.ecco.test.support.RetryRule
import com.ecco.webApi.evidence.ReferralViewModel
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class OnlineTests : BaseSeleniumTest() {
    // This is set by .as("referral")
    private val referral: ReferralViewModel? = null
    private var `when`: OfflineContext? = null
    private var given: OfflineGiven? = null
    private var expect: OfflineExpectation? = null

    @BeforeEach
    fun setUp() {
        val onlineSteps = offlineSteps.forOnline()

        given = onlineSteps.given(this)
        expect = onlineSteps.expect()
        `when` = given!!.`when`()
    }

    @RetryRule.Retries(1)
    @Test
    @Disabled("This is WIP")
    fun userCanLoginAndSeeMyReferrals() {
        login("sysadmin")

        // given a referral exists for James Blunt
        given!!.ensureReferral("OnT01", "James", "Bluntish", ServiceOptions.DEMO_ALL, ReferralOptions.DEMO_ALL).`as`(
            "referral",
        )

        val names =
            `when`!!
                .viewReferrals()

        // i expect the named referral (james blunt) is seen in the offline referrals list
        val clientName = referral!!.clientDisplayName
        expect!!.canSeeText(clientName)

        assertThat(names).contains(clientName)

        // TODO:
        // when.gotoReferralOverview(clientName)
        //     .openTasksList()

        //        expect.canSeeText("emergency details")
        //                .canSeeText("case notes")
        //                .canSeeText("bananas");

        // when

        logout()
    }
}