package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.Constants;

public class LoginPage extends EccoBasePage {

    private static final String URL = "/nav/secure/login";

    public static final String FIELD_USERNAME = "j_username";
    public static final String FIELD_PASSWORD = "j_password";
    private static final String BUTTON_XPATH_SUBMIT = "//input[@type='submit']";

    public LoginPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public WelcomePage login(String username, String password) {
        verifyIsCurrentPage();
        waitForVisibleElement("expected submit", By.name(FIELD_USERNAME)); // this is the last field to appear
        waitFor(200); // small delay to cope with animation
        setField(FIELD_USERNAME, username);
        setField(FIELD_PASSWORD, password);
        clickElementXpath(BUTTON_XPATH_SUBMIT);
        WelcomePage oldWelcomePage = new WelcomePage(getWebDriver());
        // after logging in, we are at the new page, so jump back go to old welcome page for tests
        oldWelcomePage.navigateAsEntryPoint();
        waitForPageLoaded();
        return oldWelcomePage;
    }

    @Override
    public EccoBasePage defaultAction() {
        login(Constants.SYSADMIN_USERNAME, Constants.SYSADMIN_PASSWORD);
        return new WelcomePage(getWebDriver());
    }

}
