package com.ecco.acceptancetests.ui.pages;

import java.util.List;

import org.jspecify.annotations.Nullable;

import org.hamcrest.Matcher;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedCondition;

import static com.ecco.acceptancetests.ui.steps.webdriver.WebElementMatchers.elementWithText;
import static org.hamcrest.Matchers.hasItem;

public class CalendarPage extends BasePageObject {

    private static final String URL = "/online/viewCalendar";
    private static final DateTimeFormatter dataDateFmt = DateTimeFormat.forPattern("YYYY-MM-dd");
    public CalendarPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public void gotoToday() {
        gotoDay(new DateTime());
    }

    /** Not sure what this was supposed to do, so for now preventing it from doing anything! */
    @Deprecated
    public void gotoDay(DateTime day) {
//        String dataDate = dataDateFmt.print(day);
//        final WebElement element = getWebDriver().findElement(By.xpath("//td[@data-date='" + dataDate + "']"));
//        if (!element.getAttribute("class").contains("fc-state-disabled")) {
//            element.click();
//            waitForPageLoaded();
//        }
    }

    public void clickToday() {
        clickDay(new DateTime());
    }

    public void clickDay(DateTime day) {
        String dataDate = dataDateFmt.print(day);
        // click on the date of month label that appears at the top of each grid cell
        final WebElement element = getWebDriver().findElement(By.xpath("//thead/tr/td[@data-date='" + dataDate + "']"));
        element.click();
        waitForVisibleElement("Input field 'name' never appeared", By.name("name"));
    }

    public void enterEvent(String description, String... attendees) {
        setField("name", description);
        for (String attendee : attendees) {
            setCheckboxLabel(attendee, true);
        }
        setRadio("eventType", "-1");
        clickButton("_save");
    }

    public void checkEventShown(final String expected) {
        final Matcher<? super WebElement> elementMatcher = elementWithText(expected);
        final Matcher<Iterable<? super WebElement>> iterableMatcher = hasItem(elementMatcher);
        waitFor("Event not shown after creation", new ExpectedCondition<Boolean>() {
            @Override
            public Boolean apply(final @Nullable WebDriver webDriver) {
                List<WebElement> elements = webDriver.findElements(By.className("fc-title"));
                boolean matches = iterableMatcher.matches(elements);
                return matches;
            }
            @Override
            public boolean equals(@Nullable Object obj) {
                return super.equals(obj);
            }
        });
    }


}
