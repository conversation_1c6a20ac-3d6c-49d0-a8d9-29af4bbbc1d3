
package com.ecco.acceptancetests.ui.pages;

import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.WebDriver;

public class UserAuditReportPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/reports/userAudit";

    private static final String BUTTON_SECURITYAUDIT_NAME = "reportsecurityaudit-list";
    private static final String BUTTON_LOGINAUDIT_NAME = "reportloginaudit-list";
    private static final String USERNAME = "username";

    public UserAuditReportPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public void enterUsername(String username) {
        // allow us to specify no value to test - which is typically what people try first
        if (StringUtils.isNotBlank(username))
            setSelection(USERNAME, username);
    }

    public void clickSecurityAuditReport() {
        clickButton(BUTTON_SECURITYAUDIT_NAME);
    }
    public void clickLoginAuditReport() {
        clickButton(BUTTON_LOGINAUDIT_NAME);
    }

}
