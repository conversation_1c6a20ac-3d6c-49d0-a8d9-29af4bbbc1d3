package com.ecco.acceptancetests.ui.pages;

import org.hamcrest.Description;
import org.hamcrest.TypeSafeMatcher;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import java.io.File;
import java.io.IOException;
import java.util.regex.Pattern;

/**
 * @since 14/10/2014
 */
public class UploadPage extends BasePageObject {
    private static final String URL = "/dynamic/secure/.*/upload";
    private static final String FIELD_FILE = "file";

    public UploadPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    @Override
    public void verifyIsCurrentPage() {
        verifyCurrentPage(matchesUrl());
    }

    private RegexSubstringMatcher matchesUrl() {
        return new RegexSubstringMatcher();
    }

    public UploadPage uploadFile(File file) throws IOException {
        findElementSoon(By.name(FIELD_FILE)).sendKeys(file.getCanonicalPath());
        return this;
    }

    private static class RegexSubstringMatcher extends TypeSafeMatcher<String> {
        private static final Pattern URL_PATTERN = Pattern.compile(URL);

        @Override
        protected boolean matchesSafely(String item) {
            return URL_PATTERN.matcher(item).find();
        }

        @Override
        public void describeTo(Description description) {
            description.appendText("contains regex ").appendValue(URL);
        }
    }
}
