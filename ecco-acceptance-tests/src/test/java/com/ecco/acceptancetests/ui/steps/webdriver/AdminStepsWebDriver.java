package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.AdminSteps;
import com.ecco.acceptancetests.ui.pages.AdminDeleteReferralClientPage;
import com.ecco.acceptancetests.ui.pages.AdminPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;

import org.openqa.selenium.WebDriver;

public class AdminStepsWebDriver extends WebDriverUI implements AdminSteps {

    public static String DELETEPAGE_TEXT = "1 - Find referral by id or code to check you have the right one";
    public AdminStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @Override
    public void deleteReferralClient() {
        AdminPage adminPage = adminPage();
        AdminDeleteReferralClientPage deleteReferralClientPage = adminPage.gotoDeleteReferralClientPage();
        checkCanSeeText(DELETEPAGE_TEXT);
    }

    @Override
    public void deleteReferralClientDirect() {
        navigate("/nav/secure/admin/deletions.html");
        checkCanSeeText(DELETEPAGE_TEXT);
    }

    private AdminPage adminPage() {
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();

        // we'd like to click a button on settings perhaps, but for now its direct page access
        //SettingsPage settingsPage = welcomePage.gotoSettings();
        //AdminPage adminPage = settingsPage.gotoAdminPage();
        navigate(AdminPage.URL);
        AdminPage adminPage = new AdminPage(webDriver);
        waitForPageLoaded();

        // verify access
        adminPage.verifyIsCurrentPage();
        checkCannotSeeText("You do not have security rights to perform that request.");

        return adminPage;
    }

}
