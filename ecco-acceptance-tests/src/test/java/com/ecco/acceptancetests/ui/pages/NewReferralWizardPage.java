package com.ecco.acceptancetests.ui.pages;


import com.ecco.acceptancetests.ui.pages.referral.FindReferralsPage.ExistingClientOutcome;
import com.ecco.data.client.ServiceOptions;
import org.openqa.selenium.By;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import static org.junit.Assert.assertTrue;

public class NewReferralWizardPage extends EccoBasePage {

    private static final String URL = "/online/r/referrals/new";

    private static final String LINK_NAV_SERVICE = "service";
    private static final String LINK_NAV_PROJECT = "project";

    private static final String NO_CLIENTS = "no matches - a new client: please enter more details";
    private static final String CLIENTS_FOUND = "matches found choose one or create a new one, or try another search.";
    private static final String PROBLEM_HEADER_TEXT = "there's a problem";

    private static final String REFERRALS_FOUND = "found referral(s) select an existing referral or continue new referral";
    private static final String NO_REFERRALS = "no referrals found no referral exists. continue new referral";

    private static final String MATCH_HEADER_TEXT = "matched a known client: check your referral hasn't already been processed";

    public NewReferralWizardPage(WebDriver webDriver) {
        super(URL, webDriver);
    }


    public NewReferralWizardPage gotoServiceStep() {
        clickLink(LINK_NAV_SERVICE);
        return this;
    }

    @Override
    public EccoBasePage defaultAction() {
        return null;
    }

    public void selectService(ServiceOptions service) {
        clickLink(service.getServiceName());
    }

    public void selectFirstProject() {
        clickElementByCssSelector("ul.list-unstyled > li > a");
    }

    public void navigateNewClient() {
        clickLink("create a new one");
    }

    public void searchClient(String firstName, String lastName) {
        setFieldSoonByXpathSelector("(//input[@type='text'])[1]", firstName + " " + lastName);
        clickButtonById("search");
    }

    public void selectFirstClient() {
        clickElementByCssSelector("table.table > tbody > tr > td > a");
    }

    public void navigateNewReferral() {
        clickLink("continue new referral");
    }

    public void selectSelfReferral() {
        clickButtonByText("self referral");
        clickButtonByText("next");
    }

    public void finish() {
        clickButtonByText("save");
    }

    private void assertActiveBreadcrumbIs(String contentText) {
        try {
            findElementSoon(By.xpath("//li[contains(@class, 'active')]//a[contains(text(),'" + contentText + "')]"));
        } catch (Exception e) {
            throw new TimeoutException("Referral Wizard: failed waiting for active breadcrumb: " + contentText, e);
        }
    }

    public void assertServiceStep() {
        assertActiveBreadcrumbIs("service");
    }

    public void assertProjectStep() {
        assertActiveBreadcrumbIs("project");
    }

    public void assertClientStep() {
        assertActiveBreadcrumbIs("client");
    }

    public void assertReferralStep() {
        assertActiveBreadcrumbIs("referral");
    }

    public void assertSourceStep() {
        assertActiveBreadcrumbIs("source");
    }

    public void assertFinalStep() {
        assertActiveBreadcrumbIs("complete");
    }

    public void verifyIsCurrentPageNewClient() {
        super.verifyIsCurrentPage();
        waitForIdContainingText("status-message", NO_CLIENTS);
//        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
//        assertTrue("Incorrect page found - expected to see text " + NEW_SUB_HEADER_TEXT,
//                bodyText.getText().contains(NEW_SUB_HEADER_TEXT));
    }

    // either we see existing clients: "there are matches"
    // or we see messages from hasExistingReferrals
    public ExistingClientOutcome hasExistingClients(boolean existingClientExpected) {
        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
        boolean existing = bodyText.getText().contains(CLIENTS_FOUND);
        assertTrue("client match expectations not met", (existingClientExpected && existing) || (!existingClientExpected && !existing));
        if (bodyText.getText().contains(PROBLEM_HEADER_TEXT)) {
            return ExistingClientOutcome.PROBLEM;
        }
        return existing ? ExistingClientOutcome.MATCH : ExistingClientOutcome.NO_MATCH;
    }

    // either we see existing referrals: "found referral(s) select an existing referral or continue new referral"
    // or we see "no referrals found no referral exists. continue new referral"
    public boolean hasExistingReferrals(boolean existingClientExpected) {
        WebElement bodyText = getWebDriver().findElement(By.id("status-message"));
        boolean existing = bodyText.getText().contains(REFERRALS_FOUND);
        boolean newNotExisting = bodyText.getText().contains(NO_REFERRALS);
        // assert that we have one page or the other
        assertTrue("Incorrect page found - expected to see text one of 'no referrals found' or 'found referral(s)'",
                existing | newNotExisting);
        // check our expectations
        assertTrue("referral match expectations not met",
                (existingClientExpected && existing) || (!existingClientExpected && !existing));
        return existing;
    }

    public void useExistingClient(String expectedReferralClientName) {
        clickLink(expectedReferralClientName);
    }
}
