package com.ecco.acceptancetests.ui.pages.supportplan;

import org.openqa.selenium.WebDriver;

public class BeHealthyTab extends SupportPlanBasePage {

    public BeHealthyTab(WebDriver webDriver) {
        super(webDriver);
    }

    /*
     * The following text: with tick/cross images, ticks turn text into links...
     * - Abuse <PERSON>
     * - Abuse Drugs
     * - Abu<PERSON>
     * - Poor Diet
     * - Lack of health awareness – ie not attending GP or Dentist when appropriate to do so
     * - No physical activity/fitness regime
     */
}
