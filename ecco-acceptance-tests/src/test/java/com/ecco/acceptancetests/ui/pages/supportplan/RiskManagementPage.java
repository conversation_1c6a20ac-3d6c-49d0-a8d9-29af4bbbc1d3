package com.ecco.acceptancetests.ui.pages.supportplan;

import org.openqa.selenium.WebDriver;

public class RiskManagementPage extends SupportPlanBasePage {

    public RiskManagementPage(WebDriver webDriver) {
        super(webDriver);
    }

    public void setTrigger(String trigger) {
        String triggerPath = "label[contains(text(), 'trigger')]/../textarea";
        xpathWithinActiveBootstrapTab(triggerPath).sendKeys(trigger);
    }

    public void setControl(String control) {
        String controlPath = "label[contains(text(), 'control')]/../textarea";
        xpathWithinActiveBootstrapTab(controlPath).sendKeys(control);
    }

    public void setOutcomeLevelRed() {
        String radioPath = "label[@for='red']/input";
        xpathWithinActiveBootstrapTab(radioPath).click();
    }

    public void setOutcomeLevelAmber() {
        String radioPath = "label[@for='amber']/input";
        xpathWithinActiveBootstrapTab(radioPath).click();
    }

    public void setOutcomeLevelGreen() {
        String radioPath = "label[@for='green']/input";
        xpathWithinActiveBootstrapTab(radioPath).click();
    }

}
