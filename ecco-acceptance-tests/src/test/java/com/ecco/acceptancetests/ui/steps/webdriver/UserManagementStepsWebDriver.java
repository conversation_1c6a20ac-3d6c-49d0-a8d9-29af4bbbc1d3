package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.UserManagementSteps;
import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.ui.pages.Role;
import com.ecco.acceptancetests.ui.pages.SettingsPage;
import com.ecco.acceptancetests.ui.pages.UserDetailsPage;
import com.ecco.acceptancetests.ui.pages.UserManagementPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;

import com.ecco.data.client.actors.UserActor;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import org.jspecify.annotations.NonNull;
import org.openqa.selenium.WebDriver;

import java.util.List;

public class UserManagementStepsWebDriver extends WebDriverUI implements UserManagementSteps {

    // WIP: This will probably be injected per thread by the factory
    private final UniqueDataService unique = UniqueDataService.instance;
    private UserActor userActor;

    public UserManagementStepsWebDriver(WebDriver webDriver,
                                        UserActor userActor) {
        super(webDriver);
        this.userActor = userActor;
    }

    @NonNull
    @Override
    public String createUser(@NonNull String username, @NonNull Role... group) {
        String uniqueUsername = unique.userNameFor(username);
        String uniquePwd = unique.passwordFor(username);
        createUser(unique.firstNameFor(username), unique.lastNameFor(username),
                uniqueUsername, uniquePwd, group);

        userActor.createPassword(uniqueUsername, uniquePwd);
        return uniqueUsername;
    }

    @Override
    public void createSharedUser(@NonNull String username, @NonNull Role... group) {
        createUser("Dallas", username, username, username, group);
    }

    private void createUser(String firstNameFor, String lastNameFor, String userNameFor, String passwordFor,
            Role... groups) {
        // make sure we start on the Welcome page
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        SettingsPage settingsPage = welcomePage.gotoUsersAndSettings();
        UserManagementPage userManagementPage = settingsPage.gotoLogins();
        UserDetailsPage userDetailsPage = userManagementPage.gotoAddUserPage();
        userManagementPage = userDetailsPage.createNewUser(firstNameFor, lastNameFor,
                userNameFor, passwordFor, groups);
        checkCanSeeTextInTag("users", "h6");
    }

    @NonNull
    @Override
    public List<String> listUsers() {
        // make sure we start on the Welcome page
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        SettingsPage settingsPage = welcomePage.gotoSettings();
        UserManagementPage userManagementPage = settingsPage.gotoLogins();
        return userManagementPage.getVisibleUsers();
    }

    @NonNull
    @Override
    public List<String> listUserGroups() {
        // make sure we start on the Welcome page
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        SettingsPage settingsPage = welcomePage.gotoSettings();
        UserManagementPage userManagementPage = settingsPage.gotoLogins();
        return userManagementPage.getVisibleGroups();
    }

    @NonNull
    @Override
    public List<String> listUsers(@NonNull String initial, @NonNull String groupName, boolean enabledOnly) {
        // make sure we start on the Welcome page
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        SettingsPage settingsPage = welcomePage.gotoSettings();
        UserManagementPage userManagementPage = settingsPage.gotoLogins();
        userManagementPage.chooseInitial(initial);
        userManagementPage.chooseGroup(groupName);
        userManagementPage.chooseEnabledOnly(enabledOnly);
        return userManagementPage.getVisibleUsers();
    }

    @NonNull
    @Override
    public IndividualUserSummaryViewModel createIndividualWithUser(@NonNull String username, @NonNull String newPassword,
                                                                   @NonNull String firstName, @NonNull String lastName,
                                                                   @NonNull Role ...groups) {
        throw new UnsupportedOperationException("Not completed on WebDriver");
    }
}
