package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.CalendarSteps;
import com.ecco.acceptancetests.ui.pages.CalendarPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;

import org.joda.time.DateTime;
import org.jspecify.annotations.NonNull;
import org.openqa.selenium.WebDriver;

public class CalendarStepsWebDriver extends WebDriverUI implements CalendarSteps {

    public CalendarStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @Override
    public void createCalendarEventForDay(@NonNull String description, int daysInFuture) {
        // make sure we start on the Welcome page
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        CalendarPage calendarPage = welcomePage.gotoCalendar();
        calendarPage.gotoDay(new DateTime().plusDays(daysInFuture));
        calendarPage.clickDay(new DateTime().plusDays(daysInFuture));
        calendarPage.enterEvent(description);
        calendarPage.verifyIsCurrentPage();
        calendarPage.checkEventShown(description);
    }

    @Override
    public void addCalendarEventForTomorrowWithAttendees(@NonNull String description, @NonNull String... attendees) {
        CalendarPage page = new CalendarPage(webDriver);
        page.gotoDay(new DateTime().plusDays(1));
        page.clickDay(new DateTime().plusDays(1));
        page.enterEvent(description, attendees);
        page.verifyIsCurrentPage();
        page.checkEventShown(description);
    }
}
