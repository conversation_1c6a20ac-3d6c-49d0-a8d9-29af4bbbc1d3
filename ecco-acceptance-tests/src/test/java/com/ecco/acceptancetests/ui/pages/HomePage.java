package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class HomePage extends BasePageObject {

    public static final String URL = "/nav/index.html";

    public HomePage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public LoginPage gotoLoginPage() {
        verifyIsCurrentPage();
        getPageHeader().gotoLogin();
        waitForPageLoaded();
        return new LoginPage(getWebDriver());
    }

}
