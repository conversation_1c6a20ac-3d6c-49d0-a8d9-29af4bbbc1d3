package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import java.util.Map;

/**
 * @since 05/08/2013
 */
public class ListsPage extends BasePageObject {
    private static final String URL = "/dynamic/secure/listIdNames";
    private static final String LINK_NEW = "new";
    private static final String BUTTON_SAVE_NAME = "_save";
    private static final String LINK_BACK = "back";

    public enum List {
        REFERRAL_ACTIVITY_TYPES("referral activity types");

        private final String linkName;

        private List(String linkName) {
            this.linkName = linkName;
        }

        public String getLinkName() {
            return linkName;
        }
    }

    public ListsPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public void addToList(List list, Map<String, Object> values) {
        verifyIsCurrentPage();
        clickLink(list.getLinkName());
        clickLink(LINK_NEW);
        for (Map.Entry<String, Object> field : values.entrySet()) {
            if (field.getValue() instanceof Boolean) {
                setCheckbox(field.getKey(), (Boolean) field.getValue());
            } else {
                String value = String.valueOf(field.getValue());
                setField(field.getKey(), value);
            }
        }
        clickButton(BUTTON_SAVE_NAME);
        if (values.containsKey("name")) {
            waitForVisibleElement("Newly added item not shown in list", By.linkText(values.get("name").toString()));
        }
        clickLink(LINK_BACK);
    }

}
