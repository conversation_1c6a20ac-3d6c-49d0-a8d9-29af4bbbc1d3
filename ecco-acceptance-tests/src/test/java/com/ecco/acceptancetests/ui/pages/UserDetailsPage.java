package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class UserDetailsPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/userManagementFlow";

    private static final String FIELD_FIRST_NAME = "firstName";
    private static final String FIELD_LAST_NAME = "lastName";
    private static final String FIELD_USERNAME = "username";
    private static final String FIELD_PASSWORD_NEW = "newPassword";
    private static final String FIELD_ENABLED = "enabled";
    private static final String FIELD_GROUPS = "newGroups";

    private static final String BUTTON_SAVE_ID = "_eventId_save";

    public UserDetailsPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

//    @Override
//    public void verifyIsCurrentPage() {
//        super.verifyIsCurrentPage();
//        // TODO add some logic to ensure we're on the right stage here ?
//    }

    public UserManagementPage createNewUser(String firstName, String lastName, String username, String password, Role... groups) {
        setField(FIELD_FIRST_NAME, firstName);
        setField(FIELD_LAST_NAME, lastName);
        setField(FIELD_USERNAME, username);
       // setField(FIELD_PASSWORD_NEW, password);
        setCheckbox(FIELD_ENABLED, true);
        for (String roleName : Role.rolesToNames(groups)) {
            setCheckbox(roleName, true);
        }
        clickButton(BUTTON_SAVE_ID);
        return new UserManagementPage(getWebDriver());
    }
}
