package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.steps.ReportSteps.ReportOptions;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;

import org.junit.jupiter.api.Test;

public class SecurityTests extends BaseSeleniumTest {

    @Test
    public void reports_noErrorMessageWhenNoUsername() throws Exception {
        loginSteps.loginAsSysadmin();

        // difficult to test since it relies on a session terminating
        //viewLoginReport();

        viewSecurityReport();
        loginSteps.navigateToWelcome(); // until ECCO-627 we get an error with just clicking back when choosing a service (its going back to a post)

        loginSteps.logout();
    }

    private void viewSecurityReport() {
        ReportOptions repOptions = new ReportOptions();
        reportLoginAuditSteps.viewSecurityReport(null);
        reportLoginAuditSteps.checkCanSeeText("please select a username for the security audit report");
    }

}
