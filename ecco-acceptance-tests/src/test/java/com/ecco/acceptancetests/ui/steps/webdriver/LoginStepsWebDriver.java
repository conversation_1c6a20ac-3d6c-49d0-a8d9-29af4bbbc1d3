package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.Constants;
import com.ecco.acceptancetests.steps.LoginSteps;
import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.ui.pages.HomePage;
import com.ecco.acceptancetests.ui.pages.LoginPage;
import com.ecco.acceptancetests.ui.pages.LogoutPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;

import org.jspecify.annotations.NonNull;
import org.openqa.selenium.WebDriver;

public class LoginStepsWebDriver extends WebDriverUI implements LoginSteps {

    private final UniqueDataService unique = UniqueDataService.instance;

    public LoginStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @Override
    public void login(@NonNull String user) {
        login(unique.userNameFor(user), unique.passwordFor(user));
    }

    @Override
    public void loginAsSysadmin() {
        login(Constants.SYSADMIN_USERNAME, Constants.SYSADMIN_PASSWORD);
        checkCanSeeText("sysadmin");
    }

    @Override
    public void login(@NonNull String username, @NonNull String password) {
        navigateToHome();
        HomePage home = new HomePage(webDriver);
        LoginPage loginPage = home.gotoLoginPage();
        WelcomePage welcomePage = loginPage.login(username, password);
        welcomePage.verifyIsCurrentPage();
        checkCannotSeeText("oops!");
        checkCannotSeeText("there was a problem!");
        // TODO: Extract below as checkCorrectMenusAreVisibleForUser(username) and add tests
        if (password.equals(Constants.SYSADMIN_PASSWORD) && username.equals(Constants.SYSADMIN_USERNAME)) {
            checkCanSeeTextAtXpath("sysadmin", "/html/body/div[1]/header/div/span[2]", 12);

            checkCanSeeText("reports");
        }
    }

    @Override
    public void logout() {
        navigateToWelcome();
        waitForPageLoaded();
        new LogoutPage(webDriver).logout();
        checkCanSeeText("Thank you for everything you do");
    }

}
