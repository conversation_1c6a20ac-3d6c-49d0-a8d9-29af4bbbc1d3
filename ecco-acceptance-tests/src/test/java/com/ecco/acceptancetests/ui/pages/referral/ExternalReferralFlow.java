package com.ecco.acceptancetests.ui.pages.referral;

// the 'flow' has been replaced by a contents page with links to referral 'aspects'
// however, the flow could be introduced for external logins wanting to create a referral
// so we just cut it out for now - but these are disabled elsewhere for now
public interface ExternalReferralFlow {

    /**
     * Enter default info into fields on the page (or no data at all, where none is required)
     */
    //public void defaultAction();

    //public ExternalReferralFlow resultingPage();
    /*
    //if (this instanceof ClientDetailsPage) {
        //return new ClientContactDetailsPage(getWebDriver());
    //} else if (this instanceof ClientContactDetailsPage) {
        //return new ServicePage(getWebDriver());
    // ...
    // end of wizard - what to return?

    Assert.fail("Unknown page");
    return null;
    // throw an exception here instead?
    */

}
