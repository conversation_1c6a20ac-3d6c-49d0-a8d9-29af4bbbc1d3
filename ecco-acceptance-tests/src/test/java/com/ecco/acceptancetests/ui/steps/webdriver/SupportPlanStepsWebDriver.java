package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.expectations.SupportPlanExpectation;
import com.ecco.acceptancetests.fixtures.SupportPlanFixture;
import com.ecco.acceptancetests.givens.SupportPlanGiven;
import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.acceptancetests.steps.SupportPlanContext;
import com.ecco.acceptancetests.ui.pages.WelcomePage;
import com.ecco.acceptancetests.ui.pages.referral.ListReferralsPage;
import com.ecco.acceptancetests.ui.pages.referral.TaskDefinitionsService1;
import com.ecco.acceptancetests.ui.pages.supportplan.*;
import com.ecco.acceptancetests.ui.smoke.SupportPlanTests;

import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.openqa.selenium.WebDriver;

import java.util.List;


/**
 * SupportPlanContext is meant to capture all the user activity around support planning
 */
public class SupportPlanStepsWebDriver extends WebDriverUI implements SupportPlanContext {

    TaskDefinitionsService1 referralPage = new TaskDefinitionsService1(webDriver);
    ReferralSteps referralSteps;

    public SupportPlanStepsWebDriver(ReferralSteps steps, WebDriver webDriver) {
        super(webDriver);
        this.referralSteps = steps;
    }

    @NonNull
    @Override
    public SupportPlanFixture openSupportPlan(@NonNull String firstName, @NonNull String lastName) {

        // make sure we start on the Welcome page     x
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        FindSupportPlansPage findPage = welcomePage.gotoSupportPlans();
        ListReferralsPage listPage = findPage.find(firstName, lastName);

        SupportPlanPage sp = listPage.openSupportPlan(firstName, lastName);

        // Double-check we've opened the correct referral
        sp.checkReferralName(firstName + " " + lastName);

        checkCanSeeText("support plan");

        return new SupportPlanFixture(new SupportPlanPage(webDriver));
    }

    @NonNull
    @Override
    public SupportPlanFixture openNeedsAssessment(@NonNull String firstName, @NonNull String lastName) {

        referralSteps.listReferrals();
        // now on the referral list page...
        ListReferralsPage listPage = new ListReferralsPage(webDriver);
        listPage.openReferral(firstName, lastName);

        referralPage.needsAssessment();

        NeedAssessmentPage na = new NeedAssessmentPage(webDriver);
        //na.checkReferralName(firstName + " " + lastName);

        return new SupportPlanFixture(new SupportPlanPage(webDriver));
    }

    @NonNull
    @Override
    public SupportPlanFixture reviewSetup(@NonNull String firstName, @NonNull String lastName, @NonNull LocalDate newReviewDate) {
        // make sure we start on the Welcome page
        referralSteps.listReferrals();

        // now on the referral list page...
        ListReferralsPage listPage = new ListReferralsPage(webDriver);
        listPage.openReferral(firstName, lastName);

        referralPage.review();

        ReviewSetupPage rev = new ReviewSetupPage(webDriver);

        if (newReviewDate != null) {
            rev.setup(newReviewDate);
        } else {
            rev.continueReview();
        }

        return new SupportPlanFixture(new ReviewPage(webDriver));
    }

    @NonNull
    @Override
    public List<String> getNeedsAssessmentTabs(@NonNull String firstName, @NonNull String lastName) {
        // make sure we start on the Welcome page
        openNeedsAssessment(firstName, lastName);
        NeedAssessmentPage na = new NeedAssessmentPage(webDriver);
        return na.getAvailableTabs();
    }

    @NonNull
    @Override
    public SupportPlanGiven given(@NonNull SupportPlanTests testFramework) {
        return new SupportPlanGiven(this, testFramework);
    }

    @NonNull
    @Override
    public SupportPlanExpectation expect() {
        return new SupportPlanExpectation(this);
    }
}
