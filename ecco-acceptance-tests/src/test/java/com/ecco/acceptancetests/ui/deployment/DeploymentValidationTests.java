package com.ecco.acceptancetests.ui.deployment;

import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.acceptancetests.ui.DisableOnCI;
import org.junit.jupiter.api.Test;

public class DeploymentValidationTests extends BaseSeleniumTest {

    @Test
    public void adminUserCanSeeHomeLoginAndCreateNewUser() {
        loginSteps.loginAsSysadmin();
        userManagementSteps.createUser("user");
        loginSteps.logout();
    }

    @DisableOnCI("Works when tested locally and manually")
    @Test
    public void adminUserCanCreateCalendarEntry() {
        loginSteps.loginAsSysadmin();
        calendarSteps.createCalendarEventForDay("my big event", 2);
    }

}
