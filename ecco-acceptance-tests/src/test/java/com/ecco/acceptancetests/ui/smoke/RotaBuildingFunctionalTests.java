package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.acceptancetests.ui.pages.Role;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

@SuppressWarnings({"OptionalGetWithoutIsPresent", "unused"})
public class RotaBuildingFunctionalTests extends BaseJsonTest {

    @Test
    public void bldgEndToEndTest() {
        loginAsSysadmin();

        String aptType = unique.nameFor("typeBld1");
        final FixedContainerViewModel building = rotaSteps.createBuilding(aptType);
        rotaSteps.createBuildingAgreement(building);
        // create ad-hoc appt demand against building with no resources assigned to it
        rotaSteps.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(building, LocalDateTime.now());

        // create a resource (worker) allocated to the building
        String userName = unique.userNameFor("rb-user1");
        IndividualUserSummaryViewModel uvm = userManagementSteps.createIndividualWithUser(userName, unique.passwordFor(userName),
                userName + "-first", userName + "-last", Role.staff);
        final String workerName = rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, building.buildingId).getName();

        // allocate worker to it
        rotaSteps.checkCanAssignResourceToFirstAppointmentToday("workers:all", "buildings:"+building.buildingId, ""+building.name, workerName);

        //checkCannotDeleteAppointment(clientName);
        //checkCanUnassignAppointmentFromWorker(clientName, workerName);
        //checkCanDeleteAppointment(clientName);

        logout();
    }


}
