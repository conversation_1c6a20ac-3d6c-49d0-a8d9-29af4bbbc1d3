package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class QUnitPage extends BasePageObject {

    /**
     * Navigate to the entry-point page
     *
     * @param relativePath path relative to tests directory (e.g. common/base64-test.html)
     */
    public QUnitPage(WebDriver webDriver, String relativePath) {
        super("/r/noCache/scripts/tests/" + relativePath, webDriver);
        super.navigateAsEntryPoint();
    }

    public int getTotalErrorCount() {
        String text = getTextByCssSelector("#qunit-testresult > span.failed");
        return Integer.parseInt(text);
    }
}
