package com.ecco.acceptancetests.ui.pages.supportplan;

import org.junit.Assert;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

public class CloseSupportPlanConfirmationPage extends SupportPlanBasePage {

    private static final String LINK_TEXT_CANCEL = "cancel";
    private static final String LINK_TEXT_CLOSE = "close-off support plan";
    private static final String TEXT_CONFIRMATION = "are you sure you want to close off the client with";

    public CloseSupportPlanConfirmationPage(WebDriver webDriver) {
        super(webDriver);
    }

    /**
     * Check the confirmation page displays the correct reason.
     * Input parameter should be one of the REASON_* constants from CloseSupportPlanPage.
     */
    public void checkCorrectReason(String reason) {
        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
        Assert.assertTrue("Confirmation text not displayed correctly on page", bodyText.getText().contains(TEXT_CONFIRMATION));
        Assert.assertTrue("Correct reason not found on page", bodyText.getText().contains(reason));
    }

    public void cancel() {
        clickLink(LINK_TEXT_CANCEL);
        // TODO next page?
    }

    public SupportPlanClosedPage close() {
        clickLink(LINK_TEXT_CLOSE);
        return new SupportPlanClosedPage(getWebDriver());
    }
}
