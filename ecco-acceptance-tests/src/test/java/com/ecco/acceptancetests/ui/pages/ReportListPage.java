package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class ReportListPage extends BasePageObject {

    public static final String URL = "/online/charts/";
    private static final String LINK_REFERRALREPORT = "referrals by service, then by project, then by worker, then breakdown";

    public ReportListPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public ReportReferralPage openReferralReport() {
        verifyIsCurrentPage();
        clickLink(LINK_REFERRALREPORT);
        return new ReportReferralPage(getWebDriver());
    }

}
