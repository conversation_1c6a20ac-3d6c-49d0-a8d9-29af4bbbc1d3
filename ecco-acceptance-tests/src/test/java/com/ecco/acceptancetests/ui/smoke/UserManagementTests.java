package com.ecco.acceptancetests.ui.smoke;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import com.ecco.acceptancetests.Constants;
import com.ecco.acceptancetests.TestUtils;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.acceptancetests.ui.ConstantsUI;
import com.ecco.acceptancetests.ui.pages.Role;

public class UserManagementTests extends BaseSeleniumTest {

    @Test
    public void sysadminUserCanCreateNewUserAdmin() throws Exception {
        loginSteps.loginAsSysadmin();
        String user1 = userManagementSteps.createUser("user1", Role.manager);
        loginSteps.logout();

        // Check the new user can log in
        loginSteps.login("user1");
        loginSteps.checkCanSeeText(user1);
        loginSteps.logout();
    }

    @Test // FIXME: Migrate to api test, as there is no new UI worth testing
    public void sysadminCannotCreateTwoUsersWithSameName() throws Exception {
        // Create a new Staff user
        loginSteps.loginAsSysadmin();
        userManagementSteps.createUser("user2", Role.useradmin);

        try {
            userManagementSteps.createUser("user2", Role.useradmin);
            fail();
        } catch (AssertionError e) {
            // TODO: we should be testing that the user got an error message, possibly even a 4xx error
            assertThat(e.getMessage(), equalTo("Text [user management] was expected to be visible in tag: h1 and is not"));
        }

        // TODO Need to go via "menu" link now that we don't have logout/login on "oops" page
        // 1userUI.logout();
    }

    @Test
    public void sysadminCanChangeSearch() throws Exception {
        loginSteps.loginAsSysadmin();

        // check only sysadmin group is in the drop down
        List<String> users = userManagementSteps.listUsers(null, ConstantsUI.SYSADMIN_GROUP, true);
        assertThat(users.size(), equalTo(1));
        assertThat(users.get(0), equalTo(Constants.SYSADMIN_USERNAME));

        // check same group with "s" for username initial
        users = userManagementSteps.listUsers("s", ConstantsUI.SYSADMIN_GROUP, true);
        assertThat(users.size(), equalTo(1));
        assertThat(users.get(0), equalTo(Constants.SYSADMIN_USERNAME));

        // check same but with enabled and disabled users
        users = userManagementSteps.listUsers("s", ConstantsUI.SYSADMIN_GROUP, false);
        assertThat(users.size(), equalTo(1));
        assertThat(users.get(0), equalTo(Constants.SYSADMIN_USERNAME));

        loginSteps.logout();
    }

    @Test
    public void userCannotSeeSysadminInGroups() throws Exception {
        loginSteps.loginAsSysadmin();

        String user1 = userManagementSteps.createUser("user9", Role.useradmin);
        loginSteps.logout();

        // Check the new user can log in
        loginSteps.login("user9");
        loginSteps.checkCanSeeText(user1);

        // check can see the drop down without sysadmin
        List<String> groups = userManagementSteps.listUserGroups();
        boolean exists = false;
        for (String group : groups) {
            if (StringUtils.equalsIgnoreCase("sysadmin", group)) {
                exists = true;
            }
        }
        assertThat(exists, equalTo(false));

        loginSteps.logout();
    }

    @Test
    @Disabled("Test is flaky due to paging - we need a safe approach.  Unlimited pageSize option would be sufficient")
    public void sysadminCanSeeAllUsersOnTheSystem() throws Exception {
        loginSteps.loginAsSysadmin();
        String user3 = userManagementSteps.createUser("user3", Role.useradmin);
        String[] addedUsers = {Constants.SYSADMIN_USERNAME, user3};
        TestUtils.listContains(userManagementSteps.listUsers(), addedUsers);
    }

}
