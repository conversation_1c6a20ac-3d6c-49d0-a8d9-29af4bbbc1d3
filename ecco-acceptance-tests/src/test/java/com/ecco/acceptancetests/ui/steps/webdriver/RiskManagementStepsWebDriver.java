package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.expectations.RiskManagementExpectation;
import com.ecco.acceptancetests.fixtures.RiskManagementFixture;
import com.ecco.acceptancetests.givens.RiskManagementGiven;
import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.acceptancetests.steps.RiskManagementContext;
import com.ecco.acceptancetests.ui.pages.referral.TaskDefinitionsService1;
import com.ecco.acceptancetests.ui.pages.supportplan.RiskManagementPage;
import com.ecco.acceptancetests.ui.smoke.RiskManagementTests;

import com.ecco.data.client.ServiceOptions;
import org.jspecify.annotations.NonNull;
import org.openqa.selenium.WebDriver;


public class RiskManagementStepsWebDriver extends WebDriverUI implements RiskManagementContext {

    TaskDefinitionsService1 referralPage = new TaskDefinitionsService1(webDriver);
    ReferralSteps referralSteps;

    public RiskManagementStepsWebDriver(ReferralSteps steps, WebDriver webDriver) {
        super(webDriver);
        this.referralSteps = steps;
    }

    @NonNull
    @Override
    public RiskManagementFixture openRiskManagement(@NonNull String firstName, @NonNull String lastName, @NonNull final String referralCode, @NonNull final ServiceOptions service) {

        referralSteps.findClientThenReferral(null, firstName, lastName, referralCode);

        referralPage.riskManagement();

        RiskManagementPage na = new RiskManagementPage(webDriver);
        // Not shown in modal -> na.checkReferralName(firstName + " " + lastName);

        return new RiskManagementFixture(new RiskManagementPage(webDriver));
    }

    /**
     * @param testsInstance
     *      the test object being run
     */
    @NonNull
    @Override
    public RiskManagementGiven given(@NonNull RiskManagementTests testsInstance) {
        return new RiskManagementGiven(this, testsInstance);
    }

    @NonNull
    @Override
    public RiskManagementExpectation expect() {
        return new RiskManagementExpectation(this);
    }
}
