package com.ecco.acceptancetests.ui.pages.supportplan;

import org.openqa.selenium.WebDriver;

public class RiskManagementBasePage extends SupportPlanBasePage {

    private static final String URL = "/dynamic/secure/---";

    // Tabs
    public static final String TAB_OUTCOME_RTO = "risk to others";
    public static final String TAB_OUTCOME_RTC = "risk to community";
    public static final String TAB_OUTCOME_RTP = "risk to property";
    public static final String TAB_OUTCOME_RTS = "risk to self";

    public RiskManagementBasePage(WebDriver webDriver) {
        super(URL, webDriver);
    }

}
