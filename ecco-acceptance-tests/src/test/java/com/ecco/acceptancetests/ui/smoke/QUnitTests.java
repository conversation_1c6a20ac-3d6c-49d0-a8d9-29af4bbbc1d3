package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.ui.BaseSeleniumTest;

import com.ecco.test.support.RetryRule;
import org.junit.jupiter.api.Test;

@RetryRule.Retries(2)
public class QUnitTests extends BaseSeleniumTest {

    @Test
    public void allLazyJsTestsPass() {
        qUnitUI.runQUnitTest("collections/Lazyjs-test.html");
    }

    @Test
    public void allEditableTestsPass() {
        qUnitUI.runQUnitTest("editable/editable-test.html");
    }

    @Test
    public void allHactTestsPass() {
        qUnitUI.runQUnitTest("hact/hact-test.html");
    }

    @Test
    public void allListDefTestsPass() {
        qUnitUI.runQUnitTest("data-attr/listdef-test.html");
    }

    @Test
    public void allListDefHierarchicalTestsPass() {
        qUnitUI.runQUnitTest("data-attr/listdef-hierarchical-test.html");
    }

    @Test
    public void allListDefHierarchicalSharedValueTestsPass() {
        qUnitUI.runQUnitTest("data-attr/listdef-hierarchical-sharedvalue-test.html");
    }

    @Test
    public void allListDefHierarchicalAutoTestsPass() {
        qUnitUI.runQUnitTest("data-attr/listdef-hierarchical-auto-test.html");
    }

    @Test
    public void allListDefSelect2TestsPass() {
        qUnitUI.runQUnitTest("data-attr/listdef-select2-test.html");
    }

    @Test
    public void allReportMovementTestsPass() {
        qUnitUI.runQUnitTest("reports/ReportTests.html");
    }

    @Test
    public void allInputTestsPass() {
        qUnitUI.runQUnitTest("inputs/input-test.html");
    }

}
