package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class SettingsPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/menuSetting";

    private static final String LINK_LOGINS = "users";
    private static final String LINK_OUTCOMES = "outcomes";
    private static final String LINK_LISTS = "lists";
    private static final String LINK_LOGO = "logo";

    public SettingsPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public UserManagementPage gotoLogins() {
        clickMaterialUIMenuItem(LINK_LOGINS);
        return new UserManagementPage(getWebDriver());
    }

    public void gotoOutcomes() {
        gotoPage(LINK_OUTCOMES);
    }

    public ListsPage gotoLists() {
        gotoPage(LINK_LISTS);
        return new ListsPage(getWebDriver());
    }

    private void gotoPage(String linkText) {
        clickLink(linkText);
    }

    public void menu() {
        getPageHeader().gotoMenu();
        waitForPageLoaded();
        verifyIsCurrentPage();
    }

    public UploadPage gotoLogo() {
        gotoPage(LINK_LOGO);
        return new UploadPage(getWebDriver());
    }
}
