package com.ecco.acceptancetests.ui.pages.supportplan;

import org.openqa.selenium.WebDriver;

public class StaySafeTab extends SupportPlanBasePage {

    public StaySafeTab(WebDriver webDriver) {
        super(webDriver);
    }

    /*
     * The following text: with tick/cross images, ticks turn text into links...
     * - Risk of attack outside the home
     * - Risk of attack inside the home
     * - Placing self in danger – ie places, abuse alcohol/drugs
     * - Getting home safely after evenings out
     * - Aggression leading to being attacked
     * - Accidental injury – road/falls/work related
     * - Risk of hate crime
     */
}
