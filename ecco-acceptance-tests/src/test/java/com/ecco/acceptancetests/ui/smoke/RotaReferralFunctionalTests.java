package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.acceptancetests.ui.DisableOnCI;
import com.ecco.acceptancetests.ui.pages.Role;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaAppointmentViewModel;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.ecco.webApi.evidence.ReferralSummaryViewModel;
import com.ecco.webApi.rota.AppointmentActionCommandDto;
import kotlin.Pair;
import org.hamcrest.Description;
import org.hamcrest.TypeSafeMatcher;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import java.time.LocalDate;
import java.util.List;

import static org.hamcrest.Matchers.iterableWithSize;
import static org.junit.Assert.*;

/**
 * Rota endToEnd test for appointments to workers (UI tests).
 * <ol>
 *     <li>create user + worker and link (or link existing user if being clever)</li>
 *     <li>create availability</li>
 *     <li>create client</li>
 *     <li>create appointment</li>
 *     <li>create worker availability</li>
 *     <li>check can assign appt to worker</li>
 * </ol>
 */
public class RotaReferralFunctionalTests extends BaseSeleniumTest {

    protected final DateTime now = new DateTime();

    protected static TypeSafeMatcher<HttpStatus> isSuccessful() {
        return new TypeSafeMatcher<>() {
            @Override
            protected boolean matchesSafely(HttpStatus item) {
                return item.is2xxSuccessful();
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("is 2xx successful");
            }
        };
    }

    @Test
    @DisableOnCI("avoid multiple rota tests for the moment - they are long")
    public void endToEndTest() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        String username = userManagementSteps.createUser("rr1-user1", Role.staff);
        final String workerName = rotaSteps.createWorkerAndJobWithAvailabilityForToday(username, null).getName();
        final Pair<Long, String> referralPair = rotaSteps.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(username, LocalDate.now().atTime(22, 0), null);
        String clientName = referralPair.getSecond();
        rotaSteps.checkCanAssignResourceToFirstAppointmentToday("workers:all", "referrals:all", clientName, workerName);
        loginSteps.logout();
        logoutApi();

        loginSteps.login("rr1-user1");
        loginAsUserApi("rr1-user1");
        rotaSteps.checkClientReferralIsAvailableOffline(clientName);
        loginSteps.logout();
        logoutApi();

        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        ReferralSummaryViewModel svm = referralActor.getReferralSummaryById(referralPair.getFirst()).getBody();
        checkCannotDeleteAppointment(svm.serviceRecipientId);
        checkCanUnassignAppointmentFromWorker(clientName, workerName);
        checkCanDeleteAppointment(svm.serviceRecipientId);

        rotaSteps.checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(clientName, svm.serviceRecipientId, LocalDate.now().atTime(22, 0), null);

        loginSteps.logout();
        logoutApi();
    }


    protected void checkCanUnassignAppointmentFromWorker(String recipientName, String workerName) {
        // TODO: replace with UI interaction when it's finalised to avoid this test getting too fragile now
        Rota rota = rotaActor.getWholeOrgRotaOnDate(now);

        RotaResourceViewModel workerJob = rota.findResourceByName(workerName);
        assertNotNull("Expected worker with name '" + workerName, workerJob);

        List<RotaAppointmentViewModel> appointments = workerJob.findAppointmentsByServiceRecipientName(recipientName);
        assertThat("Expect appointment returned", appointments, iterableWithSize(1));

        // deallocate
        AppointmentActionCommandDto action = new AppointmentActionCommandDto(AppointmentActionCommandDto.OPERATION_DEALLOCATE,
                appointments.get(0).getRef(), appointments.get(0).getServiceRecipientId(), "workers:all", "referrals:all");
        action.deallocateResourceId = workerJob.getResourceId().intValue();
        commandActor.executeCommand(action);

        // Fetch the rota again and check the appointment is shown as unallocated
        rota = rotaActor.getWholeOrgRotaOnDate(now);
        List<RotaAppointmentViewModel> activities = rota.findDemandByServiceRecipientName(recipientName);
        // We have 2 activities due to having created the ad-hoc one
        assertThat("Expect unassigned appointments returned", activities, iterableWithSize(1));
    }

    protected void checkCannotDeleteAppointment(int serviceRecipientId) {
        try {
            checkResultOfDeletingAppointment(serviceRecipientId);
            fail("exception expected");
        } catch (IllegalStateException e) {
            // do nothing - this is good
        }
    }

    protected void checkCanDeleteAppointment(int serviceRecipientId) {
        checkResultOfDeletingAppointment(serviceRecipientId);
    }

    void checkResultOfDeletingAppointment(int serviceRecipientId) {
        var schedules = agreementActor.getAppointmentSchedulesFromAgreement(serviceRecipientId, now);
        agreementActor.deleteSchedule(schedules.getBody().get(0), serviceRecipientId);

        // Check we can subsequently load the rota
//   TODO: Reinstate this and fix issue:
//     rotaActor.getRotaForStartDate(now());
    }

}
