package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.ui.BaseSeleniumTest;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Test things off the settings menu.
 *
 * @since 14/10/2014
 */
public class SettingsTests extends BaseSeleniumTest {

    @Test
    @Disabled("relies on UploadPage which uses sendKeys to type a file path - need to disable JQ plugin to allow that to happen for test")
    public void whenLogoUploadedThenSavedSuccessfully() throws Exception {
        loginSteps.loginAsSysadmin();
        settingsSteps.uploadLogo();
        settingsSteps.checkCanSeeText("Logo saved successfully.");
    }
}
