package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class AdminPage extends BasePageObject {

    public static final String URL = "/nav/secure/admin/";
    private static final String LINK_DELETE_REFERRALCLIENT = "(hard) Delete referrals and clients";

    public AdminPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public AdminDeleteReferralClientPage gotoDeleteReferralClientPage() {
        verifyIsCurrentPage();
        clickLink(LINK_DELETE_REFERRALCLIENT);
        return new AdminDeleteReferralClientPage(getWebDriver());
    }

}
