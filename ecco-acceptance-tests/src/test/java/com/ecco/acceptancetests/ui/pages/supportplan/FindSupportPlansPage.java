package com.ecco.acceptancetests.ui.pages.supportplan;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.BasePageObject;
import com.ecco.acceptancetests.ui.pages.referral.ListReferralsPage;

public class FindSupportPlansPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/supportPlanFlow";

    private static final String FIELD_FIRSTNAME = "contact.firstName";
    private static final String FIELD_LASTNAME = "contact.lastName";
    private static final String FIELD_BIRTHDATE_DAY = "birthDate.day";
    private static final String FIELD_BIRTHDATE_MONTH = "birthDate.month";
    private static final String FIELD_BIRTHDATE_YEAR = "birthDate.year";
    private static final String RADIO_FIELD_GENDER = "gender";
    private static final String FIELD_NATIONAL_INSURANCE = "ni";
    private static final String FIELD_NHS_NUMBER = "nhs";
    private static final String FIELD_ECCO_USERNAME = "username";

    private static final String BUTTON_FIND_ID = "_eventId_search";

    public FindSupportPlansPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    /**
     * Most used in tests - find a referral by first name and last name only
     */
    public ListReferralsPage find(String firstName, String lastName) {
        return find(firstName, lastName, null, null, null, null, null, null, null);
    }

    /**
     * Find a referral ready for opening up the Support Plan flow
     */
    public ListReferralsPage find(String firstName, String lastName, String dobDay, String dobMonth, String dobYear,
            String gender, String niNumber, String nhsNumber, String eccoUsername) {
        setField(FIELD_FIRSTNAME, firstName);
        setField(FIELD_LASTNAME, lastName);
        setSelection(FIELD_BIRTHDATE_DAY, dobDay);
        setSelection(FIELD_BIRTHDATE_MONTH, dobMonth);
        setSelection(FIELD_BIRTHDATE_YEAR, dobYear);
        setRadio(RADIO_FIELD_GENDER, gender);
        setField(FIELD_NATIONAL_INSURANCE, niNumber);
        setField(FIELD_NHS_NUMBER, nhsNumber);
        setField(FIELD_ECCO_USERNAME, eccoUsername);
        clickButton(BUTTON_FIND_ID);

        waitWhileElementDisplayed("loading gif never disappeared", By.id("spinning"));
        return new ListReferralsPage(getWebDriver());
    }

}
