package com.ecco.acceptancetests.ui.pages.supportplan;

import org.junit.Assert;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.WelcomePage;

public class SupportPlanClosedPage extends SupportPlanBasePage {

    private static final String LINK_TEXT_MENU = "menu";
    private static final String TEXT_CLOSED = "support plan closed";

    public SupportPlanClosedPage(WebDriver webDriver) {
        super(webDriver);
    }

    public void checkPlanClosed() {
        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
        Assert.assertTrue("Close text not displayed correctly on page", bodyText.getText().contains(TEXT_CLOSED));
    }

    public WelcomePage backToTheMenu() {
        clickLink(LINK_TEXT_MENU);
        return new WelcomePage(getWebDriver());
    }
}
