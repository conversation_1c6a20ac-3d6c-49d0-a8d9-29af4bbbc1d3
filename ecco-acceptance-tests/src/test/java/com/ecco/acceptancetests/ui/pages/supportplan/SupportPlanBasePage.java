package com.ecco.acceptancetests.ui.pages.supportplan;

import static org.junit.Assert.assertEquals;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.referral.ReferralViewPage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SupportPlanBasePage extends EvidenceBasePage {

    private static final String URL = "/dynamic/secure/supportPlanFlow";

    // Links in the main page header
    private static final String LINK_APPOINTMENTS = "appointments";
    private static final String LINK_ATTACHMENTS = "attachments";
    private static final String LINK_CONTACT_DETAILS = "contact details";
    private static final String LINK_OVERVIEW = "overview";

    // Tabs
    public static final String TAB_HREF_BASE = "#tab_";
    public static final String TAB_OUTCOME_AF = "abuse free";
    public static final String TAB_OUTCOME_BH = "be healthy";
    public static final String TAB_OUTCOME_EW = "economic wellbeing";
    public static final String TAB_OUTCOME_UT = "use of time";

    public SupportPlanBasePage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public SupportPlanBasePage(String pageURL, WebDriver webDriver) {
        super(pageURL, webDriver);
    }

    public void checkReferralName(String expected) {
        assertEquals("Referral name not correct", expected, getWebDriver().findElement(By.id(ReferralViewPage.SID_CLIENT_NAME)).getText());
    }

    // ***** HEADER LINKS *****

    public void clickNameBack(String clientName) {
        clickLink(clientName);
    }

    public void appointments() {
        clickLink(LINK_APPOINTMENTS);
    }

    public void attachments() {
        clickLink(LINK_ATTACHMENTS);
    }

    public void contactDetails() {
        clickLink(LINK_CONTACT_DETAILS);
    }

    public void overview() {
        clickLink(LINK_OVERVIEW);
    }

    /*
    public CloseSupportPlanPage closeOff() {
        //clickLink(LINK_CLOSE_OFF);
        return new CloseSupportPlanPage(getWebDriver());
    }
    */


    // ***** TABS *****

    /**
     * Return a list of all tabs on the Support Plan page
     */
    public List<String> getAvailableTabs() {
        String tabsPath = "//div[@id='evidence-container']//ul[contains(@class, 'nav-tabs')]/li/a";
        List<WebElement> links = getWebDriver().findElements(By.xpath(tabsPath)); // By.tagName("a"));
        List<String> tabs = links.stream()
                .filter(link -> link.getAttribute("href").contains(TAB_HREF_BASE))
                .map(WebElement::getText)
                .collect(Collectors.toList());
        return tabs;
    }

    public WebElement xpathWithinActiveBootstrapTab(String xpath) {
        String activeTabPath = "//div[@id='evidence-container']//li[contains(@class, 'active')]/a";
        WebElement we = getWebDriver().findElement(By.xpath(activeTabPath));
        String fragmentId = StringUtils.substringAfter(we.getAttribute("href"), "#");
        we = findElementSoon(By.xpath("//div[@id='" + fragmentId + "']//descendant::" + xpath));
        return we;
    }

    public void outcome(String outcomeText) {
        clickLinkPartial(outcomeText);
    }

    private String getXPathForSmartStep(String action) {
        return "//span[contains(@class, 'actionDefName') and text() = '" + action + "']/ancestor::div[@class='action-instance']";
    }

    private String getXPathForSmartStepIcon(String action, int icon) {
        return this.getXPathForSmartStep(action).concat("/div/div/div[2]/div/div/span["+icon+"]");
    }

    public void clickStatus(String action) {
        // used firebug and firepath to help get the expression
        String path = this.getXPathForSmartStepIcon(action, 1);
        try {
            clickElementXpath(path);
        } catch (WebDriverException e) {
            throw new WebDriverException("Could not click status (see caused by): action = " + action, e);
        }
    }

    public void clickLinkAction(String action) {
        String path = this.getXPathForSmartStepIcon(action, 2);
        try {
            clickElementXpath(path);
        } catch (WebDriverException e) {
            throw new WebDriverException("Could not click link (see caused by): action = " + action, e);
        }
    }

    public void clickTargetDateIcon(String action) {
        String path = this.getXPathForSmartStepIcon(action, 3);;
        try {
            clickElementXpath(path);
        } catch (WebDriverException e) {
            throw new WebDriverException("Could not click link (see caused by): action = " + action, e);
        }
    }

    public void setTarget(String action, Date target) {
        try {
            clickTargetDateIcon(action);
        }
        catch (Exception e) {
            log.warn("Ignoring exception on target date click: {}", e.getMessage());
        }
        String path = this.getXPathForSmartStep(action).concat("//input[@placeholder='target date']");
        try {
            setDateXpath(path, target);
        } catch (WebDriverException e) {
            throw new WebDriverException("Could not set date (see caused by): action = " + action, e);
        }
    }

    //    public void linkAction(String outcomeHref, String action) {
//        clickLinkHref(outcomeHref);
//    }

    /*
    public void economicWellbeing() {
        clickLinkHref(TAB_HREF_OUTCOME1);
    }

    public void enjoyAndAchieve() {
        clickLinkHref(TAB_HREF_OUTCOME2);
    }

    public void beHealthy() {
        clickLinkHref(TAB_HREF_OUTCOME3);
    }

    public void staySafe() {
        clickLinkHref(TAB_HREF_OUTCOME4);
    }

    public void positiveContribution() {
        clickLinkHref(TAB_HREF_OUTCOME5);
    }
    */

}
