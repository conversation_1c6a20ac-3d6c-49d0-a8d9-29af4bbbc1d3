package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

public class PageFooter {

    private volatile WebDriver webDriver;

    private static final String LINK_TERMS = "terms and conditions";
    private static final String LINK_PRIVACY = "privacy policy";
    private static final String LINK_ACCEPTABLE_USE = "acceptable use policy";

    public PageFooter(WebDriver webDriver) {
        this.webDriver = webDriver;
    }

    public void gotoTermsAndConditions() {
        gotoPage(LINK_TERMS);
    }

    public void gotoPrivacyPolicy() {
        gotoPage(LINK_PRIVACY);
    }

    public void gotoAcceptableUsePolicy() {
        gotoPage(LINK_ACCEPTABLE_USE);
    }

    private void gotoPage(String linkText) {
        webDriver.findElement(By.linkText(linkText)).click();
    }

}
