package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.api.rota.RotaStepsWebApi;
import com.ecco.acceptancetests.steps.HrSteps;
import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.acceptancetests.steps.RotaSteps;
import com.ecco.acceptancetests.ui.steps.webdriver.WebDriverUI;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.actors.*;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.viewModels.Result;
import kotlin.Pair;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.openqa.selenium.WebDriver;
import org.springframework.web.client.RestTemplate;
import reactor.util.function.Tuple2;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;

public class RotaStepsWebDriver extends WebDriverUI implements RotaSteps {

    private final WorkerActor workerActor;
    private final HrSteps hrSteps;
    private final ReferralSteps referralSteps;
    private final RotaStepsWebApi rotaStepsAPI;

    public RotaStepsWebDriver(WebDriver webDriver, RestTemplate restTemplate, SessionDataActor sessionDataActor, WorkerActor workerActor,
                                 AgreementActor agreementActor, ReferralActor referralActor, CalendarActor calendarActor,
                                 BuildingActor buildingActor, RotaActor rotaActor,
                                 ServiceRecipientActor serviceRecipientActor, ServiceActor serviceActor,
                                 HrSteps hrSteps, ReferralSteps referralSteps) {
        super(webDriver);
        this.hrSteps = hrSteps;
        this.referralSteps = referralSteps;
        this.workerActor = workerActor;
        this.rotaStepsAPI = new RotaStepsWebApi(restTemplate, sessionDataActor, workerActor, agreementActor, referralActor, calendarActor,
                buildingActor, rotaActor, serviceRecipientActor, serviceActor, referralSteps);
    }

    @NonNull
    @Override
    public WorkerResult createWorkerAndJob(@NonNull String username) {
        return rotaStepsAPI.createWorkerAndJob(username);
    }

    @NonNull
    @Override
    public WorkerResult createWorkerAndJobWithAvailabilityForToday(@NonNull final String username, final Integer workerPrimaryLocationBuildingId) {
        Tuple2<Long, String> worker = hrSteps.createWorker(username);
        int workerJobId = hrSteps.createWorkerJob(worker.getT1(), LocalDate.now());
        var workerCalendarId = workerActor.getWorker(worker.getT1()).getBody().getCalendarId();
        hrSteps.linkWorkerToUser(worker.getT2(), username);
        var workerJob = workerActor.getWorkerJobId(workerJobId).getBody();
        assert workerJob != null;
        var workerResult = new WorkerResult(worker.getT1(), worker.getT2(), workerJobId, workerCalendarId, workerJob.getServiceRecipient().serviceRecipientId);
        return createWorkerAvailabilityForToday(workerResult, workerPrimaryLocationBuildingId);
    }
    private WorkerResult createWorkerAvailabilityForToday(WorkerResult worker, final Integer workerPrimaryLocationBuildingId) {
        if (workerPrimaryLocationBuildingId != null) {
            setWorkerPrimaryLocation(worker.getWorkerId().intValue(), worker.getWorkerJobId(), workerPrimaryLocationBuildingId);
        }
        String calendarId = workerActor.findWorkerCalendarId(worker.getWorkerId());
        rotaStepsAPI.createAvailabilityForToday(calendarId);
        return worker;
    }

    @NonNull
    @Override
    public Pair<Long, String> createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(@NonNull String appointmentTypeName, @NonNull LocalDateTime time, @Nullable Integer srId) {
        ReferralOptions options = new ReferralOptions().requiresProjects(false)
                .requiresDataProtection(true)
                .requiresEmergencyDetails(true);
//                .withServiceAgreement(agreementAppointmentType);
        Pair<Long, String> result = referralSteps.processReferral(options, DEMO_ALL, "FirstName", "LastName", "AB123456C", "this is a new referral comment 1");
        rotaStepsAPI.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(appointmentTypeName, result, time, srId);
        return result;
    }

    @Override
    public long createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(@NonNull String appointmentTypeName, @NonNull Pair<Long, String> referralResult, @NonNull LocalDateTime time, Integer srId) {
        return rotaStepsAPI.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(appointmentTypeName, referralResult, time, srId);
    }

    @Override
    public void setWorkerPrimaryLocation(long workerId, int workerJobId, int primaryLocationId) {
        rotaStepsAPI.setWorkerPrimaryLocation(workerId, workerJobId, primaryLocationId);
    }

    @Override
    public void checkCanAssignResourceToFirstAppointmentToday(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter, @NonNull String recipientName, @NonNull String resourceName) {
        rotaStepsAPI.checkCanAssignResourceToFirstAppointmentToday(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName);
    }

    @NonNull
    @Override
    public Result checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(@NonNull String serviceRecipientName, int serviceRecipientId, LocalDateTime time, Integer resourceSrId) {
        return rotaStepsAPI.checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(serviceRecipientName, serviceRecipientId, time, resourceSrId);
    }

    @NonNull
    @Override
    public Result checkCanCreateScheduleOnFirstAgreement(@NonNull String serviceRecipientName, int serviceRecipientId, @Nullable LocalDateTime startDateTime, @Nullable DaysOfWeek daysOfWeek, LocalDate endDate, int additionalStaff, Integer resourceSrId) {
        return rotaStepsAPI.checkCanCreateScheduleOnFirstAgreement(serviceRecipientName, serviceRecipientId, startDateTime, daysOfWeek, endDate, additionalStaff, resourceSrId);
    }

    @Override
    public void checkClientReferralIsAvailableOffline(@NonNull String clientName) {
        rotaStepsAPI.checkClientReferralIsAvailableOffline(clientName);
    }

    @NonNull
    @Override
    public FixedContainerViewModel createBuilding(@NonNull String buildingName) {
        return rotaStepsAPI.createBuilding(buildingName);
    }

    @NonNull
    @Override
    public FixedContainerViewModel createBuilding(@NonNull String buildingName, @NonNull FixedContainerViewModel parentBuilding) {
        return rotaStepsAPI.createBuilding(buildingName, parentBuilding);
    }

    @NonNull
    @Override
    public FixedContainerViewModel createCareRun(@NonNull String careRunName, @NonNull FixedContainerViewModel parentBuilding) {
        return rotaStepsAPI.createCareRun(careRunName, parentBuilding);
    }

    @Override
    public void createBuildingAgreement(@NonNull FixedContainerViewModel building) {
        rotaStepsAPI.createBuildingAgreement(building);
    }

    @Override
    public void checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(@NonNull FixedContainerViewModel building, @NonNull LocalDateTime time) {
        rotaStepsAPI.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(building, time);
    }

    @Override
    public void createBuildingAvailabilityFromToday(int buildingId) {
        rotaStepsAPI.createBuildingAvailabilityFromToday(buildingId);
    }

    @Override
    public void checkCanAssignResourceToSingleAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter, @NonNull String recipientName, @NonNull String resourceName, @NonNull LocalDateTime time) {
        rotaStepsAPI.checkCanAssignResourceToSingleAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time);
    }

    @Override
    public void checkCanUnAssignResourceFromSingleAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter, @NonNull String recipientName, @NonNull String resourceName, @NonNull LocalDateTime time) {
        rotaStepsAPI.checkCanUnAssignResourceFromSingleAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time);
    }

    @Override
    public void checkCanAssignResourceToRecurringAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter, @NonNull String recipientName, @NonNull String resourceName, @NonNull LocalDateTime time, @NonNull DaysOfWeek daysOfWeek, @NonNull LocalDate recurringEnd) {
        rotaStepsAPI.checkCanAssignResourceToRecurringAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, daysOfWeek, recurringEnd);
    }

    @Override
    public void checkCanUnAssignResourceFromRecurringAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter, @NonNull String recipientName, @NonNull String resourceName, @NonNull LocalDateTime time, @NonNull DaysOfWeek daysOfWeek, @NonNull LocalDate recurringEnd) {
        rotaStepsAPI.checkCanUnAssignResourceFromRecurringAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, daysOfWeek, recurringEnd);
    }

    @Override
    public void createAppointmentTypeForBuilding(@NonNull String appointmentTypeName) {
        rotaStepsAPI.createAppointmentTypeForBuilding(appointmentTypeName);
    }

    @Override
    public void checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreementOfTypeAtTime(@NonNull FixedContainerViewModel building, @NonNull String appointmentTypeName, @NonNull LocalDateTime time) {
        rotaStepsAPI.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreementOfTypeAtTime(building, appointmentTypeName, time);
    }

    @NonNull
    @Override
    public String createWorkerAndJobWithAvailabilityForToday(@NonNull String username, @NonNull String firstName, @NonNull String lastName, @Nullable Integer workerPrimaryLocationBuildingId) {
        return rotaStepsAPI.createWorkerAndJobWithAvailabilityForToday(username, firstName, lastName, workerPrimaryLocationBuildingId);
    }
}
