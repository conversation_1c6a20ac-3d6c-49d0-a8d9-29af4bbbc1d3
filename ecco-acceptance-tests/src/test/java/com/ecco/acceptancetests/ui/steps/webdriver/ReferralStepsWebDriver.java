package com.ecco.acceptancetests.ui.steps.webdriver;

import static com.ecco.acceptancetests.ui.pages.referral.FindReferralsPage.ExistingClientOutcome.MATCH;
import static com.ecco.acceptancetests.ui.pages.referral.FindReferralsPage.ExistingClientOutcome.NO_MATCH;
import static com.ecco.acceptancetests.ui.pages.referral.FindReferralsPage.ExistingClientOutcome.PROBLEM;
import static org.springframework.util.Assert.hasText;

import com.ecco.acceptancetests.steps.CommonSteps;
import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.acceptancetests.ui.pages.EccoBasePage;
import com.ecco.acceptancetests.ui.pages.NewReferralWizardPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;
import com.ecco.acceptancetests.ui.pages.referral.*;
import com.ecco.acceptancetests.ui.pages.referral.FindReferralsPage.ExistingClientOutcome;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.ServiceOptions;
import com.ecco.webApi.evidence.ReferralViewModel;
import java.util.List;

import kotlin.Pair;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.junit.Assert;
import org.openqa.selenium.WebDriver;

public class ReferralStepsWebDriver extends WebDriverUI implements ReferralSteps, CommonSteps {

    private TaskDefinitionsService1 referralPage = new TaskDefinitionsService1(webDriver);

    ReferralStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @Override
    public void findClientThenReferral(@NonNull String clientCode, @NonNull String firstNamePrefix, @NonNull String lastNamePrefix, @NonNull String referralCode) {

        navigateToWelcome();
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        FindReferralsPage checkClientPage = welcomePage.gotoFindReferrals();
        checkClientPage.find(clientCode, firstNamePrefix, lastNamePrefix, null, null, null, null, null, null, null);

        // could be a list of clients, or a list of the client referrals
        // so we can't use the same logic as FindSupportPlansPage which returns ListReferralsPage
        // but we can assume it for now
        // we expect to match the client and have a list of referrals
        checkCanSeeText("matched a known client: review their referrals (use 'referrals -> new' to add a referral to this client");
        ListReferralsPage listPage = new ListReferralsPage(webDriver);
        if (StringUtils.isNotEmpty(referralCode)) {
            listPage.openReferral(referralCode);
        } else {
            listPage.openReferral(firstNamePrefix, lastNamePrefix);
        }
    }

    @Override
    public long processReferralToId(@NonNull ReferralOptions options, @NonNull ServiceOptions service, @NonNull String firstName, @NonNull String lastName,
                                    @NonNull String niNumber, @NonNull String referralComment) {
        return processReferralToViewModel(options, service, firstName, lastName, niNumber, referralComment).referralId;
    }

    @NonNull
    @Override
    public Pair<Long, String> processReferral(@NonNull ReferralOptions options, @NonNull ServiceOptions service, @NonNull String firstName, @NonNull String lastName,
                                              @NonNull String niNumber, @NonNull String referralComment) {
        ReferralViewModel rvm = processReferralToViewModel(options, service, firstName, lastName, niNumber, referralComment);
        return new Pair<>(rvm.referralId, rvm.getClientDisplayName());
    }

    /**
     * @return part implemented view model
     */
    private ReferralViewModel processReferralToViewModel(ReferralOptions options, ServiceOptions service, String firstName, String lastName,
                                                         String niNumber, String referralComment) {
        // only part of the view model is applied
        ReferralViewModel rvmPart = createReferral(true, false, false, null,
                options, service, firstName, lastName, null, niNumber, "male", "English", "Missing");
        editTaskDefinitions(service, options);
//  TODO: Reinstate this at some point?      addReferralComment(referralComment);
        // uploadFile(fileName);
        // emergency details...
        return rvmPart;
    }

    @Override
    public void processTaskDefinitions(@NonNull ReferralOptions options, @NonNull ServiceOptions service) {
        editTaskDefinitions(service, options);
    }


    /**
     * The essential steps to creating a referral (no processing)
     * @param unique Whether to ensure this is a unique referral (guaranteed to be created)
     * @return a minimial ReferralViewModel - only the id is populated currently, but seems useful to have the potential than just return an id
     */
    @NonNull
    @Override
    public ReferralViewModel createReferral(boolean unique, boolean existingClientExpected, boolean useExistingClient,
                                            @NonNull String expectedReferralClientName, @NonNull ReferralOptions options, @NonNull ServiceOptions serviceName,
                                            @NonNull String firstName, @NonNull String lastName, @NonNull LocalDate dob, @NonNull String niNumber, @NonNull String gender, @NonNull String language, @NonNull String ethnicity) {

        if (dob == null) {
            dob = LocalDate.now();
        }

        // Unique-ify the entered names :-)
        if (unique) {
            firstName = firstName.concat("-" + System.currentTimeMillis());
            lastName = lastName.concat("-" + System.currentTimeMillis());
        }

        createReferralUsingBootstrapUI(existingClientExpected, useExistingClient, expectedReferralClientName, options,
                serviceName, firstName, lastName, dob, niNumber, gender, language, ethnicity);

        referralPage.verifyIsCurrentPage();
        String verifyReferralName = firstName + " " + lastName;
        if (StringUtils.isNotBlank(expectedReferralClientName)) {
            verifyReferralName = expectedReferralClientName;
        }
        referralPage.checkReferralName(verifyReferralName);

        // visibility of ReferralToViewModel prevents us loading and converting to a full model (but we only really need to see if this approach works out)
        long rid = referralPage.getReferralId();
        ReferralViewModel rvm = new ReferralViewModel();
        rvm.setReferralId(rid);
        rvm.setClientDisplayName(firstName + " " + lastName);
        rvm.setClientFirstName(firstName);
        rvm.setClientLastName(lastName);
        return rvm;
    }

    private void createReferralUsingBootstrapUI(boolean existingClientExpected, boolean useExistingClient,
            String expectedReferralClientName, ReferralOptions options, ServiceOptions service,
            String firstName, String lastName, LocalDate dob, String niNumber, String gender, String language,
            String ethnicity) {
        NewReferralWizardPage wizard = new NewReferralWizardPage(webDriver);
        wizard.navigateAsEntryPoint();

        wizard.assertServiceStep();
        wizard.selectService(service);

        if (options.requiresProjects()) {
            wizard.assertProjectStep();
            wizard.selectFirstProject();
        }

        wizard.assertClientStep();
        wizard.searchClient(firstName, lastName);

        // see if the find above brought us to a matching client - do we want to carry on as a new client
        // "there are matches" or straight through to new client screen
        ExistingClientOutcome existingClient = wizard.hasExistingClients(existingClientExpected);

        // I got 99 problems but the unavailability of external client sources ain't one: sorry, Kanye.
        // i.e. if there's a problem with external data sources during a test, just use a new client.
        if (existingClient == PROBLEM
                || existingClient == NO_MATCH
                || (existingClient == MATCH && !useExistingClient)) {
            wizard.navigateNewClient();
        } else if (existingClient == MATCH) { // and therefore useExistingClient = true
            hasText(expectedReferralClientName);
            wizard.useExistingClient(expectedReferralClientName);

            // see if we are a new referral (seeing the client) or if we have an existing match
            // "matched a known client: check your referral hasn't already been processed"
            boolean existingReferral = wizard.hasExistingReferrals(existingClientExpected);
            // if existing, continue new referral is assumed - since that is the method name
            if (existingReferral) {
                wizard.navigateNewReferral();
            }
        }

        // now have client modal showing
        ClientModalPage clientModalPage = new ClientModalPage(webDriver);
        clientModalPage.assertPage();

        // verify the message if we are new client
        if (existingClient != MATCH) {
//        TODO: We don't hav a message    wizard.verifyIsCurrentPageNewClient();
        }

        clientModalPage.saveClient(firstName, lastName, dob, gender,"Flat 1, 23 Steep Hill Road", "AA1 1AA");

        if (existingClientExpected && useExistingClient) {
            wizard.assertReferralStep();
            wizard.navigateNewReferral();
        }

        wizard.assertSourceStep();
        wizard.selectSelfReferral();

        wizard.assertFinalStep();
        wizard.finish();
    }

    private void editTaskDefinitions(ServiceOptions service, ReferralOptions options) {
        if (service.hasReferralOverviewTab()) {
            referralPage.selectFlowTab();
        }

        if (options.requiresDataProtection()) {
            editTaskDefinitionDataProtection(options);
        }
        if (options.requiresEmergencyDetails()) {
            editTaskDefinitionEmergencyDetails(options);
        }
        if (options.requiresSource()) {
            editTaskDefinitionSource(options);
        }
        if (options.requiresDeliveredBy()) {
            editTaskDefinitionDeliveredBy(options);
        }
        if (options.requiresProjects() && !referralPage.isDetailsEnabled()) { // we must still need to select a project
            editTaskDefinitionDestination(options);
        }

        editTaskDefinitionDetails(options);
        editTaskDefinitionPendingStatus(options);
        editTaskDefinitionDecideReferral(options);
        if (options.requiresAccommodation()) {
            editTaskDefinitionAccommodation(options);
        }
        editTaskDefinitionSetupAssessment(options);
        if (options.requiresFunding()) {
            editTaskDefinitionFunding(options);
        }
        editTaskDefinitionDecideService(options);
        editTaskDefinitionStart(options);
        if (options.withServiceAgreement()) {
            editTaskDefinitionServiceAgreement(options);
        }
        if (options.requiresSupportStaffNotes()) {
            editReferralSupportStaffNotes(options);
        }
        if (options.requiresGeneralQuestionnaireOutcomeStar()) {
            editTaskDefinitionGeneralQuestionnaireOutcomeStar(options);
        }
        if (options.hasExitedDate()) {
            editExitedDate(options);
        }
    }

    private void addReferralComment(String comment) {
        referralPage.comments();
        CommentsPage cPage = new CommentsPage(referralPage, webDriver);
        cPage.comment(comment);
        cPage.save();
        referralPage.verifyIsCurrentPage();
    }

    @Override
    public void printReferralOverview(@NonNull String firstName, @NonNull String lastName) {
        findClientThenReferral(null, firstName, lastName, null);
        referralPage.printable();
        //waitForPageLoaded();
        // suggested approach to viewing a second tab: http://stackoverflow.com/questions/12729265/switch-tabs-using-selenium-webdriver
        //new Actions(webDriver).sendKeys(webDriver.findElement(By.tagName("html")), Keys.CONTROL).sendKeys(webDriver.findElement(By.tagName("html")),Keys.NUMPAD2).build().perform();
        // however it actually opens in a new window: http://stackoverflow.com/questions/11220869/selenium-how-can-i-select-new-window
    }

    @Override
    public void navigateToAppointments(@NonNull String firstName, @NonNull String lastName) {
        findClientThenReferral(null, firstName, lastName, null);
        referralPage.appointments();
    }

    @Override
    public void exited(@NonNull String firstName, @NonNull String lastName, @NonNull LocalDate exitedDate) {
        findClientThenReferral(null, firstName, lastName, null);
        editExitedDate(new ReferralOptions().hasExitedDate(exitedDate));
    }

    private void editTaskDefinitionSource(ReferralOptions options) {
        referralPage.source();
        EccoBasePage page = new SourceTypePage("/dynamic/secure/referralAspectFlow", referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionDeliveredBy(ReferralOptions options) {
        referralPage.deliveredBy();
        EccoBasePage page = new DeliveredByPage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionDestination(ReferralOptions options) {
        referralPage.destination();
        EccoBasePage page = new DestinationPage(options, referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionDetails(ReferralOptions options) {
        referralPage.details();
        EccoBasePage page = new DetailsPage(options, referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionDataProtection(ReferralOptions options) {
        referralPage.dataProtectionAspect();
        EccoBasePage page = new DataProtectionPage(options, referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionEmergencyDetails(ReferralOptions options) {
        referralPage.emergencyDetailsAspect();
        EccoBasePage page = new EmergencyDetailsPage(options, referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionPendingStatus(ReferralOptions options) {
        referralPage.pendingStatus();
        EccoBasePage page = new PendingStatusPage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionDecideReferral(ReferralOptions options) {
        referralPage.decideReferral();
        EccoBasePage page = new DecideReferralPage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionAccommodation(ReferralOptions options) {
        // this just presses cancel, because the new 'accommodation' page is currently identical to 'project'
        // this is actually okay! we've moved everyone off this task now!!
        // so we just skip - except now we need to click 'mark done' to go past
        referralPage.accommodation();
        EccoBasePage page = new AccommodationPage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);

        page.markDone("accommodation");

        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionSetupAssessment(ReferralOptions options) {
        referralPage.setupAssessment();
        EccoBasePage page = new SetupAssessmentPage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionFunding(ReferralOptions options) {
        referralPage.funding();
        EccoBasePage page = new FundingPage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionDecideService(ReferralOptions options) {
        referralPage.decideService();
        EccoBasePage page = new DecideServicePage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionStart(ReferralOptions options) {
        referralPage.start();
        EccoBasePage page = new StartPage(referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        page.verifyIsCurrentPage();
    }

    private void editTaskDefinitionServiceAgreement(ReferralOptions options) {
        referralPage.serviceAgreement();
        EccoBasePage page = new ServiceAgreementPage(options, referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        referralPage.verifyIsCurrentPage();
    }

    private void editReferralSupportStaffNotes(ReferralOptions options) {
        referralPage.supportStaffNotes();
        EccoBasePage page = new CaseNotesPage(referralPage, webDriver)
            .defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        referralPage.verifyIsCurrentPage();
    }

    private void editTaskDefinitionGeneralQuestionnaireOutcomeStar(ReferralOptions options) {
        referralPage.generalQuestionnaireOutcomeStar();
        GeneralQuestionnaireOutcomeStarPage page = new GeneralQuestionnaireOutcomeStarPage(referralPage, webDriver);
        page.checkStarCorrect();
        EccoBasePage returnPage = page.defaultAction();
        Assert.assertTrue(returnPage instanceof ReferralViewPage);
        referralPage.verifyIsCurrentPage();
    }

    private void editExitedDate(ReferralOptions options) {
        referralPage.exited();
        EccoBasePage page = new ExitedPage(options, referralPage, webDriver);
        page = page.defaultAction();
        Assert.assertTrue(page instanceof ReferralViewPage);
        referralPage.verifyIsCurrentPage();
    }

    @NonNull
    @Override
    public List<String> listReferrals() {
        // make sure we start on the Welcome page
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        ListReferralsPage listPage = welcomePage.gotoListReferrals();
        List<String> referrals = listPage.getVisibleReferralNames();
        return referrals;
    }

}
