package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.expectations.GroupSupportExpectation;


import com.ecco.acceptancetests.givens.GroupSupportGiven;
import com.ecco.acceptancetests.steps.GroupSupportContext;
import com.ecco.acceptancetests.ui.pages.GroupSupportActivityPage;
import com.ecco.acceptancetests.ui.pages.GroupSupportListPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;
import com.ecco.acceptancetests.ui.smoke.GroupSupportTests;


import org.jspecify.annotations.NonNull;
import org.openqa.selenium.WebDriver;


public class GroupSupportStepsWebDriver extends WebDriverUI implements GroupSupportContext {

    private WelcomePage welcomePage;
    private GroupSupportListPage groupSupportListPage;
    private GroupSupportActivityPage groupSupportActiviyPage;

    public GroupSupportStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
        this.welcomePage = new WelcomePage(webDriver);
        this.groupSupportListPage = new GroupSupportListPage(webDriver);
        this.groupSupportActiviyPage = new GroupSupportActivityPage(webDriver);
    }

    @NonNull
    @Override
    public GroupSupportGiven given(@NonNull GroupSupportTests testFramework) {
        return new GroupSupportGiven(this, testFramework);
    }

    @NonNull
    @Override
    public GroupSupportListPage openGroupSupportList() {
        navigate(GroupSupportListPage.URL);
        return this.groupSupportListPage;
    }

    @NonNull
    @Override
    public GroupSupportActivityPage openFirstGroupSupportActivity() {
        groupSupportListPage.openFirstGroupSupportActivity();
        return groupSupportActiviyPage;
    }

    @NonNull
    @Override
    public GroupSupportActivityPage clickClientsTab() {
        this.groupSupportActiviyPage.clickClientsTab();
        return groupSupportActiviyPage;
    }

    @NonNull
    @Override
    public GroupSupportExpectation expect() {
        return new GroupSupportExpectation(this);
    }

}
