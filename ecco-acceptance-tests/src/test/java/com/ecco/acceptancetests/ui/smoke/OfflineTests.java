package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.expectations.OfflineExpectation;
import com.ecco.acceptancetests.givens.OfflineGiven;
import com.ecco.acceptancetests.steps.OfflineContext;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.ServiceOptions;
import com.ecco.test.support.RetryRule;
import com.ecco.webApi.evidence.ReferralViewModel;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.IfProfileValue;

public class OfflineTests extends BaseSeleniumTest {

    // This is set by .as("referral")
    private final ReferralViewModel referral = null;
    private OfflineContext when;
    private OfflineGiven given;
    private OfflineExpectation expect;

    @BeforeEach
    public void setUp() {
        given = offlineSteps.given(this);
        expect = offlineSteps.expect();
        when = given.when();
    }

    @RetryRule.Retries(1)
    @Test
    @IfProfileValue(name="test.category", values={"all","quarantine"}) // timeout waiting for referrals list
    public void userCanActivateOfflineAndLoginAndSyncAndSeeReferrals() {

        login("sysadmin");

        // given offline is enabled and a referral exists for James Blunt
        given.offlineIsEnabled();
        given.ensureReferral("OT01", "James", "Blunt", ServiceOptions.DEMO_ALL, ReferralOptions.DEMO_ALL).as("referral");

        // when I login offline, synchronise referrals and view the referrals list
        when

        .loginOffline()
        .syncReferrals();
        List<String> names =
                when
                .viewReferrals();

        // i expect the named referral (james blunt) is seen in the offline referrals list
        String clientName= referral.clientDisplayName;
        expect.canSeeText(clientName);

        logout();
    }
}
