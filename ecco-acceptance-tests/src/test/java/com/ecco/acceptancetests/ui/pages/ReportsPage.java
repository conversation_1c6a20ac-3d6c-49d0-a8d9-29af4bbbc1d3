package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public class ReportsPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/menuReport";

    private static final String LINK_USERAUDIT = "login audit";

    public ReportsPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    private void gotoPage(String linkText) {
        clickLink(linkText);
    }

    public void menu() {
        getPageHeader().gotoMenu();
        waitForPageLoaded();
        verifyIsCurrentPage();
    }

    public UserAuditReportPage gotoUserAudit() {
        gotoPage(LINK_USERAUDIT);
        return new UserAuditReportPage(getWebDriver());
    }
}
