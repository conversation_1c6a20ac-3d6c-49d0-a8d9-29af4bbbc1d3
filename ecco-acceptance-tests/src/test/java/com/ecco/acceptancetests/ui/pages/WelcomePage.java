package com.ecco.acceptancetests.ui.pages;

import com.ecco.acceptancetests.ui.pages.hr.HrPage;
import com.ecco.acceptancetests.ui.pages.offline.ManageOfflinePage;
import com.ecco.acceptancetests.ui.pages.offline.ReferralsListPage;
import com.ecco.acceptancetests.ui.pages.referral.FindReferralsPage;
import com.ecco.acceptancetests.ui.pages.referral.ListReferralsPage;
import com.ecco.acceptancetests.ui.pages.referral.ServicePage;
import com.ecco.acceptancetests.ui.pages.supportplan.FindSupportPlansPage;
import org.openqa.selenium.WebDriver;


public class WelcomePage extends EccoBasePage {

	private static final String URL = "/nav/r/welcome/";

	private static final String LINK_LIST = "list by status";
	private static final String LINK_FIND = "find/new";
	private static final String LINK_LIVE_CLIENTS = "my/team live";

	private static final String LINK_NEW = "new referral";
	private static final String LINK_MY_REFERRALS = "my referrals";

	private static final String LINK_SUPPORT_PLANS = "support plans";
	private static final String LINK_CALENDAR = "calendar";
	private static final String LINK_HR = "hr";
	private static final String LINK_OFFLINE = "offline";
	private static final String LINK_SETTINGS_HREF = "#settings";
	private static final String LINK_REPORTS_HREF = "#reports";


	public WelcomePage(WebDriver webDriver) {
		super(URL, webDriver);
	}


	public ListReferralsPage gotoListReferrals() {
		clickMaterialUIMenuItem(LINK_LIST);
		return new ListReferralsPage(getWebDriver());
	}

	public FindReferralsPage gotoFindReferrals() {
		clickLink(LINK_FIND);
		return new FindReferralsPage(getWebDriver());
	}

	public ServicePage followNewReferrals() {
		clickLink(LINK_NEW);
		return new ServicePage(getWebDriver());
	}

	public FindSupportPlansPage gotoSupportPlans() {
		gotoPage(LINK_SUPPORT_PLANS);
		return new FindSupportPlansPage(getWebDriver());
	}

	public CalendarPage gotoCalendar() {
		gotoPage(LINK_CALENDAR);
		return new CalendarPage(getWebDriver());
	}

	public SettingsPage gotoUsersAndSettings() {
		clickMaterialUIMenuItem("users & admin");
		return new SettingsPage(getWebDriver());
	}

	public SettingsPage gotoSettings() {
		clickLinkHref(LINK_SETTINGS_HREF);
		return new SettingsPage(getWebDriver());
	}

	public ReportsPage gotoReports() {
            clickLinkHref(LINK_REPORTS_HREF);
		return new ReportsPage(getWebDriver());
	}

	public HrPage gotoHr() {
		gotoPage(LINK_HR);
		return new HrPage(getWebDriver());
	}

	public ReferralsListPage gotoMyReferrals() {
		gotoPage(LINK_MY_REFERRALS);
		return new ReferralsListPage(getWebDriver());
	}

	public ManageOfflinePage gotoOffline() {
		gotoPage(LINK_OFFLINE);
		return new ManageOfflinePage(getWebDriver());
	}

	private void gotoPage(String linkText) {
		clickLink(linkText);
	}

	public void menu() {
		navigateAsEntryPoint(); // get to a page which has 'menu' on it
		waitForPageLoaded();
		getPageHeader().gotoMenu(); // expects a page with 'menu' on it
		waitForPageLoaded();
		verifyIsCurrentPage();
	}

	@Override
	public EccoBasePage defaultAction() {
		return null;
	}
}
