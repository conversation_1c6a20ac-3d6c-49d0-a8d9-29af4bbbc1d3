package com.ecco.acceptancetests.ui.pages;

import java.util.ArrayList;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.Select;

public class UserManagementPage extends BasePageObject {

    private static final String URL = "/nav/r/settings/users";

    private static final String LINK_ADD_USER = "new user";
    private static final String LINK_USERS_XPATH = "//a[contains(@href, '/dynamic/secure/userManagementFlow.html?id=')]";

    private static final String DROPDOWN_GROUP_NAME = "groupName";
    private static final String DROPDOWN_GROUP_ID = "groupName";

    private static final String BUTTON_ENABLEDONLY_ID = "enabledOnly";
    private static final String BUTTON_ALPHABETPREFIX_ID = "letter-";

    public UserManagementPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public UserDetailsPage gotoAddUserPage() {
        verifyIsCurrentPage();
        clickMaterialUIButton(LINK_ADD_USER);
        return new UserDetailsPage(getWebDriver());
    }

    public UserDetailsPage gotoUserDetails(String username) {
        verifyIsCurrentPage();
        clickLink(username);
        return new UserDetailsPage(getWebDriver());
    }

    public void chooseInitial(String initial) {
        if (initial == null)
            return;
        clickButtonById(BUTTON_ALPHABETPREFIX_ID + initial);
        waitForPageLoaded();
    }

    public void chooseGroup(String group) {
        if (group == null)
            return;
        setSelection(DROPDOWN_GROUP_NAME, group);
        waitForPageLoaded();
    }

    public void chooseEnabledOnly(boolean resultValue) {
        boolean enabledOnly = Boolean.parseBoolean(getWebDriver().findElement(By.id(BUTTON_ENABLEDONLY_ID)).getAttribute("checked"));
        if (enabledOnly == resultValue)
            return;
        clickButtonById(BUTTON_ENABLEDONLY_ID);
        waitForPageLoaded();
    }

    public List<String> getVisibleUsers() {
        List<String> usernames = new ArrayList<>();
        List<WebElement> links = getWebDriver().findElements(By.xpath(LINK_USERS_XPATH));
        for (WebElement link : links) {
            usernames.add(link.getText());
        }
        return usernames;
    }

    public List<String> getVisibleGroups() {
        List<String> groups = new ArrayList<>();
        WebElement webElement = getWebDriver().findElement(By.id(DROPDOWN_GROUP_ID));
        Select selectElement = new Select(webElement);
        for (WebElement option : selectElement.getOptions()) {
            groups.add(option.getText());
        }
        return groups;
    }

}
