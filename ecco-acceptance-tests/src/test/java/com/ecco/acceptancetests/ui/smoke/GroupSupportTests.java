package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.expectations.GroupSupportExpectation;
import com.ecco.acceptancetests.givens.GroupSupportGiven;
import com.ecco.acceptancetests.steps.GroupSupportContext;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.acceptancetests.ui.pages.Role;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class GroupSupportTests extends BaseSeleniumTest {

    private GroupSupportGiven given;
    private GroupSupportContext when;
    private GroupSupportExpectation expect;

    @BeforeEach
    public void setUp() {
        // targetVariable = object.methodOnTheObject(parameter);
        // targetVar = ClassName.staticMethod(params);
        given = groupSupportSteps.given(this);
        when = given.when();
        expect = groupSupportSteps.expect();
    }

    @Test
    public void checkStaffCanSeeGroupSupport() {
        final String staffgs = "staffgs";
        login("sysadmin");
        userManagementSteps.createUser(staffgs, Role.staff);
        loginSteps.logout();

        // check the new user can't see the admin page
        loginSteps.login(staffgs);
        loginSteps.checkCanSeeText(staffgs);

        given.ensureGroupSupportActivity();
        when.openGroupSupportList();
        expect.canSeeText("new group support activity");

        when.openFirstGroupSupportActivity();
        expect.canSeeText("capacity");

        when.clickClientsTab();
        expect.canSeeText("Clients must be 'live' on the selected service to be listed here");

        loginSteps.navigateToWelcome(); // ensure that we are on a page which has 'logout' (admin pages don't currently)
        loginSteps.logout();
    }

}
