package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.UnexpectedTagNameException;


/*

def click(self):
click_element = ActionChains(self.browser)\
.click_and_hold(self.element)\
.release(self.element)
click_element.perform()



@property
def items(self):
self.open()
item_divs = self.dropdown.find_elements_by_css_selector(
'ul.select2-results li div.select2-result-label')
return [div.text for div in item_divs]
*/
public class Select2 {

    private final WebElement element;

//    def __init__(self, element):
//        self.browser = element.parent
//        self.replaced_element = element
//        self.element = browser.find_element_by_id(
//                's2id_{0}'.format(element.get_attribute('id')))
    public Select2(WebElement element) {
        String tagName = element.getTagName();

        if (null == tagName || !"select".equals(tagName.toLowerCase())) {
          throw new UnexpectedTagNameException("select", tagName);
        }
        this.element = element;
    }

//    def open(self):
//        if not self.is_open:
//            self.click()
//
//    def close(self):
//        if self.is_open:
//            self.click()

    public boolean isOpen() {
        return element.getAttribute("class").contains("select2-container--open");
    }

    public void dropDown() {
//        return browser.find_element_by_id('select2-drop')
    }
}
