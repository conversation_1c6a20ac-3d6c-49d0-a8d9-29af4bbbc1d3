package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.WebDriver;

public abstract class EccoBasePage extends BasePageObject {

    protected EccoBasePage(String pageURL, WebDriver webDriver) {
        super(pageURL, webDriver);
    }

    public static final String BUTTON_NEXT_ID = "next";
    public static final String BUTTON_NEXT_INNER_ID = "_eventId_nextInner";
    public static final String BUTTON_SAVE = "save";
    public static final String BUTTON_ACCEPT = "accept";
    public static final String BUTTON_CANCEL = "cancel";
    public static final String BUTTON_REJECT = "_eventId_reject";

    // with aspects, we often want to call an easy default method which returns a new page
    // if we don't use the default then we can expect to create the resulting page ourselves
    public abstract EccoBasePage defaultAction();

    public void markDone(String taskDefinitionName) {
        clickElementByCssSelector("#ra-markdone-"+ taskDefinitionName);
    }

}
