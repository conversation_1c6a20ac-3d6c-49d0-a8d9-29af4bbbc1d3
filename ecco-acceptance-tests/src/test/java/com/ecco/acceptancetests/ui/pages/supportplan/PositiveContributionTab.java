package com.ecco.acceptancetests.ui.pages.supportplan;

import org.openqa.selenium.WebDriver;

public class PositiveContributionTab extends SupportPlanBasePage {

    public PositiveContributionTab(WebDriver webDriver) {
        super(webDriver);
    }

    /*
     * The following text: with tick/cross images, ticks turn text into links...
     * - carry out voluntary work
     * - seek full-time employment
     * - engage in group life skills sessions
     * - attend service user meetings
     * - contribute at discussions held to shape services
     * - contribute at review sessions with support staff
     */
}
