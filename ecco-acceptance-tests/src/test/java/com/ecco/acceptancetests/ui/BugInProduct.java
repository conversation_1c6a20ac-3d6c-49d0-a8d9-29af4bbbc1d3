package com.ecco.acceptancetests.ui;

import org.springframework.test.annotation.IfProfileValue;

import java.lang.annotation.*;

@Documented
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target( { ElementType.TYPE, ElementType.METHOD })


/**
 * Indicates a tests will be disabled unless -Dtest.category=bug or =all is specified.
 * Test required to be run with {@link SpringJUnit4ClassRunner}), otherwise will
 * always run.
 */
@IfProfileValue(name="test.category", values={"all","bug"})
public @interface BugInProduct {

    /**
     * Explain the problem and preferably give the tracking reference to the bug report
     */
    String value();
}
