package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.SettingsSteps;
import com.ecco.acceptancetests.ui.pages.SettingsPage;
import com.ecco.acceptancetests.ui.pages.UploadPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;
import com.google.common.io.Files;
import org.openqa.selenium.WebDriver;

import java.io.File;
import java.io.IOException;

/**
 * @since 14/10/2014
 */
public class SettingsStepsWebDriver extends WebDriverUI implements SettingsSteps {
    // This is a single pixel GIF, apparently
    private static final byte[] IMAGE_DATA = {
            0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x1, 0x0, 0x1, 0x0, -0x80, 0x0, 0x0, -0x1, -0x1, -0x1, 0x0, 0x0, 0x0,
            0x2c, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x1, 0x0, 0x0, 0x2, 0x2, 0x44, 0x1, 0x0, 0x3b };

    public SettingsStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @Override
    public void uploadLogo() throws IOException {
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        final SettingsPage settingsPage = welcomePage.gotoSettings();
        final UploadPage uploadPage = settingsPage.gotoLogo();
        File file = File.createTempFile("logo", ".gif");
        file.deleteOnExit();
        Files.write(IMAGE_DATA, file);
        uploadPage.uploadFile(file);
    }
}
