package com.ecco.acceptancetests.ui.steps.webdriver;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.events.AbstractWebDriverEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DebugLoggingEventListener extends AbstractWebDriverEventListener {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Override
    public void afterClickOn(WebElement element, WebDriver driver) {
        super.afterClickOn(element, driver);
        log("afterClickOn", element);
    }

    @Override
    public void beforeClickOn(WebElement element, WebDriver driver) {
        log("beforeClickOn", element);
        super.beforeClickOn(element, driver);
    }

    private void log(String method, WebElement element) {
        log.debug(method + "({})", element); //.getAttribute("id"));
    }
}
