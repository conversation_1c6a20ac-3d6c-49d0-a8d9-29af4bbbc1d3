package com.ecco.acceptancetests.ui.pages;

import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;

public class PageHeader {

    private volatile WebDriver webDriver;

    private static final String LINK_HOME = "home";
    private static final String LINK_LOGIN = "login";

    private static final String LINK_MENU = "menu";
    private static final String LINK_LOGOUT = "logout";

    public PageHeader(WebDriver webDriver) {
        this.webDriver = webDriver;
    }

    public void logout() {
        webDriver.findElement(By.linkText(LINK_LOGOUT)).click();
    }

    public void gotoHome() {
        gotoPage(LINK_HOME);
    }

    public void gotoLogin() {
        gotoPage(LINK_LOGIN);
    }

    public void gotoMenu() {
        try {
            gotoPage(LINK_MENU);
        } catch (NoSuchElementException e) {
            // assume we're already on welcome page if the menu doesn't exist
        }
    }

    private void gotoPage(String linkText) {
        webDriver.findElement(By.linkText(linkText)).click();
    }
}
