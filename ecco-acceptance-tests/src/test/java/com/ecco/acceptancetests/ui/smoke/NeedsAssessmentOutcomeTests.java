package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.TestUtils;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.test.support.UniqueDataService;
import com.ecco.data.client.model.ReferralAggregate;

import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.IfProfileValue;

import java.util.List;

import static com.ecco.data.client.ServiceOptions.ACCOMMODATION;
import static com.ecco.data.client.ServiceOptions.FLOATING_SUPPORT;

/**
 * Tests for git issue #7
 *
 */
public class NeedsAssessmentOutcomeTests extends BaseSeleniumTest {

    private static final String TEST_NI_NUMBER = "*********";

    private static final String[] EXPECTED_TABS_ACCOMMODATION = {"abuse free", "be healthy", "economic wellbeing", "use of time"};
    private static final String[] EXPECTED_TABS_FLOATING_SUPPORT = {"be healthy", "economic wellbeing", "use of time", "trust and hope"};

    // WIP: This will probably be injected per thread by the factory
    private final UniqueDataService unique = UniqueDataService.instance;

    @Test
    @IfProfileValue(name="test.category", values={"all","bug"})
    public void needsAssessmentsHaveCorrectOutcomesForService() throws Exception {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        // construct a new referral
        // we should probably re-use some ReferralOptions idea in creating the referral
        // TODO use a createFixture
        ReferralAggregate ra = new ReferralAggregate();
        ra.client.setFirstName(unique.clientFirstNameFor("Jane"));
        ra.client.setLastName(unique.clientLastNameFor("Smith"));
        ra.referral.setImportServiceName(ACCOMMODATION.getServiceName());
        LocalDate dte = new LocalDate(2014, 02, 13);
        ra.referral.setDecisionDate(dte.toLocalDateTime(LocalTime.MIDNIGHT));
        ra.referral.setReceivedDate(dte);
        ra.referral.setReceivingServiceDate(dte);
        ra.referral.currentTaskDefinitionIndex = "99"; // LEGACY - referral aspect index to 'end' so that we see all links
        referralActor.submitReferralAggregateImport(ra);
        Assert.assertNotNull("TEST ERROR: creating a referral did not return a referral id", ra.referral.referralId);

        // Open the Support Plan for the referral and check the tabs listed
        List<String> tabs = supportPlanSteps.getNeedsAssessmentTabs(ra.client.firstName, ra.client.lastName);
        TestUtils.listContains(tabs, EXPECTED_TABS_ACCOMMODATION);

        // construct a new referral
        // we should probably re-use some ReferralOptions idea in creating the referral
        // TODO use a createFixture
        ra = new ReferralAggregate();
        ra.client.setFirstName(unique.clientFirstNameFor("Jane"));
        ra.client.setLastName(unique.clientLastNameFor("Smith"));
        ra.referral.setImportServiceName(FLOATING_SUPPORT.getServiceName());
        ra.referral.setDecisionDate(dte.toLocalDateTime(LocalTime.MIDNIGHT));
        ra.referral.setReceivedDate(dte);
        ra.referral.setReceivingServiceDate(dte);
        ra.referral.currentTaskDefinitionIndex = "99"; // LEGACY - referral aspect index to 'end' so that we see all links
        referralActor.submitReferralAggregateImport(ra);
        Assert.assertNotNull("TEST ERROR: creating a referral did not return a referral id", ra.referral.referralId);

        // Open the Support Plan for the second referral and check the tabs listed
        tabs = supportPlanSteps.getNeedsAssessmentTabs(ra.client.firstName, ra.client.lastName);
        TestUtils.listContains(tabs, EXPECTED_TABS_FLOATING_SUPPORT);

        logoutApi();
        loginSteps.logout();
    }
}
