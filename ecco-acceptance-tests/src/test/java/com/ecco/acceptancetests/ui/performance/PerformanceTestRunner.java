package com.ecco.acceptancetests.ui.performance;

import org.eeichinger.testing.web.AbstractPerformanceTestRunner;
import org.eeichinger.testing.web.BrowserSessionFactory;

import java.util.ArrayList;
import java.util.List;

public class PerformanceTestRunner extends AbstractPerformanceTestRunner<StandardUserSession> {

    public static void main(String[] args) throws Exception {
        new PerformanceTestRunner().run(args);
    }

    @Override
    protected List<BrowserSessionFactory<StandardUserSession>> createBrowserProfiles() {
        List<BrowserSessionFactory<StandardUserSession>> browserProfiles = new ArrayList<>();
        browserProfiles.add(new BrowserSessionFactory<>(StandardUserSession.class, 100, 20000));
        return browserProfiles;
    }
}
