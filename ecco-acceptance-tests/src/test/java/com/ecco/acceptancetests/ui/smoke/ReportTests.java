package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.steps.ReportSteps.ReportOptions;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;

import com.ecco.acceptancetests.ui.pages.Role;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

public class ReportTests extends BaseSeleniumTest {

    @Test
    @Disabled("reports page exists, but not HR")
    public void sysadminUserCanSeeHrReport() throws Exception {
        loginSteps.loginAsSysadmin();
        ReportOptions repOptions = new ReportOptions();

        // touching this report could be annoying as it shows the download box
        //reportHrSteps.viewHrSupportActionsReport(repOptions);
        // attempt to clear
        loginSteps.navigateToHome();
    }

    @Test
    public void checkReportUserCanSeeReports() {
        loginSteps.loginAsSysadmin();
        String staffRep = userManagementSteps.createUser("userrep", Role.reports);
        loginSteps.logout();

        // check the new user can't see the admin page
        loginSteps.login("userrep");
        loginSteps.checkCanSeeText(staffRep);

        reportListSteps.openReferralReport();

        loginSteps.navigateToWelcome(); // ensure that we are on a page which has 'logout' (admin pages don't currently)
        loginSteps.logout();
    }

    @Test
    public void checkStaffUserCantSeeReports() {
        loginSteps.loginAsSysadmin();
        String staffRep = userManagementSteps.createUser("userstaff", Role.staff);
        loginSteps.logout();

        // check the new user can't see the admin page
        loginSteps.login("userstaff");
        loginSteps.checkCanSeeText(staffRep);

        try {
            reportListSteps.openReferralReport();
            fail();
        } catch (AssertionError e) {
            // TODO: we should be testing that the user got an error message, possibly even a 4xx error
            assertThat(e.getMessage(), equalTo("Couldn't click link: 'referrals by service, then by project, then by worker, then breakdown'"));
        }

        loginSteps.navigateToWelcome(); // ensure that we are on a page which has 'logout' (admin pages don't currently)
        loginSteps.logout();
    }

}
