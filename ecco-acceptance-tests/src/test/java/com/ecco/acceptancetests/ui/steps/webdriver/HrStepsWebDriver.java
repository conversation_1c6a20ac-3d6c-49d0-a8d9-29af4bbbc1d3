package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.HrSteps;
import com.ecco.data.client.actors.WorkerActor;
import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.ui.pages.WelcomePage;
import com.ecco.acceptancetests.ui.pages.hr.HrPage;
import com.ecco.acceptancetests.ui.pages.hr.LinkUserPage;
import com.ecco.acceptancetests.ui.pages.hr.WorkerEditPage;
import com.ecco.acceptancetests.ui.pages.hr.WorkerOverviewPage;

import org.jspecify.annotations.NonNull;
import org.junit.Assert;
import org.openqa.selenium.WebDriver;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.time.LocalDate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HrStepsWebDriver extends WebDriverUI implements HrSteps {

    private static final Pattern WORKER_ID_PARAM = Pattern.compile("workerId=(\\d+)");
    // WIP: This will probably be injected per thread by the factory
    private final UniqueDataService unique = UniqueDataService.instance;
    private final WorkerActor workerActor;

    public HrStepsWebDriver(WebDriver webDriver, WorkerActor workerActor) {
        super(webDriver);
        this.workerActor = workerActor;
    }

    @NonNull
    @Override
    public Tuple2<Long, String> createWorker(@NonNull String workerName) {
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        HrPage hrPage = welcomePage.gotoHr();
        WorkerEditPage workerEditPage = hrPage.gotoAddWorkerPage();
        hrPage = workerEditPage.createNewWorker(unique.idFor(workerName), unique.firstNameFor(workerName), unique.lastNameFor(workerName));
        final Matcher matcher = WORKER_ID_PARAM.matcher(webDriver.getCurrentUrl());
        if (!matcher.find()) {
            Assert.fail("No workerId parameter in current URL after creating worker");
        }
        return Tuples.of(Long.valueOf(matcher.group(1)), unique.firstNameFor(workerName) + ' ' + unique.lastNameFor(workerName));
    }

    @Override
    public int createWorkerJob(long workerId, @NonNull LocalDate start) {
        return workerActor.createWorkerJob(workerId, start);
    }

    @Override
    public void linkWorkerToUser(@NonNull String workerName, @NonNull String username) {
        WorkerOverviewPage workerOverviewPage = new WorkerOverviewPage(webDriver);
        LinkUserPage linkUserPage = workerOverviewPage.linkedUser();
        linkUserPage.linkUser(username);
        workerOverviewPage.verifyIsCurrentPage();
        checkCanSeeText(username);
    }

}
