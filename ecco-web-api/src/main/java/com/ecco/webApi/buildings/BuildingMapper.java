package com.ecco.webApi.buildings;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.dto.AddedRemovedDto;
import com.ecco.infrastructure.commands.CommandMappings;
import org.mapstruct.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BuildingMapper extends CommandMappings {

    /**
     * Updates a FixedContainer entity from a BuildingCommandViewModel.
     * This method only maps fields that are present in the ViewModel (not null).
     *
     * @param viewModel The source ViewModel with changes
     * @param building The target FixedContainer entity to update
     */
    @Mapping(target = "resourceTypeId", source = "resourceType")
    @Mapping(target = "locationId", source = "location")
    @Mapping(target = "chargeCategoryCombinations", ignore = true) // Handled by @AfterMapping
    @Mapping(target = "id", ignore = true) // Don't modify the ID
    @Mapping(target = "collectionId", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "parent", ignore = true) // Parent is handled separately via parentId
    @Mapping(target = "parentId", source = "parentId")
    @Mapping(target = "nearestChargeCategoryCombinations", ignore = true) // This is a computed property
    @Mapping(target = "resourceType", ignore = true) // Resource type is handled by resourceTypeId
    @Mapping(target = "calendarId", ignore=true)
    @Mapping(target = "withName", ignore=true)
    @Mapping(target = "externalClientSource", ignore=true)
    @Mapping(target = "serviceRecipient", ignore=true)
    @Mapping(target = "textMap", ignore=true)
    @Mapping(target = "choicesMap", ignore=true)
    void updateBuildingFromViewModel(BuildingCommandViewModel viewModel, @MappingTarget FixedContainer building);

    @AfterMapping
    default void mapChargeCategoryCombinations(BuildingCommandViewModel viewModel, @MappingTarget FixedContainer building) {
        AddedRemovedDto<BuildingCommandViewModel.ChargeCategoryCombination> combinationsChange = viewModel.chargeCategoryCombinations;

        if (combinationsChange != null) {
            // Get current combinations
            List<FixedContainer.ChargeCategoryInfo> currentCombinations = new ArrayList<>(building.getChargeCategoryCombinations());

            // Remove combinations that are in the removed list
            if (combinationsChange.removed != null && !combinationsChange.removed.isEmpty()) {
                currentCombinations.removeIf(existing ->
                    combinationsChange.removed.stream().anyMatch(toRemove ->
                        Objects.equals(existing.getChargeCategoryId(), toRemove.chargeCategoryId) &&
                        Objects.equals(existing.getChargeNameId(), toRemove.chargeNameId)
                    )
                );
            }

            // Add new combinations that are in the added list
            if (combinationsChange.added != null && !combinationsChange.added.isEmpty()) {
                List<FixedContainer.ChargeCategoryInfo> newCombinations = combinationsChange.added.stream()
                        .map(combo -> new FixedContainer.ChargeCategoryInfo(combo.chargeNameId, combo.chargeCategoryId))
                        .toList();
                currentCombinations.addAll(newCombinations);
            }

            // Set the updated combinations
            building.setChargeCategoryCombinations(currentCombinations);
        }
    }
}
