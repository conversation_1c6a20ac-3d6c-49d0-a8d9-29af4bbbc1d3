package com.ecco.infrastructure.hibernate;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.Map;

public class JSO<PERSON>lobMap<T> extends BaseJSONClob<Map<String, T>> {

    private final JavaType mapType;

    private Class<T> clazz;

    public JSONClobMap(ObjectMapper mapper, Class<T> clazz) {
        super(mapper);
        this.clazz = clazz;
        this.mapType = mapper.getTypeFactory()
                .constructMapType(HashMap.class, String.class, clazz);
    }

    @Override
    public JavaType getTargetType() {
        return mapType;
    }

    @Override
    public Class<T> getReturnedClass() {
        return this.clazz;
    }
}
