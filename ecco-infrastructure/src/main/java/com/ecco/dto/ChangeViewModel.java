package com.ecco.dto;

import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;


/**
 * Data-transfer object representing a change to a value.
 *
 * @param <T> The type of the value that has changed.
 */
@NullMarked
public class ChangeViewModel<T> {
    public @Nullable T from;

    public @Nullable T to;

    /**
     * Default constructor for Jackson deserialization.
     */
    public ChangeViewModel() {
    }

    @Nullable
    public static <S> ChangeViewModel<S> create(@Nullable S from, @Nullable S to) {
        if (from == null && to == null
                || from != null && from.equals(to)) {
            return null;
        }

        ChangeViewModel<S> vm = new ChangeViewModel<>();
        vm.from = from;
        vm.to = to;
        return vm;
    }

    public static <S> ChangeViewModel<S> changeNullTo(S to) {
        Assert.notNull(to, "cannot change from null to null");
        ChangeViewModel<S> vm = new ChangeViewModel<>();
        vm.to = to;
        return vm;
    }

    @Override
    public String toString() {
        return "[from=" + from + ", to=" + to + "]";
    }

}
